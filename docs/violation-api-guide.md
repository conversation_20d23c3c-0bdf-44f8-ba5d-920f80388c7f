# 违法违纪人员查询接口文档

## 概述

违法违纪人员查询接口提供了完整的违法违纪人员信息查询功能，涉及三个核心实体：

- **PoliceViolationPerson**: 违法违纪人员基本信息
- **PoliceViolationResult**: 违法违纪处罚结果
- **PoliceViolationSummary**: 违法违纪问题汇总

## 数据关联关系

```
PoliceViolationPerson (人员信息)
├── xxzjbh (人员信息主键编号)
└── wt_xxzjbh (问题信息主键编号)
    │
    ├── PoliceViolationResult (处理结果)
    │   └── ry_xxzjbh = person.xxzjbh
    │
    └── PoliceViolationSummary (问题汇总)
        └── xxzjbh = person.wt_xxzjbh
```

## 主要接口

### 1. 分页查询违法违纪人员综合信息

**接口地址**: `POST /violation/page`

**功能**: 查询违法违纪人员的完整信息，包括人员基本信息、问题汇总和处理结果

**请求参数**: `ViolationPersonQueryDTO`

```json
{
  "page": 1,
  "limit": 20,
  "name": "张三",
  "gender": "男",
  "caseOrg": "某派出所",
  "policeDept": "治安大队",
  "position": "民警",
  "rank": "四级警长",
  "politicalStatus": "中共党员",
  "isAccountability": "否",
  "dispositionCategory": "第一种形态",
  "birthDateStart": "1980-01-01",
  "birthDateEnd": "1990-12-31",
  "joinPartyDateStart": "2000-01-01",
  "joinPartyDateEnd": "2010-12-31",
  "clueSource": "群众举报",
  "violationType": "违法违纪",
  "caseNo": "CASE2024001",
  "cljg": "警告处分",
  "cldw": "纪委监委",
  "clsjStart": "2024-01-01",
  "clsjEnd": "2024-12-31",
  "keyword": "张三"
}
```

**响应数据**: `R<List<ViolationPersonVO>>`

```json
{
  "errno": 200,
  "msg": "success",
  "data": [
    {
      "personId": 1,
      "xxzjbh": "PERSON001",
      "wtXxzjbh": "PROBLEM001",
      "name": "张三",
      "gender": "男",
      "birthDate": "1980-01-01",
      "caseOrg": "某派出所",
      "policeDept": "治安大队",
      "position": "民警",
      "rank": "四级警长",
      "politicalStatus": "中共党员",
      "joinPartyDate": "2005-07-01",
      "isAccountability": "否",
      "dispositionCategory": "第一种形态",
      "remark": "备注信息",
      "reportOrg": "市公安局",
      "clueSource": "群众举报",
      "clueContent": "违规执法",
      "foundDate": "2024-01-01",
      "foundOrg": "监察部门",
      "violationType": "违法违纪",
      "caseNo": "CASE2024001",
      "results": [
        {
          "resultId": 1,
          "lbmc": "党纪处分",
          "clsj": "2024-03-01",
          "cldw": "纪委监委",
          "cljg": "警告处分"
        }
      ],
      "createdAt": "2024-01-01 10:00:00",
      "updatedAt": "2024-01-01 10:00:00"
    }
  ],
  "total": 1
}
```

### 2. 查询违法违纪人员详情

**接口地址**: `GET /violation/detail/{personId}`

**功能**: 根据人员ID查询完整的违法违纪信息

**路径参数**: 
- `personId`: 人员主键ID

**响应数据**: `R<ViolationPersonVO>`

### 3. 其他辅助接口

#### 3.1 查询人员基本信息列表
- **接口**: `POST /violation/persons`
- **功能**: 只查询人员基本信息，不包含关联数据

#### 3.2 查询问题汇总列表
- **接口**: `POST /violation/summaries`
- **功能**: 查询违法违纪问题汇总信息

#### 3.3 查询处理结果列表
- **接口**: `POST /violation/results`
- **功能**: 查询违法违纪处理结果信息

#### 3.4 根据人员xxzjbh查询处理结果
- **接口**: `GET /violation/results/person/{xxzjbh}`
- **功能**: 查询指定人员的所有处理结果

#### 3.5 根据问题xxzjbh查询问题汇总
- **接口**: `GET /violation/summary/{xxzjbh}`
- **功能**: 查询指定问题的汇总信息

#### 3.6 统计违法违纪人员数量
- **接口**: `POST /violation/count`
- **功能**: 根据查询条件统计人员数量

## 查询条件说明

### 基本查询条件
- `name`: 姓名（模糊查询）
- `gender`: 性别（精确匹配）
- `caseOrg`: 立案时所在单位（模糊查询）
- `policeDept`: 警种部门（模糊查询）
- `position`: 职务（模糊查询）
- `rank`: 职级（精确匹配）
- `politicalStatus`: 政治面貌（精确匹配）
- `isAccountability`: 是否系被倒查问责（精确匹配）
- `dispositionCategory`: 四种形态处理归类（精确匹配）

### 日期范围查询
- `birthDateStart/birthDateEnd`: 出生日期范围
- `joinPartyDateStart/joinPartyDateEnd`: 入党时间范围
- `clsjStart/clsjEnd`: 处理时间范围

### 关联信息查询
- `clueSource`: 问题线索来源
- `violationType`: 违规违纪类型
- `caseNo`: 案件编号
- `cljg`: 处理结果
- `cldw`: 处理单位

### 关键字搜索
- `keyword`: 在姓名、单位、部门、职务中进行模糊搜索

## 使用示例

### 查询某个单位的违法违纪人员
```json
{
  "page": 1,
  "limit": 20,
  "caseOrg": "某派出所"
}
```

### 查询特定时间段的处理结果
```json
{
  "page": 1,
  "limit": 20,
  "clsjStart": "2024-01-01",
  "clsjEnd": "2024-12-31"
}
```

### 关键字搜索
```json
{
  "page": 1,
  "limit": 20,
  "keyword": "张三"
}
```

## 注意事项

1. **数据关联**: 系统会自动根据 `xxzjbh` 和 `wt_xxzjbh` 字段进行数据关联
2. **分页查询**: 所有列表接口都支持分页，默认每页20条记录
3. **排序规则**: 默认按创建时间倒序排列
4. **数据完整性**: 如果关联数据不存在，对应字段将为空，不会影响主要数据的返回
5. **性能优化**: 使用批量查询减少数据库访问次数

## 错误处理

接口会返回标准的错误响应格式：

```json
{
  "errno": 500,
  "msg": "查询失败：具体错误信息",
  "data": null
}
```

常见错误：
- 参数验证失败
- 数据库连接异常
- 数据不存在
- 权限不足
