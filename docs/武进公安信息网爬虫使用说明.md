# 武进公安信息网爬虫使用说明

## 概述

本爬虫用于爬取武进公安信息网（http://************）的砺警学堂数据，支持爬取新闻列表和详情页面，包括附件信息。

## 功能特性

1. **自动获取总页数**：爬虫会自动解析页面获取总页数信息
2. **全量数据爬取**：支持爬取所有页面的数据
3. **单页数据爬取**：支持爬取指定页面的数据
4. **智能解析**：自动解析新闻标题、链接、发布时间等信息
5. **详情页面爬取**：支持爬取每条新闻的详细内容
6. **附件信息提取**：自动提取新闻详情页面中的附件信息
7. **多种文件类型支持**：支持PDF、Word、Excel、PowerPoint等多种附件类型识别

## 技术实现

### 依赖库
- **Jsoup 1.17.2**：用于HTML解析
- **Hutool 5.8.25**：用于HTTP请求和工具类
- **Spring Boot**：框架支持

### 核心类

1. **FjWebSiteSpiderService**：爬虫核心服务类
2. **WjPoliceNewsDto**：新闻数据传输对象
3. **WjPoliceNewsDetailDto**：新闻详情数据传输对象
4. **WjPoliceNewsAttachmentDto**：新闻附件数据传输对象
5. **WjPoliceSpiderResult**：爬虫结果封装类
6. **WjPoliceSpiderController**：REST API控制器

## API接口

### 1. 爬取所有页面数据
```
POST /api/spider/wj-police/crawl-all
参数：
- baseUrl (可选): 基础URL，为空时使用默认URL
- includeDetail (默认false): 是否包含详情信息
```

### 2. 爬取指定页面数据
```
POST /api/spider/wj-police/crawl-page
参数：
- baseUrl (可选): 基础URL，为空时使用默认URL
- page (默认1): 页码
- includeDetail (默认false): 是否包含详情信息
```

### 3. 爬取指定新闻详情
```
GET /api/spider/wj-police/news-detail/{newsId}
参数：
- newsId: 新闻ID
```

### 4. 获取第八次"登锋"砺警学堂信息
```
GET /api/spider/wj-police/eighth-dengfeng
参数：
- baseUrl (可选): 基础URL，为空时使用默认URL
```

## 数据结构

### WjPoliceNewsDto
```json
{
  "title": "新闻标题",
  "url": "新闻链接",
  "publishDate": "2024-04-07",
  "pageNumber": 1,
  "containsDengfeng": true,
  "newsId": "66028",
  "detail": {...}
}
```

### WjPoliceNewsDetailDto
```json
{
  "newsId": "66028",
  "title": "第八次"登锋"砺警学堂（情指中心4月2日）——《重大敏感警情的指挥调度和综合指挥室建设》",
  "detailUrl": "http://************/articleshow.asp?id=66028",
  "publishTime": "2024-04-07T14:52:33",
  "viewCount": 122,
  "content": "<table>...</table>",
  "textContent": "纯文本内容",
  "attachments": [...],
  "success": true,
  "errorMessage": null
}
```

### WjPoliceNewsAttachmentDto
```json
{
  "fileName": "重大敏感警情的指挥调度和综合指挥室建设.pptx",
  "downloadUrl": "http://************/manager1/eweb/uploadfile/20240407145229591.pptx",
  "fileType": "powerpoint",
  "fileSize": null,
  "iconUrl": "./武进公安局_files/unknow.gif"
}
```

### WjPoliceSpiderResult
```json
{
  "totalPages": 3,
  "currentPage": 1,
  "totalRecords": 38,
  "newsList": [...],
  "success": true,
  "errorMessage": null,
  "eighthDengfengNews": {...}
}
```

## 使用示例

### 1. 爬取所有数据（仅列表）
```bash
curl -X POST "http://localhost:8080/api/spider/wj-police/crawl-all"
```

### 2. 爬取所有数据（包含详情）
```bash
curl -X POST "http://localhost:8080/api/spider/wj-police/crawl-all?includeDetail=true"
```

### 3. 爬取第3页数据（包含详情）
```bash
curl -X POST "http://localhost:8080/api/spider/wj-police/crawl-page?page=3&includeDetail=true"
```

### 4. 爬取指定新闻详情
```bash
curl -X GET "http://localhost:8080/api/spider/wj-police/news-detail/66028"
```

### 5. 查找第八次登锋砺警学堂
```bash
curl -X GET "http://localhost:8080/api/spider/wj-police/eighth-dengfeng"
```

## 配置说明

### 默认配置
- 列表页基础URL：`http://************/ArticleList.asp?mode=%ED%C2%BE%AF%D1%A7%CC%C3`
- 详情页基础URL：`http://************/articleshow.asp?id=`
- HTTP超时：30秒
- 编码格式：GBK

### 自定义配置
可以通过传入baseUrl参数来使用自定义的URL。

## 附件类型支持

爬虫支持识别以下文件类型：
- **PDF文件**：.pdf
- **Word文档**：.doc, .docx
- **Excel表格**：.xls, .xlsx
- **PowerPoint演示文稿**：.ppt, .pptx
- **文本文件**：.txt
- **图片文件**：.jpg, .jpeg, .png, .gif
- **压缩文件**：.zip, .rar, .7z
- **其他类型**：unknown

## 注意事项

1. **网络环境**：确保能够访问武进公安信息网
2. **请求频率**：建议控制请求频率，避免对目标网站造成压力
3. **数据时效性**：爬取的数据可能会随着网站更新而变化
4. **错误处理**：爬虫包含完善的错误处理机制，会返回详细的错误信息

## 测试

项目包含测试类 `FjWebSiteSpiderServiceTest`，可以运行测试来验证爬虫功能：

```bash
mvn test -Dtest=FjWebSiteSpiderServiceTest
```

## 日志

爬虫运行过程中会输出详细的日志信息，包括：
- 请求URL
- 解析进度
- 数据统计
- 错误信息

## 扩展性

爬虫设计具有良好的扩展性，可以轻松添加：
- 新的数据字段解析
- 不同的过滤条件
- 数据存储功能
- 定时任务支持
