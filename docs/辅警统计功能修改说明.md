# 辅警统计功能修改说明

## 概述
本次修改为辅警统计功能创建了专门的DTO类，并按照新的学历分类标准重新设计了统计和穿透查询功能。

## 主要修改内容

### 1. 新增AuxiliaryPoliceStatisticsDTO类
- **文件位置**: `src/main/java/com/hl/archive/domain/dto/AuxiliaryPoliceStatisticsDTO.java`
- **功能**: 专门用于辅警统计的返回DTO
- **学历分类**: 
  - `eduJuniorAndBelow`: 初中及以下学历人数
  - `eduTechnical`: 中专学历人数
  - `eduHighSchool`: 高中学历人数
  - `eduCollege`: 大专学历人数
  - `eduBachelorAndAbove`: 本科及以上学历人数

### 2. 修改Mapper接口
- **文件位置**: `src/main/java/com/hl/archive/mapper/AuxiliaryPoliceInfoMapper.java`
- **修改内容**: 
  - 更新方法返回类型为`AuxiliaryPoliceStatisticsDTO`
  - 新增按部门统计方法

### 3. 修改XML映射文件
- **文件位置**: `src/main/resources/mapper/AuxiliaryPoliceInfoMapper.xml`
- **修改内容**:
  - 新增`AuxiliaryPoliceStatisticsDTOMap` ResultMap
  - 更新SQL查询以支持新的学历分类统计
  - 更新穿透查询以支持新的学历分类条件

### 4. 修改Service层
- **文件位置**: `src/main/java/com/hl/archive/service/AuxiliaryPoliceInfoService.java`
- **修改内容**:
  - 更新方法返回类型
  - 新增按部门统计方法

### 5. 修改Controller层
- **文件位置**: `src/main/java/com/hl/archive/controller/AuxiliaryPoliceInfoController.java`
- **修改内容**:
  - 更新接口返回类型
  - 新增按部门统计接口

### 6. 更新请求参数说明
- **文件位置**: `src/main/java/com/hl/archive/domain/request/StatisticsDrillDownRequest.java`
- **修改内容**: 更新注释，添加辅警学历分类的说明

## API接口说明

### 1. 按部门统计辅警信息
- **接口**: `POST /auxiliaryPoliceInfo/statisticsByDepartment`
- **返回**: `List<AuxiliaryPoliceStatisticsDTO>`
- **功能**: 按部门统计所有辅警的人数、年龄段、学历分布等信息

### 2. 按组织ID统计辅警信息
- **接口**: `POST /auxiliaryPoliceInfo/statisticsPoliceByOrgId`
- **参数**: `StatisticsQueryRequest`
- **返回**: `AuxiliaryPoliceStatisticsDTO`
- **功能**: 按指定组织ID统计辅警信息

### 3. 辅警统计穿透查询
- **接口**: `POST /auxiliaryPoliceInfo/statisticsDrillDown`
- **参数**: `StatisticsDrillDownRequest`
- **返回**: `List<AuxiliaryPoliceInfo>`
- **功能**: 根据统计类型查询具体的辅警人员列表

## 学历分类映射

### 辅警学历分类标准
1. **初中及以下** (`edu_junior_and_below`): 包含初中、小学、文盲或空值
2. **中专** (`edu_technical`): 包含中专、技校
3. **高中** (`edu_high_school`): 包含高中
4. **大专** (`edu_college`): 包含专科、大专
5. **本科及以上** (`edu_bachelor_and_above`): 包含本科、大学、硕士、博士

### 穿透查询支持的统计类型
- 年龄段: `age_20_30`, `age_30_40`, `age_40_50`, `age_50_up`
- 学历: `edu_junior_and_below`, `edu_technical`, `edu_high_school`, `edu_college`, `edu_bachelor_and_above`
- 性别: `maleCount`, `femaleCount`
- 在岗状态: `onDutyCount`, `offDutyCount`

## 测试说明
- **测试文件**: `src/test/java/com/hl/archive/service/AuxiliaryPoliceStatisticsTest.java`
- **测试内容**: 
  - 按部门统计功能测试
  - 按组织ID统计功能测试
  - 穿透查询功能测试（按学历、年龄、性别）

## 注意事项
1. 新的学历分类仅适用于辅警统计，民警统计仍使用原有的分类标准
2. 来源统计字段（转业军人、警校毕业等）在辅警表中暂时设为0，如需要可后续扩展
3. 穿透查询同时支持新旧学历分类的统计类型，保证向后兼容性
4. 建议在生产环境部署前先运行测试用例验证功能正确性

## 后续扩展建议
1. 如果辅警表中有来源信息字段，可以更新SQL查询以提供真实的来源统计
2. 可以考虑为辅警添加更多维度的统计，如工作年限、岗位类型等
3. 可以优化SQL查询性能，添加适当的索引
