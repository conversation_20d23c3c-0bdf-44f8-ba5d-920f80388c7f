2025-09-04 11:27:09.485 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-04 11:27:09.491 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-04 11:27:09.495 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-04 11:27:12.035 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.035 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.037 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:18.716 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-04 11:27:18.717 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.728 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-04 11:27:18.728 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.739 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-04 11:27:18.740 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.752 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-04 11:27:18.752 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.764 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-04 11:27:18.764 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.775 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-04 11:27:18.775 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.786 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-04 11:27:18.786 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.797 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-04 11:27:18.797 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.807 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-04 11:27:18.807 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.819 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-04 11:27:18.819 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.835 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-04 11:27:18.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.851 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-04 11:27:18.851 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.863 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-04 11:27:18.883 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-04 11:27:18.883 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.897 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-04 11:27:18.897 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.909 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-04 11:27:18.909 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.921 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-04 11:27:18.922 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.937 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-04 11:27:18.938 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.953 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-04 11:27:18.954 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.966 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-04 11:27:18.967 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.978 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-04 11:27:18.979 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.989 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-04 11:27:18.990 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.000 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-04 11:27:19.000 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.010 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-04 11:27:19.010 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.027 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-04 11:27:19.028 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.046 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-04 11:27:19.046 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.057 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-04 11:27:19.057 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.066 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-04 11:27:19.066 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.090 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-04 11:27:19.090 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.100 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-04 11:27:19.101 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.111 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-04 11:27:19.111 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.122 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-04 11:27:19.122 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.137 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-04 11:27:19.137 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.147 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-04 11:27:19.148 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.163 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-04 11:27:19.163 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.177 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-04 11:27:19.178 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.192 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-04 11:27:19.193 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.206 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-04 11:27:19.206 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.221 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-04 11:27:19.223 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.236 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-04 11:27:19.237 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.247 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-04 11:27:19.248 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.260 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-04 11:27:19.261 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.273 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-04 11:27:19.273 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.285 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-04 11:27:19.286 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.297 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-04 11:27:19.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.307 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-04 11:27:19.308 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.319 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-04 11:27:19.319 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.330 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-04 11:27:19.331 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.346 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-04 11:27:19.346 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.374 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-04 11:27:19.374 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-04 11:27:19.400 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.413 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-04 11:27:19.413 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.428 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-04 11:27:19.429 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:31.451 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:31.453 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:34.543 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:34.543 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:37.630 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:37.631 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:40.709 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:40.710 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:43.811 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:43.813 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:46.905 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:46.906 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:50.008 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:50.009 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:53.109 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:53.109 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:56.458 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:56.459 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:59.620 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:59.620 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:28:28.981 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor68.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-04 11:28:55.202 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 26170ms to respond
[m2025-09-04 11:29:20.216 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource1DataSource) took 25013ms to respond
[m2025-09-04 11:29:45.142 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource2DataSource) took 24926ms to respond
[m2025-09-04 13:27:49.209 [33mWARN [m [35m[Thread-13][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-04 13:27:49.209 [33mWARN [m [35m[Thread-8][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-04 13:27:49.209 [33mWARN [m [35m[Thread-13][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-04 13:27:49.210 [33mWARN [m [35m[Thread-8][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-04 17:54:17.748 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-04 17:54:17.755 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-04 17:54:17.759 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-04 17:54:21.404 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.404 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.404 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.406 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.406 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:29.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-04 17:54:29.320 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.331 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-04 17:54:29.332 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.343 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-04 17:54:29.343 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.354 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-04 17:54:29.354 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.365 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-04 17:54:29.365 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.377 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-04 17:54:29.377 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.388 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-04 17:54:29.389 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-04 17:54:29.399 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.411 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-04 17:54:29.411 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.423 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-04 17:54:29.423 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.435 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-04 17:54:29.435 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.447 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-04 17:54:29.447 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.459 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-04 17:54:29.476 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-04 17:54:29.477 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.491 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-04 17:54:29.491 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.502 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-04 17:54:29.502 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.516 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-04 17:54:29.516 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.531 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-04 17:54:29.531 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.545 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-04 17:54:29.545 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.573 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-04 17:54:29.574 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.587 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-04 17:54:29.588 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.600 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-04 17:54:29.601 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.611 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-04 17:54:29.611 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.623 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-04 17:54:29.623 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.641 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-04 17:54:29.642 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.660 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-04 17:54:29.660 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.671 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-04 17:54:29.671 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.681 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-04 17:54:29.682 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.694 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-04 17:54:29.694 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.704 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-04 17:54:29.704 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.715 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-04 17:54:29.716 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.726 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-04 17:54:29.727 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.737 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-04 17:54:29.737 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.749 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-04 17:54:29.749 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.761 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-04 17:54:29.761 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.771 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-04 17:54:29.771 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.784 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-04 17:54:29.784 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.794 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-04 17:54:29.794 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.809 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-04 17:54:29.810 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.825 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-04 17:54:29.825 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-04 17:54:29.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.848 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-04 17:54:29.848 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.860 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-04 17:54:29.861 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.873 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-04 17:54:29.873 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.885 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-04 17:54:29.886 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.896 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-04 17:54:29.896 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.906 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-04 17:54:29.907 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.920 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-04 17:54:29.920 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.934 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-04 17:54:29.935 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.954 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-04 17:54:29.954 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.971 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-04 17:54:29.972 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.984 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-04 17:54:29.985 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.997 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-04 17:54:29.998 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:57.533 [33mWARN [m [35m[main][m [36mo.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext[m [34m[][m - [33mException encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'auxiliaryPoliceInfoController' defined in file [D:\work\code\hl-wj-police-archive\target\classes\com\hl\archive\controller\AuxiliaryPoliceInfoController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'auxiliaryPoliceInfoService': Invocation of init method failed; nested exception is java.lang.NullPointerException
[m2025-09-04 17:54:57.542 [33mWARN [m [35m[main][m [36mo.s.c.a.AnnotationConfigApplicationContext[m [34m[][m - [33mException thrown from ApplicationListener handling ContextClosedEvent
[m org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'rabbitConnectionFactory': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:264) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:221) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:140) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.5.jar:3.1.5]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:604) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) ~[spring-boot-2.7.18.jar:2.7.18]
	at com.hl.AppMain.main(AppMain.java:32) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
2025-09-04 17:54:57.803 [31mERROR[m [35m[main][m [36mo.s.b.SpringApplication[m [34m[][m - [31mApplication run failed
[m org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'auxiliaryPoliceInfoController' defined in file [D:\work\code\hl-wj-police-archive\target\classes\com\hl\archive\controller\AuxiliaryPoliceInfoController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'auxiliaryPoliceInfoService': Invocation of init method failed; nested exception is java.lang.NullPointerException
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) ~[spring-boot-2.7.18.jar:2.7.18]
	at com.hl.AppMain.main(AppMain.java:32) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'auxiliaryPoliceInfoService': Invocation of init method failed; nested exception is java.lang.NullPointerException
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:160) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 18 more
Caused by: java.lang.NullPointerException
	at com.hl.archive.utils.SsoCacheUtil.getCacheMap(SsoCacheUtil.java:20) ~[classes/:?]
	at com.hl.archive.utils.SsoCacheUtil.getOrganizationIdByNameWithLike(SsoCacheUtil.java:75) ~[classes/:?]
	at com.hl.archive.service.AuxiliaryPoliceInfoService.cleanAuxiliaryPoliceInfo(AuxiliaryPoliceInfoService.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 18 more
2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-11][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-17][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-17][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-11][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-04 17:58:16.611 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-04 17:58:16.617 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-04 17:58:16.621 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-04 17:58:19.807 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.808 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.808 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.809 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.809 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.809 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:27.285 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-04 17:58:27.285 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.294 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-04 17:58:27.296 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.306 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-04 17:58:27.307 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-04 17:58:27.318 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.329 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-04 17:58:27.329 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.340 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-04 17:58:27.340 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.351 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-04 17:58:27.352 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-04 17:58:27.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.373 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-04 17:58:27.373 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.383 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-04 17:58:27.383 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.395 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-04 17:58:27.395 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.408 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-04 17:58:27.408 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.419 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-04 17:58:27.436 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-04 17:58:27.437 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-04 17:58:27.449 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.460 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-04 17:58:27.461 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.473 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-04 17:58:27.473 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.484 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-04 17:58:27.485 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.496 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-04 17:58:27.496 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.510 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-04 17:58:27.510 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.525 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-04 17:58:27.526 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.539 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-04 17:58:27.540 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.549 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-04 17:58:27.549 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.559 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-04 17:58:27.559 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.579 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-04 17:58:27.579 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.595 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-04 17:58:27.595 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.607 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-04 17:58:27.607 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.616 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-04 17:58:27.617 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.628 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-04 17:58:27.629 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.638 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-04 17:58:27.639 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.649 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-04 17:58:27.649 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.659 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-04 17:58:27.660 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.671 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-04 17:58:27.671 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.680 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-04 17:58:27.681 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.691 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-04 17:58:27.691 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.701 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-04 17:58:27.702 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.712 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-04 17:58:27.712 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.724 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-04 17:58:27.724 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.740 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-04 17:58:27.741 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.755 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-04 17:58:27.755 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.767 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-04 17:58:27.768 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.780 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-04 17:58:27.780 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.792 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-04 17:58:27.793 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.804 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-04 17:58:27.804 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.815 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-04 17:58:27.816 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.826 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-04 17:58:27.827 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.837 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-04 17:58:27.837 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.848 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-04 17:58:27.849 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.865 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-04 17:58:27.865 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.882 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-04 17:58:27.882 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.898 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-04 17:58:27.899 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.918 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-04 17:58:27.919 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.930 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-04 17:58:27.930 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:42.578 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:42.578 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:45.684 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:45.685 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:48.779 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:48.780 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:51.882 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:51.882 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:54.977 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:54.978 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:58.082 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:58.082 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:01.169 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:01.169 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:04.254 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:04.254 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:07.633 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:07.633 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:10.784 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:10.784 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:45.163 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor69.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-04 18:00:12.040 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 26828ms to respond
[m2025-09-04 18:00:36.927 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource1DataSource) took 24887ms to respond
[m2025-09-04 18:01:01.800 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource2DataSource) took 24873ms to respond
[m2025-09-04 19:04:12.724 [33mWARN [m [35m[Thread-12][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-04 19:04:12.725 [33mWARN [m [35m[Thread-18][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-04 19:04:12.728 [33mWARN [m [35m[Thread-18][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-04 19:04:12.732 [33mWARN [m [35m[Thread-12][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-05 08:53:44.610 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-05 08:53:44.618 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-05 08:53:44.622 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-05 08:53:48.213 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.213 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.215 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.215 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.215 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.216 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:55.937 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-05 08:53:55.937 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.949 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-05 08:53:55.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.960 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-05 08:53:55.960 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.971 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-05 08:53:55.971 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.982 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-05 08:53:55.983 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.993 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-05 08:53:55.993 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.004 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-05 08:53:56.004 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.015 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-05 08:53:56.015 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.025 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-05 08:53:56.025 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.036 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-05 08:53:56.037 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.049 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-05 08:53:56.050 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.060 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-05 08:53:56.061 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.072 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-05 08:53:56.090 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-05 08:53:56.091 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.104 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-05 08:53:56.105 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.115 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-05 08:53:56.116 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.127 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-05 08:53:56.127 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.139 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-05 08:53:56.139 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.152 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-05 08:53:56.153 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.166 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-05 08:53:56.166 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.178 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-05 08:53:56.178 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.190 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-05 08:53:56.190 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.200 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-05 08:53:56.200 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.210 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-05 08:53:56.210 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.228 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-05 08:53:56.228 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.244 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-05 08:53:56.244 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.255 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-05 08:53:56.256 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.266 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-05 08:53:56.266 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.277 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-05 08:53:56.277 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.288 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-05 08:53:56.289 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.299 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-05 08:53:56.299 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.309 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-05 08:53:56.309 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-05 08:53:56.319 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.329 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-05 08:53:56.329 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.339 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-05 08:53:56.340 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.351 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-05 08:53:56.351 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-05 08:53:56.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.372 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-05 08:53:56.372 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.386 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-05 08:53:56.387 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.400 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-05 08:53:56.400 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.411 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-05 08:53:56.412 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.423 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-05 08:53:56.425 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.437 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-05 08:53:56.437 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-05 08:53:56.449 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.460 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-05 08:53:56.460 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.469 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-05 08:53:56.470 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.480 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-05 08:53:56.480 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.491 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-05 08:53:56.492 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.506 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-05 08:53:56.507 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.523 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-05 08:53:56.523 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.540 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-05 08:53:56.540 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.553 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-05 08:53:56.553 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.564 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-05 08:53:56.564 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:54:11.341 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:11.341 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:14.424 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:14.424 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:17.508 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:17.510 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:20.603 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:20.604 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:23.710 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:23.710 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:26.800 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:26.800 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:29.907 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:29.907 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:33.002 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:33.003 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:36.386 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:36.387 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:39.542 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:39.544 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:55:18.626 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor66.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-05 08:55:45.079 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 26418ms to respond
[m2025-09-05 08:56:10.035 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource1DataSource) took 24956ms to respond
[m2025-09-05 08:56:34.923 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource2DataSource) took 24887ms to respond
[m2025-09-05 09:02:34.930 [31mERROR[m [35m[http-nio-28183-exec-1][m [36mc.h.c.c.e.GlobalExceptionHandler[m [34m[][m - [31m/auxiliaryPoliceInfo/statisticsPoliceByOrgId --> 
[m org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.hl.common.domain.R<com.hl.archive.domain.dto.PoliceStatisticsDTO> com.hl.archive.controller.AuxiliaryPoliceInfoController.statisticsPoliceByOrgId(com.hl.archive.domain.request.StatisticsQueryRequest)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:163) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
2025-09-05 09:05:12.157 [33mWARN [m [35m[Thread-20][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-05 09:05:12.158 [33mWARN [m [35m[Thread-20][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-05 09:05:12.157 [33mWARN [m [35m[Thread-14][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-05 09:05:12.159 [33mWARN [m [35m[Thread-14][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m