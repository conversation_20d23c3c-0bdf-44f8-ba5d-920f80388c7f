package com.hl.archive.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.common.domain.R;
import com.hl.dict.entity.DictData;
import com.hl.dict.service.DictDataService;
import com.hl.security.utils.PassToken;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/dict-test")
@RequiredArgsConstructor
public class DictImportTest {

    private final DictDataService dictDataService;

    @GetMapping("/import-one")
    public R<?> importDict(){
        Set<String> one = new HashSet<>();
        ExcelReader reader = ExcelUtil.getReader("D:\\work\\code\\hl-wj-police-archive\\sql\\222.xlsx");
        List<List<Object>> read = reader.setSheet(3).read(2);
        for (List<Object> objects : read) {
            one.add((String) objects.get(1));
        }

        for (String s : one) {
            DictData dictData = new DictData();
            dictData.setIsSystem(1);
            dictData.setDictName(s);
            dictData.setParentId(Long.valueOf("1943153681746665473"));
            dictData.setDictType("ability_label");
            dictData.setDictValue(IdUtil.fastSimpleUUID());
            dictDataService.addDict(dictData);
        }
        return  R.ok(one);
    }


    @GetMapping("/import-two")
    public R<?> importDictTwo(){
        Set<String> dic = new HashSet<>();
        ExcelReader reader = ExcelUtil.getReader("D:\\work\\code\\hl-wj-police-archive\\sql\\222.xlsx");
        List<List<Object>> read = reader.setSheet(3).read(2);
        for (List<Object> objects : read) {
            String s1 = (String) objects.get(2);

            if (dic.contains(s1)) {
                continue;
            }
            String s = (String) objects.get(1);
            // 查询
            DictData abilityLabel = dictDataService.getOne(Wrappers.<DictData>lambdaQuery()
                    .eq(DictData::getDictName, s)
                    .eq(DictData::getDictType, "ability_label")
                    .eq(DictData::getParentId, Long.valueOf("1943153681746665473"))
                    .last(" limit 1"));
            if (abilityLabel != null) {
                DictData dictData = new DictData();
                dictData.setIsSystem(1);
                dictData.setDictName(s1);
                dictData.setParentId(abilityLabel.getId());
                dictData.setDictType("ability_label");
                dictData.setDictValue(IdUtil.fastSimpleUUID());
                dictDataService.addDict(dictData);
                dic.add(s1);
            }
        }

        return  R.ok(dic);
    }

    @GetMapping("/import-three")
    public R<?> importDictThree(){
        Set<String> dic = new HashSet<>();
        ExcelReader reader = ExcelUtil.getReader("D:\\work\\code\\hl-wj-police-archive\\sql\\222.xlsx");
        List<List<Object>> read = reader.setSheet(3).read(2);
        for (List<Object> objects : read) {
            String s1 = (String) objects.get(3);

            if (dic.contains(s1)) {
                continue;
            }
            String s = (String) objects.get(2);
            // 查询
            DictData abilityLabel = dictDataService.getOne(Wrappers.<DictData>lambdaQuery()
                    .eq(DictData::getDictName, s)
                    .eq(DictData::getDictType, "ability_label")
                    .last(" limit 1"));
            if (abilityLabel != null) {
                DictData dictData = new DictData();
                dictData.setIsSystem(1);
                dictData.setDictName(s1);
                dictData.setParentId(abilityLabel.getId());
                dictData.setDictType("ability_label");
                dictData.setDictValue(IdUtil.fastSimpleUUID());
                JSONObject object = new JSONObject();
                object.put("explain",objects.get(4));
                object.put("require",objects.get(5));

                dictData.setExtend(object);
                dictDataService.addDict(dictData);
                dic.add(s1);
            }
        }

        return  R.ok(dic);
    }



    @PostMapping("/test")
    @ApiOperation("测试")
    @PassToken
    public R<?> test(@RequestBody JSONObject param){
        return  R.ok("ok");
    }



}
