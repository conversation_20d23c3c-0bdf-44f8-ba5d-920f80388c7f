package com.hl.archive.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.service.PoliceAbilityTagService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 民警职业能力标签控制器
 */
@RestController
@RequestMapping("/abilityTag")
@Api(tags = "民警职业能力标签")
@RequiredArgsConstructor
public class PoliceAbilityTagController {

    private final PoliceAbilityTagService policeAbilityTagService;

    @PostMapping("/pageAbilityTag")
    @ApiOperation(value = "查询民警职业能力标签列表")
    public R<List<PoliceAbilityTag>> pageAbilityTag(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceAbilityTag> page = policeAbilityTagService.page(
                Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceAbilityTag>lambdaQuery()
                        .like(StringUtils.isNotBlank(request.getIdCard()), PoliceAbilityTag::getIdCard, request.getIdCard())
                        .and(StringUtils.isNotBlank(request.getQuery()), i -> i.like(PoliceAbilityTag::getAbilityTagName, request.getQuery())
                                .or()
                                .like(PoliceAbilityTag::getDescription, request.getQuery()))
                        .orderByDesc(PoliceAbilityTag::getObtainTime)
        );
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/addAbilityTag")
    @ApiOperation("添加民警职业能力标签")
    public R<Boolean> addAbilityTag(@RequestBody PoliceAbilityTag request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeAbilityTagService.save(request));
    }

    @PostMapping("/updateAbilityTag")
    @ApiOperation("更新民警职业能力标签")
    public R<Boolean> updateAbilityTag(@RequestBody PoliceAbilityTag request) {
        // 设置更新时间
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeAbilityTagService.updateById(request));
    }

    @PostMapping("/deleteAbilityTag")
    @ApiOperation("删除民警职业能力标签")
    public R<Boolean> deleteAbilityTag(@RequestBody PoliceAbilityTag request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeAbilityTagService.removeById(request.getId()));
    }


}
