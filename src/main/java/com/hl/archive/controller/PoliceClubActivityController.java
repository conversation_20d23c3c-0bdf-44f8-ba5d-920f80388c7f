package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceClubActivityApproveTaskDTO;
import com.hl.archive.domain.dto.PoliceClubActivityCreateTaskDTO;
import com.hl.archive.domain.dto.PoliceClubActivityEnrollQueryDTO;
import com.hl.archive.domain.dto.PoliceClubActivityQueryDTO;
import com.hl.archive.domain.entity.PoliceClubActivity;
import com.hl.archive.domain.entity.PoliceClubActivityEnroll;
import com.hl.archive.service.PoliceClubActivityService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/clubActivity")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "社团活动")
public class PoliceClubActivityController {

    private final PoliceClubActivityService policeClubActivityService;


    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceClubActivity>> page(@RequestBody PoliceClubActivityQueryDTO requestDTO) {
        Page<PoliceClubActivity> page = policeClubActivityService.pageList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/queryDetail")
    @ApiOperation("详情")
    public R<PoliceClubActivity> queryDetail(@RequestBody PoliceClubActivity request) {
        return R.ok(policeClubActivityService.queryDetail(request));
    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody PoliceClubActivity request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubActivityService.save(request));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceClubActivity request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubActivityService.updateById(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody PoliceClubActivity request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubActivityService.removeById(request));
    }

    @PostMapping("/createActivityTask")
    @ApiOperation("创建社团活动任务")
    public R<?> createActivityTask(@RequestBody PoliceClubActivityCreateTaskDTO request) {
        R<?> r = policeClubActivityService.createActivityTask(request);
        return r;
    }

    @PostMapping("/approveActivityTask")
    @ApiOperation("审核社团活动任务")
    public R<?> approveActivityTask(@RequestBody PoliceClubActivityApproveTaskDTO request) {
        R<?> r = policeClubActivityService.approveActivityTask(request);
        return r;
    }


    @PostMapping("/pageActivityEnroll")
    @ApiOperation("查询活动报名")
    public R<List<PoliceClubActivityEnroll>> pageActivityEnroll(@RequestBody PoliceClubActivityEnrollQueryDTO requestDTO) {
        Page<PoliceClubActivityEnroll> page = policeClubActivityService.pageEnrollList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/enrollPermission")
    @ApiOperation("根据活动检测是否能够报名")
    public R<Boolean> enrollPermission(@RequestBody PoliceClubActivityEnroll request) {
        boolean b = policeClubActivityService.enrollPermission(request);
        return R.ok(b);
    }

    @PostMapping("/enroll")
    @ApiOperation("报名")
    public R<Boolean> enroll(@RequestBody PoliceClubActivityEnroll request) {
        return R.ok(policeClubActivityService.saveEnroll(request));
    }

    @PostMapping("/approveEnroll")
    @ApiOperation("审核报名 更新 status ")
    public R<Boolean> approveEnroll(@RequestBody PoliceClubActivityEnroll request) {
        return R.ok(policeClubActivityService.approveEnroll(request));
    }


}
