package com.hl.archive.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.service.PoliceDrinkReportService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/drinkReport")
@RequiredArgsConstructor
@Api(tags = "饮酒报备")
public class PoliceDrinkReportController {

    private final PoliceDrinkReportService policeDrinkReportService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceDrinkReport>> page(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceDrinkReport> page = policeDrinkReportService.page(Page.of(request.getPage(), request.getLimit()), Wrappers.<PoliceDrinkReport>lambdaQuery()
                .eq(PoliceDrinkReport::getIdCard, request.getIdCard())
                .orderByDesc(PoliceDrinkReport::getDrinkTime));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }
}
