package com.hl.archive.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceHeartToHeartQueryDTO;
import com.hl.archive.domain.entity.PoliceHeartToHeartVisit;
import com.hl.archive.service.PoliceHeartToHeartVisitService;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import com.hl.security.User;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/heartToHeartVisit")
@Api(tags = "谈心谈话")
@RequiredArgsConstructor
public class PoliceHeartToHeartVisitController {

    private final PoliceHeartToHeartVisitService policeHeartToHeartVisitService;


    @PostMapping("/listHeartToHeartVisit")
    @ApiOperation(value = "谈心家访记录")
    public R<List<PoliceHeartToHeartVisit>> listHeartToHeartVisit(@RequestBody PoliceHeartToHeartQueryDTO request) {

        Page<PoliceHeartToHeartVisit> page = policeHeartToHeartVisitService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceHeartToHeartVisit>lambdaQuery()
                        .eq(StringUtils.isNotBlank(request.getVisitorIdCard()), PoliceHeartToHeartVisit::getVisitorIdCard, request.getVisitorIdCard())
                        .eq(StringUtils.isNotBlank(request.getVisiteeIdCard()), PoliceHeartToHeartVisit::getVisiteeIdCard, request.getVisiteeIdCard())
                        .eq(request.getConfirmStatus() != null, PoliceHeartToHeartVisit::getConfirmStatus, request.getConfirmStatus())
                        .eq(StringUtils.isNotBlank(request.getForm()),PoliceHeartToHeartVisit::getForm,request.getForm())
                        .and(StringUtils.isNotBlank(request.getQuery()),
                                i -> i.
                                        like(PoliceHeartToHeartVisit::getVisitorName, request.getQuery())
                                        .or()
                                        .like(PoliceHeartToHeartVisit::getVisiteeName, request.getQuery())
                                        .or()
                                        .like(PoliceHeartToHeartVisit::getContent, request.getQuery())
                                        .or()
                                        .like(PoliceHeartToHeartVisit::getLocation, request.getQuery())
                                        .or()
                                        .like(PoliceHeartToHeartVisit::getReason, request.getQuery())
                        )
                        .orderByDesc(PoliceHeartToHeartVisit::getStartTime));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/addHeartToHeartVisit")
    @ApiOperation("添加谈心家访记录")
    public R<Boolean> addHeartToHeartVisit(@RequestBody PoliceHeartToHeartVisit request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        Object visitee = SsoCacheUtil.getUserObjByIdCard(request.getVisiteeIdCard());
        if (visitee == null) {
            return R.fail("被谈心/家访人不存在");
        }
        JSONObject visiteeObj = JSONObject.from(visitee);
        request.setVisiteeName(visiteeObj.getString("name"));
        request.setVisiteeUnit(visiteeObj.getJSONArray("organization").getJSONObject(0).getString("organization_name"));
        request.setVisiteePosition(visiteeObj.getString("job_name"));

        Object visitor = SsoCacheUtil.getUserObjByIdCard(UserUtils.getUser().getIdCard());
        if (visitor == null) {
            return R.fail("谈心/家访人不存在");
        }
        JSONObject visitorObj = JSONObject.from(visitor);
        request.setVisitorIdCard(visitorObj.getString("id_card"));
        request.setVisitorName(visitorObj.getString("name"));
        request.setVisitorUnit(visitorObj.getJSONArray("organization").getJSONObject(0).getString("organization_name"));
        request.setVisitorPosition(visitorObj.getString("job_name"));
        return R.ok(policeHeartToHeartVisitService.save(request));
    }

    @PostMapping("/updateHeartToHeartVisit")
    @ApiOperation("更新谈心家访记录")
    public R<Boolean> updateHeartToHeartVisit(@RequestBody PoliceHeartToHeartVisit request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeHeartToHeartVisitService.updateById(request));
    }

    @PostMapping("/deleteHeartToHeartVisit")
    @ApiOperation("删除谈心家访记录")
    public R<Boolean> deleteHeartToHeartVisit(@RequestBody PoliceHeartToHeartVisit request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeHeartToHeartVisitService.removeById(request));
    }

}
