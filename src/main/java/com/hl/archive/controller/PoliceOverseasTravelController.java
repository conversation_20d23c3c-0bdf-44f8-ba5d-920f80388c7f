package com.hl.archive.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceOverseasTravel;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.service.PoliceOverseasTravelService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/overseasTravel")
@RequiredArgsConstructor
@Api(tags = "出国记录")
public class PoliceOverseasTravelController {

    private final PoliceOverseasTravelService policeOverseasTravelService;

    @ApiOperation("分页查询")
    @PostMapping("/page")
    public R<List<PoliceOverseasTravel>> page(@RequestBody PoliceBaseQueryRequest request) {
        Page<PoliceOverseasTravel> page = policeOverseasTravelService.page(Page.of(request.getPage(), request.getLimit()),
                Wrappers.<PoliceOverseasTravel>lambdaQuery()
                        .eq(PoliceOverseasTravel::getIdCard, request.getIdCard())
                        .orderByDesc(PoliceOverseasTravel::getStartDate));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

}
