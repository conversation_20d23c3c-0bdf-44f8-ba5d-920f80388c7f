package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PolicePhDeclarationRequestDTO;
import com.hl.archive.domain.entity.PolicePhDeclaration;
import com.hl.archive.service.PolicePhDeclarationService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/PhDeclaration")
@Api(tags = "健康个人申报")
@RequiredArgsConstructor
public class PolicePhDeclarationController {


    private final PolicePhDeclarationService policePhDeclarationService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PolicePhDeclaration>> page(@RequestBody PolicePhDeclarationRequestDTO requestDTO) {
        Page<PolicePhDeclaration> page = policePhDeclarationService.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PolicePhDeclaration>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getIdCard()), PolicePhDeclaration::getIdCard, requestDTO.getIdCard())
                .like(StrUtil.isNotBlank(requestDTO.getName()), PolicePhDeclaration::getName, requestDTO.getName()));
        return R.ok(page.getRecords(), (int) page.getTotal());

    }


    @ApiOperation("新增个人申报")
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody PolicePhDeclaration policePhDeclaration) {
        policePhDeclaration.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policePhDeclarationService.save(policePhDeclaration));
    }

    @ApiOperation("更新")
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody PolicePhDeclaration policePhDeclaration) {
        policePhDeclaration.setUpdatedBy(UserUtils.getUser().getIdCard());
        return  R.ok(policePhDeclarationService.updateById(policePhDeclaration));
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public R<Boolean> delete(@RequestBody PolicePhDeclaration policePhDeclaration){
        policePhDeclaration.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policePhDeclarationService.removeById(policePhDeclaration.getId()));
    }




}
