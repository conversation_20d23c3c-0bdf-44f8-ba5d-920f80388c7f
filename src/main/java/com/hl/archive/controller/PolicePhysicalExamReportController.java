package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PolicePhysicalExamReportRequestDTO;
import com.hl.archive.domain.entity.PolicePhysicalExamReport;
import com.hl.archive.service.PolicePhysicalExamReportService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/physicalExamReport")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "体检报告")
public class PolicePhysicalExamReportController {

    private final PolicePhysicalExamReportService policePhysicalExamReportService;


    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PolicePhysicalExamReport>> page(@RequestBody PolicePhysicalExamReportRequestDTO requestDTO) {
        Page<PolicePhysicalExamReport> page = policePhysicalExamReportService.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PolicePhysicalExamReport>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getIdCard()), PolicePhysicalExamReport::getIdCard, requestDTO.getIdCard())
                .eq(StrUtil.isNotBlank(requestDTO.getReportTime()), PolicePhysicalExamReport::getReportTime, requestDTO.getReportTime()));
        return R.ok(page.getRecords(),(int) page.getTotal());
    }

}
