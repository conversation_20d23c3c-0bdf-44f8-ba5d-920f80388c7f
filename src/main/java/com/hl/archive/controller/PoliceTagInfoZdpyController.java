package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoZdpyAddDTO;
import com.hl.archive.domain.dto.PoliceTagInfoZdpyQueryDTO;
import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.archive.domain.entity.PoliceTagInfoXjdf;
import com.hl.archive.domain.entity.PoliceTagInfoZdpy;
import com.hl.archive.service.PoliceTagInfoZdpyService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/tagInfoZdpy")
@Api(tags = "重点培养")
@RequiredArgsConstructor
public class PoliceTagInfoZdpyController {


    private final PoliceTagInfoZdpyService policeTagInfoZdpyService;


    @PostMapping("/addTag")
    @ApiOperation("添加")
    public R<Boolean> addTag(@RequestBody PoliceTagInfoZdpyAddDTO dto){
        boolean b = policeTagInfoZdpyService.addTag(dto);
        return  R.ok(b);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceTagInfoZdpy>> listTag(@RequestBody PoliceTagInfoZdpyQueryDTO dto){

        LambdaQueryWrapper<PoliceTagInfoZdpy> queryWrapper = Wrappers.<PoliceTagInfoZdpy>lambdaQuery()
                .eq(StrUtil.isNotBlank(dto.getIdCard()), PoliceTagInfoZdpy::getIdCard, dto.getIdCard());
        if (StrUtil.isNotBlank(dto.getOrganizationId())){
            if (!"320412000000".equals(dto.getOrganizationId())) {
                queryWrapper.likeRight(PoliceTagInfoZdpy::getOrganizationId, dto.getOrganizationId().substring(0, 8));
            }
        }
        Page<PoliceTagInfoZdpy> page = policeTagInfoZdpyService.page(Page.of(dto.getPage(), dto.getLimit()),queryWrapper);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除")
    public R<Boolean> batchDelete(@RequestBody JSONObject param){
        boolean b = policeTagInfoZdpyService.removeBatchByIds(param.getList("ids", String.class));
        return R.ok(b);
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceTagInfoZdpy policeTagInfoZdpy){
        boolean b = policeTagInfoZdpyService.updateById(policeTagInfoZdpy);
        return  R.ok(b);
    }


}
