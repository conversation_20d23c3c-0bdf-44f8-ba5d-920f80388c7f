package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceUnitAwardRequestDTO;
import com.hl.archive.domain.entity.PoliceUnitAward;
import com.hl.archive.service.PoliceUnitAwardService;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/policeUnitAward")
@RequiredArgsConstructor
@Api(tags = "单位荣誉")
public class PoliceUnitAwardController {

    private final PoliceUnitAwardService policeUnitAwardService;

    @PostMapping("/pageUnitAward")
    @ApiOperation("分页查询(不传分页默认全部)")
    public R<List<PoliceUnitAward>> pageUnitAward(@RequestBody PoliceUnitAwardRequestDTO requestDTO) {
        String query = "";

        if (!"320412000000".equals(requestDTO.getOrganizationId())) {
            String organizationName = SsoCacheUtil.getOrganizationName(requestDTO.getOrganizationId());
            query = organizationName.replaceAll("派出所","");
        }

        LambdaQueryWrapper<PoliceUnitAward> queryWrapper = Wrappers.<PoliceUnitAward>lambdaQuery()
                .like(StrUtil.isNotBlank(query), PoliceUnitAward::getUnit, query)
                .orderByDesc(PoliceUnitAward::getAwardTime);
        if ("320412000000".equals(requestDTO.getOrganizationId())) {
            queryWrapper.in(PoliceUnitAward::getUnit,"武进分局","武进区局","常州市武进区公安局","常州市公安局武进分局",
                    "武进区公安局");
        }

        Page<PoliceUnitAward> page = policeUnitAwardService.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);

        return R.ok(page.getRecords(), (int) page.getTotal());

    }

}
