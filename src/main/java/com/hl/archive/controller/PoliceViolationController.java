package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.ViolationPersonQueryDTO;
import com.hl.archive.domain.dto.ViolationPersonVO;
import com.hl.archive.service.PoliceViolationPersonService;
import com.hl.archive.service.PoliceViolationResultService;
import com.hl.archive.service.PoliceViolationSummaryService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 违法违纪人员管理控制器
 */
@RestController
@RequestMapping("/violation")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "违法违纪人员管理")
public class PoliceViolationController {

    private final PoliceViolationPersonService policeViolationPersonService;
    private final PoliceViolationSummaryService policeViolationSummaryService;
    private final PoliceViolationResultService policeViolationResultService;

    /**
     * 分页查询违法违纪人员综合信息
     */
    @PostMapping("/page")
    @ApiOperation("分页查询违法违纪人员综合信息")
    public R<List<ViolationPersonVO>> pageViolationPersons(@RequestBody ViolationPersonQueryDTO dto) {
        try {
            Page<ViolationPersonVO> page = policeViolationPersonService.pageViolationPersons(dto);
            return R.ok(page.getRecords(), (int) page.getTotal());
        } catch (Exception e) {
            log.error("分页查询违法违纪人员失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

//    /**
//     * 根据ID查询违法违纪人员详情
//     */
//    @GetMapping("/detail/{personId}")
//    @ApiOperation("查询违法违纪人员详情")
//    public R<ViolationPersonVO> getViolationPersonDetail(@PathVariable Long personId) {
//        try {
//            PoliceViolationPerson person = policeViolationPersonService.getById(personId);
//            if (person == null) {
//                return R.fail("人员信息不存在");
//            }
//
//            // 查询问题汇总信息
//            PoliceViolationSummary summary = null;
//            if (person.getWtXxzjbh() != null) {
//                summary = policeViolationSummaryService.lambdaQuery()
//                        .eq(PoliceViolationSummary::getXxzjbh, person.getWtXxzjbh())
//                        .one();
//            }
//
//            // 查询处理结果信息
//            List<PoliceViolationResult> results = null;
//            if (person.getXxzjbh() != null) {
//                results = policeViolationResultService.lambdaQuery()
//                        .eq(PoliceViolationResult::getRyXxzjbh, person.getXxzjbh())
//                        .orderByDesc(PoliceViolationResult::getClsj)
//                        .list();
//            }
//
//            ViolationPersonVO vo = ViolationPersonVO.fromEntities(person, summary, results);
//            return R.ok(vo);
//        } catch (Exception e) {
//            log.error("查询违法违纪人员详情失败", e);
//            return R.fail("查询失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询违法违纪人员基本信息列表
//     */
//    @PostMapping("/persons")
//    @ApiOperation("查询违法违纪人员基本信息列表")
//    public R<List<PoliceViolationPerson>> listViolationPersons(@RequestBody ViolationPersonQueryDTO dto) {
//        try {
//            Page<PoliceViolationPerson> page = policeViolationPersonService.page(
//                    Page.of(dto.getPage(), dto.getLimit()),
//                    policeViolationPersonService.buildQueryWrapper(dto)
//            );
//            return R.ok(page.getRecords(), (int) page.getTotal());
//        } catch (Exception e) {
//            log.error("查询违法违纪人员基本信息失败", e);
//            return R.fail("查询失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询违法违纪问题汇总列表
//     */
//    @PostMapping("/summaries")
//    @ApiOperation("查询违法违纪问题汇总列表")
//    public R<List<PoliceViolationSummary>> listViolationSummaries(@RequestBody ViolationPersonQueryDTO dto) {
//        try {
//            Page<PoliceViolationSummary> page = policeViolationSummaryService.page(
//                    Page.of(dto.getPage(), dto.getLimit())
//            );
//            return R.ok(page.getRecords(), (int) page.getTotal());
//        } catch (Exception e) {
//            log.error("查询违法违纪问题汇总失败", e);
//            return R.fail("查询失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询违法违纪处理结果列表
//     */
//    @PostMapping("/results")
//    @ApiOperation("查询违法违纪处理结果列表")
//    public R<List<PoliceViolationResult>> listViolationResults(@RequestBody ViolationPersonQueryDTO dto) {
//        try {
//            Page<PoliceViolationResult> page = policeViolationResultService.page(
//                    Page.of(dto.getPage(), dto.getLimit())
//            );
//            return R.ok(page.getRecords(), (int) page.getTotal());
//        } catch (Exception e) {
//            log.error("查询违法违纪处理结果失败", e);
//            return R.fail("查询失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 根据人员xxzjbh查询处理结果
//     */
//    @GetMapping("/results/person/{xxzjbh}")
//    @ApiOperation("根据人员xxzjbh查询处理结果")
//    public R<List<PoliceViolationResult>> getResultsByPersonXxzjbh(@PathVariable String xxzjbh) {
//        try {
//            List<PoliceViolationResult> results = policeViolationResultService.lambdaQuery()
//                    .eq(PoliceViolationResult::getRyXxzjbh, xxzjbh)
//                    .orderByDesc(PoliceViolationResult::getClsj)
//                    .list();
//            return R.ok(results);
//        } catch (Exception e) {
//            log.error("根据人员xxzjbh查询处理结果失败", e);
//            return R.fail("查询失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 根据问题xxzjbh查询问题汇总
//     */
//    @GetMapping("/summary/{xxzjbh}")
//    @ApiOperation("根据问题xxzjbh查询问题汇总")
//    public R<PoliceViolationSummary> getSummaryByXxzjbh(@PathVariable String xxzjbh) {
//        try {
//            PoliceViolationSummary summary = policeViolationSummaryService.lambdaQuery()
//                    .eq(PoliceViolationSummary::getXxzjbh, xxzjbh)
//                    .one();
//            return R.ok(summary);
//        } catch (Exception e) {
//            log.error("根据问题xxzjbh查询问题汇总失败", e);
//            return R.fail("查询失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 统计违法违纪人员数量
//     */
//    @PostMapping("/count")
//    @ApiOperation("统计违法违纪人员数量")
//    public R<Long> countViolationPersons(@RequestBody ViolationPersonQueryDTO dto) {
//        try {
//            long count = policeViolationPersonService.count(
//                    policeViolationPersonService.buildQueryWrapper(dto)
//            );
//            return R.ok(count);
//        } catch (Exception e) {
//            log.error("统计违法违纪人员数量失败", e);
//            return R.fail("统计失败：" + e.getMessage());
//        }
//    }
}
