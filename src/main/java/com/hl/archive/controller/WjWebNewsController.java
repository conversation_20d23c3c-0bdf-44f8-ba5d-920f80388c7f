package com.hl.archive.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.WjWebNewsQueryDTO;
import com.hl.archive.domain.entity.WjWebNews;
import com.hl.archive.service.WjWebNewsService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/wjWebNews")
@RequiredArgsConstructor
@Api(tags = "分局主页爬虫")
public class WjWebNewsController {

    private final WjWebNewsService wjWebNewsService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<?> page(@RequestBody WjWebNewsQueryDTO dto) {
        Page<WjWebNews> page = wjWebNewsService.page(Page.of(dto.getPage(), dto.getLimit()));
        return R.ok(page.getRecords(), (int) page.getTotal());

    }
}
