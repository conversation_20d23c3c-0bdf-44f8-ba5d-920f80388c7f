package com.hl.archive.controller;

import com.hl.archive.domain.dto.PoliceWorkQualityCountReturnDTO;
import com.hl.archive.domain.dto.WorkQualityCountQueryDTO;
import com.hl.archive.search.document.ViewPoliceExportDocument;
import com.hl.archive.service.PoliceWorkQualityCountService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

@RestController
@RequestMapping("/policeWorkQuality")
@Slf4j
@RequiredArgsConstructor
@Api(tags = "工作质态")
public class WorkQualityCountController {

    private final PoliceWorkQualityCountService policeWorkQualityCountService;

    @PostMapping("/count")
    @ApiOperation("统计")
    public R<PoliceWorkQualityCountReturnDTO> count(@RequestBody WorkQualityCountQueryDTO queryDTO) {
        try {
            // 使用并发执行所有统计查询，提升性能
            CompletableFuture<PoliceWorkQualityCountReturnDTO> measuresFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.countAJCS(queryDTO));

            CompletableFuture<Long> xbCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.countXBAJ(queryDTO));

            CompletableFuture<Long> zbCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.countZBAJ(queryDTO));

            CompletableFuture<Long> caseExamineCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.caseExamineCount(queryDTO));

            CompletableFuture<Long> policeExamineCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.policeExamineCount(queryDTO));

            CompletableFuture<Long> casePlaceExamineCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.casePlaceExamineCounr(queryDTO));

            CompletableFuture<PoliceWorkQualityCountReturnDTO> timeoutCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.timeoutCount(queryDTO));

            CompletableFuture<Long> handlePoliceCountFuture =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.jqCount(queryDTO));
            CompletableFuture<Long> nwtbCount =
                    CompletableFuture.supplyAsync(() -> policeWorkQualityCountService.nwtbCount(queryDTO));

            // 等待所有异步任务完成并组装结果
            CompletableFuture<PoliceWorkQualityCountReturnDTO> resultFuture = CompletableFuture.allOf(
                    measuresFuture, xbCountFuture, zbCountFuture, caseExamineCountFuture,
                    policeExamineCountFuture, casePlaceExamineCountFuture, timeoutCountFuture,handlePoliceCountFuture,
                    nwtbCount
            ).thenApply(v -> {
                PoliceWorkQualityCountReturnDTO qualityCountReturnDTO = new PoliceWorkQualityCountReturnDTO();

                // 刑事 治安 人员措施采取数量
                PoliceWorkQualityCountReturnDTO measures = measuresFuture.join();
                qualityCountReturnDTO.setXsCaseStat(measures.getXsCaseStat());
                qualityCountReturnDTO.setZaCaseStat(measures.getZaCaseStat());

                // 协办案件数量
                qualityCountReturnDTO.setXbajCount(xbCountFuture.join());

                // 主办案件数量
                qualityCountReturnDTO.setZbajCount(zbCountFuture.join());

                // 案件考评数量
                qualityCountReturnDTO.setCaseExamineCount(caseExamineCountFuture.join());

                // 警情考评数量
                qualityCountReturnDTO.setPoliceExamineCount(policeExamineCountFuture.join());

                // 办案场所考评数量
                qualityCountReturnDTO.setCasePlaceExamineCount(casePlaceExamineCountFuture.join());

                // 超时统计
                PoliceWorkQualityCountReturnDTO timeoutCount = timeoutCountFuture.join();
                qualityCountReturnDTO.setSignTimeoutCount(timeoutCount.getSignTimeoutCount());
                qualityCountReturnDTO.setWorkTimeoutCount(timeoutCount.getWorkTimeoutCount());

                qualityCountReturnDTO.setHandlePoliceCount(handlePoliceCountFuture.join());
                qualityCountReturnDTO.setNwtbCount(nwtbCount.join());

                return qualityCountReturnDTO;
            });

            return R.ok(resultFuture.get());

        } catch (CompletionException e) {
            log.error("并发执行统计查询时发生异常", e.getCause());
            throw new RuntimeException("统计查询执行失败", e.getCause());
        } catch (Exception e) {
            log.error("执行统计查询时发生异常", e);
            throw new RuntimeException("统计查询执行失败", e);
        }
    }


    @PostMapping("/jqCount")
    public R<?> jqCount(@RequestBody WorkQualityCountQueryDTO queryDTO) {
        Long l = policeWorkQualityCountService.jqCount(queryDTO);
        return R.ok(l);
    }

    @PostMapping("/pageJq")
    @ApiOperation("分页查询警情")
    public R<List<ViewPoliceExportDocument>> pageJq(@RequestBody WorkQualityCountQueryDTO queryDTO) {
        EsPageInfo<ViewPoliceExportDocument> viewPoliceExportDocumentEsPageInfo = policeWorkQualityCountService.pageJq(queryDTO);
        return R.ok(viewPoliceExportDocumentEsPageInfo.getList() == null ? new ArrayList<>() : viewPoliceExportDocumentEsPageInfo.getList(), (int) viewPoliceExportDocumentEsPageInfo.getTotal());
    }

}
