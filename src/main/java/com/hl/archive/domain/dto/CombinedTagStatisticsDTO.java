package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 登峰训练营和实战能力体系组合统计结果DTO
 */
@Data
@ApiModel(description = "登峰训练营和实战能力体系组合统计结果DTO")
public class CombinedTagStatisticsDTO {

    /**
     * 登峰训练营统计
     */
    @ApiModelProperty(value = "登峰训练营统计")
    private TagStatisticsDTO dengfengTraining;

    /**
     * 实战能力体系统计
     */
    @ApiModelProperty(value = "实战能力体系统计")
    private TagStatisticsDTO combatAbility;


    private TagStatisticsDTO xinghuoPlan;


    private TagStatisticsDTO jingyingXianfeng;
}
