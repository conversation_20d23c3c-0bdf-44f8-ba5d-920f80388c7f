package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入结果DTO
 */
@ApiModel(description = "导入结果")
@Data
public class ImportResultDTO {
    
    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount = 0;
    
    /**
     * 成功导入数量
     */
    @ApiModelProperty(value = "成功导入数量")
    private Integer successCount = 0;
    
    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer failCount = 0;
    
    /**
     * 错误信息列表
     */
    @ApiModelProperty(value = "错误信息列表")
    private List<String> errorMessages = new ArrayList<>();
    
    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean success = true;
    
    /**
     * 添加错误信息
     */
    public void addErrorMessage(String message) {
        this.errorMessages.add(message);
        this.failCount++;
        if (this.failCount > 0) {
            this.success = false;
        }
    }
    
    /**
     * 增加成功数量
     */
    public void incrementSuccess() {
        this.successCount++;
    }
    
    /**
     * 设置总数量
     */
    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
}
