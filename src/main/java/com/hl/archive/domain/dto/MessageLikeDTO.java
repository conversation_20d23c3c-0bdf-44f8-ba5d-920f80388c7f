package com.hl.archive.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 留言点赞DTO
 */
@ApiModel(description = "留言点赞DTO")
@Data
public class MessageLikeDTO {
    /**
     * 留言ID
     */
    @ApiModelProperty(value = "留言ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long messageId;

    /**
     * 用户身份证号
     */
    @ApiModelProperty(value = "用户身份证号", required = true)
    private String userIdCard;

    /**
     * 是否点赞（true=点赞，false=取消点赞）
     */
    @ApiModelProperty(value = "是否点赞", required = true)
    private Boolean isLike;
}
