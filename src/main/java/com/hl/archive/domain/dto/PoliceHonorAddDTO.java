package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.nacos.shaded.com.google.gson.JsonArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class PoliceHonorAddDTO {

    private List<String> idCards;

    /**
     * 荣誉名称
     */

    @ApiModelProperty(value = "荣誉名称")
    private String honorName;

    /**
     * 获得时间
     */
    @ApiModelProperty(value = "获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardDate;

    @ApiModelProperty(value = "编号")
    private String bh;
    /**
     * 批准机关名称
     */
    @ApiModelProperty(value = "批准机关名称")
    private String approveAuthority;


    @ApiModelProperty(value = "数据来源类型")
    private Integer sourceType;

    private JSONArray files;
}
