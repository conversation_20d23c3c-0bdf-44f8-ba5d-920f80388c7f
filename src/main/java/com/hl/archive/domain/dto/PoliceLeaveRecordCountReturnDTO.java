package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 民警请休假统计返回DTO
 */
@ApiModel(description = "民警请休假统计返回DTO")
@Data
public class PoliceLeaveRecordCountReturnDTO {


    /**
     * 出差统计
     */
    @ApiModelProperty(value = "出差统计")
    private BusinessTripStatistics businessTripStatistics;

    /**
     * 请假统计
     */
    @ApiModelProperty(value = "请假统计")
    private LeaveStatistics leaveStatistics;

    /**
     * 出差统计内部类
     */
    @Data
    @ApiModel(description = "出差统计")
    public static class BusinessTripStatistics {
        /**
         * 出差次数
         */
        @ApiModelProperty(value = "出差次数")
        private Integer tripCount = 0;

        /**
         * 出差总天数
         */
        @ApiModelProperty(value = "出差总天数")
        private Integer totalDays = 0;
    }

    /**
     * 请假统计内部类
     */
    @Data
    @ApiModel(description = "请假统计")
    public static class LeaveStatistics {
        /**
         * 请假总次数
         */
        @ApiModelProperty(value = "请假总次数")
        private Integer totalLeaveCount = 0;

        /**
         * 请假总天数
         */
        @ApiModelProperty(value = "请假总天数")
        private Integer totalLeaveDays = 0;

        /**
         * 各类型请假详细统计
         * key: 请假类型, value: 该类型的次数
         */
        @ApiModelProperty(value = "各类型请假详细统计")
        private List<TypeCount> leaveTypeDetails;
    }


    @Data
    public static class TypeCount{
        private String type;
        private Integer count;
    }
}
