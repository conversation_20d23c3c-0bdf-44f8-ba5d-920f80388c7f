package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class PolicePersonWarnRecordRequestDTO {


    private String query;

    private String organizationId;

    private String idCard;

    private String warnType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime warnStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private  Date warnEndTime;

    private String name;

    private Integer page;

    private Integer limit;

    private Integer signStatus;


}
