package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PolicePersonWarnRecordTxTaskCreateDTO {

    private String dataId;

    @ApiModelProperty(value = "被谈心谈话人")
    private List<String> btxjfr;

    @ApiModelProperty(value = "谈心谈话人")
    private List<String> txthr;

    @ApiModelProperty(value = "谈心谈话原因")
    private String txthyy;
}
