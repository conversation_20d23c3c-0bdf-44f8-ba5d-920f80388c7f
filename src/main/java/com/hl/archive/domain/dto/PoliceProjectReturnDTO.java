package com.hl.archive.domain.dto;

import com.hl.archive.domain.entity.PoliceProjectContact;
import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.archive.domain.entity.PoliceProjectMaterial;
import com.hl.archive.domain.entity.PoliceProjectStory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PoliceProjectReturnDTO {

    @ApiModelProperty("星火计划入项个人")
    private PoliceProjectEntryPerson policeProjectEntryPerson;

    @ApiModelProperty("培养联系")
    private List<PoliceProjectContact> policeProjectContacts;

    @ApiModelProperty("图文资料")
    private List<PoliceProjectMaterial> policeProjectMaterials;

    @ApiModelProperty("先进事迹")
    private List<PoliceProjectStory> policeProjectStories;
}
