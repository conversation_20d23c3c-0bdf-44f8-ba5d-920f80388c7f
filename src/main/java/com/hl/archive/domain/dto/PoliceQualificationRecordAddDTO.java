package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class PoliceQualificationRecordAddDTO {


    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private List<String> idCard;


    /**
     * 资格类目
     */
    @ApiModelProperty(value = "资格类目")
    private String category;

    /**
     * 具体项目
     */
    @ApiModelProperty(value = "具体项目")
    private String project;

    /**
     * 成绩
     */
    @ApiModelProperty(value = "成绩")
    private String score;

    /**
     * 记录日期
     */
    @ApiModelProperty(value = "记录日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime issueDate;



    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;


    private String scoreQualified;



}
