package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class PoliceTagInfoXjdfAddDTO {

    private List<String> idCardList;

    private String tagType;

    private String tagName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardDate;

    private String remark;
}
