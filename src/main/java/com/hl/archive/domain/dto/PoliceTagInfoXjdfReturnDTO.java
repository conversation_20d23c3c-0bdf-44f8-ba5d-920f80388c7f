package com.hl.archive.domain.dto;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class PoliceTagInfoXjdfReturnDTO {

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardDate;

    private String tagName;


    private List<String> idCardList;

    private List<Integer> ids;


    private String remark;

    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "idCardList")
    private List<JSONObject> personInfoList;
}
