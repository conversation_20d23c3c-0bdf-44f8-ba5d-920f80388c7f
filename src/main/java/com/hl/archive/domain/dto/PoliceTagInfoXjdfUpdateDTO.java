package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 民警标签信息修改DTO
 */
@Data
public class PoliceTagInfoXjdfUpdateDTO {

    /**
     * 原始标签的查询条件（用于定位要修改的标签组）
     */
    private String originalTagType;
    private String originalTagName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate originalAwardDate;
    private String originalRemark;

    /**
     * 新的标签信息（要修改成的值）
     */
    private String tagType;
    private String tagName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardDate;
    private String remark;

    /**
     * 要添加的人员身份证号列表
     */
    private List<String> addIdCardList;

    /**
     * 要移除的人员身份证号列表
     */
    private List<String> removeIdCardList;

    /**
     * 操作类型：UPDATE-修改，DELETE-删除整个标签组
     */
    private String operation = "UPDATE";

    /**
     * 操作类型枚举
     */
    public static class Operation {
        public static final String UPDATE = "UPDATE";
        public static final String DELETE = "DELETE";
    }
}
