package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 标签统计结果DTO
 */
@Data
@ApiModel(description = "标签统计结果DTO")
public class TagStatisticsDTO {

    /**
     * 标签类型代码
     */
    @ApiModelProperty(value = "标签类型代码")
    private String tagType;

    /**
     * 标签类型名称
     */
    @ApiModelProperty(value = "标签类型名称")
    private String tagTypeName;

    /**
     * 总人数
     */
    @ApiModelProperty(value = "总人数")
    private Integer totalCount;

    /**
     * 标签详细统计列表
     */
    @ApiModelProperty(value = "标签详细统计列表")
    private List<TagDetailStatistics> tagDetails;

    /**
     * 标签详细统计
     */
    @Data
    @ApiModel(description = "标签详细统计")
    public static class TagDetailStatistics {
        
        /**
         * 标签名称
         */
        @ApiModelProperty(value = "标签名称")
        private String tagName;

        /**
         * 该标签的人数
         */
        @ApiModelProperty(value = "该标签的人数")
        private Integer count;
    }
}
