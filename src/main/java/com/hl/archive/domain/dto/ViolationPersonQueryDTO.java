package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 违法违纪人员查询DTO
 */
@Data
@ApiModel(description = "违法违纪人员查询DTO")
public class ViolationPersonQueryDTO {

    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer limit = 20;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "立案时所在单位")
    private String caseOrg;

    @ApiModelProperty(value = "警种部门")
    private String policeDept;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "职级")
    private String rank;

    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "是否系被倒查问责")
    private String isAccountability;

    @ApiModelProperty(value = "四种形态处理归类")
    private String dispositionCategory;

    @ApiModelProperty(value = "出生日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDateStart;

    @ApiModelProperty(value = "出生日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDateEnd;

    @ApiModelProperty(value = "入党时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate joinPartyDateStart;

    @ApiModelProperty(value = "入党时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate joinPartyDateEnd;

    @ApiModelProperty(value = "问题线索来源")
    private String clueSource;

    @ApiModelProperty(value = "违规违纪类型")
    private String violationType;

    @ApiModelProperty(value = "案件编号")
    private String caseNo;

    @ApiModelProperty(value = "处理结果")
    private String cljg;

    @ApiModelProperty(value = "处理单位")
    private String cldw;

    @ApiModelProperty(value = "处理时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate clsjStart;

    @ApiModelProperty(value = "处理时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate clsjEnd;

    @ApiModelProperty(value = "关键字搜索（姓名、单位、案件编号等）")
    private String keyword;

    private String idCard;
}
