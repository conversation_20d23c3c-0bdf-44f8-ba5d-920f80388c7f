package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.domain.entity.PoliceViolationPerson;
import com.hl.archive.domain.entity.PoliceViolationResult;
import com.hl.archive.domain.entity.PoliceViolationSummary;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 违法违纪人员综合信息VO
 */
@Data
@ApiModel(description = "违法违纪人员综合信息VO")
public class ViolationPersonVO {

    @ApiModelProperty(value = "人员主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long personId;

    @ApiModelProperty(value = "人员信息主键编号")
    private String xxzjbh;

    @ApiModelProperty(value = "问题信息主键编号")
    private String wtXxzjbh;

    // ========== 人员基本信息 ==========
    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    @ApiModelProperty(value = "立案时所在单位")
    private String caseOrg;

    @ApiModelProperty(value = "警种部门")
    private String policeDept;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "职级")
    private String rank;

    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "入党(团)时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate joinPartyDate;

    @ApiModelProperty(value = "是否系被倒查问责")
    private String isAccountability;

    @ApiModelProperty(value = "四种形态处理归类")
    private String dispositionCategory;

    @ApiModelProperty(value = "备注")
    private String remark;

    // ========== 问题汇总信息 ==========
    @ApiModelProperty(value = "填报单位")
    private String reportOrg;

    @ApiModelProperty(value = "问题线索来源")
    private String clueSource;

    @ApiModelProperty(value = "问题线索内容")
    private String clueContent;

    @ApiModelProperty(value = "发现时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate foundDate;

    @ApiModelProperty(value = "发现单位")
    private String foundOrg;

    @ApiModelProperty(value = "受理时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate acceptDate;

    @ApiModelProperty(value = "受理单位")
    private String acceptOrg;

    @ApiModelProperty(value = "启动调查时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate investigationStart;

    @ApiModelProperty(value = "调查单位")
    private String investigationOrg;

    @ApiModelProperty(value = "调查情况")
    private String investigationResult;

    @ApiModelProperty(value = "启动初核时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate preliminaryStart;

    @ApiModelProperty(value = "初核单位")
    private String preliminaryOrg;

    @ApiModelProperty(value = "初核情况")
    private String preliminaryResult;

    @ApiModelProperty(value = "留置审查时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate detentionDate;

    @ApiModelProperty(value = "留置审查单位")
    private String detentionOrg;

    @ApiModelProperty(value = "主要违法违纪事实")
    private String violationFact;

    @ApiModelProperty(value = "违规违纪类型")
    private String violationType;

    @ApiModelProperty(value = "案件编号")
    private String caseNo;

    // ========== 处理结果列表 ==========
    @ApiModelProperty(value = "处理结果列表")
    private List<ViolationResultVO> results;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 处理结果VO
     */
    @Data
    @ApiModel(description = "违法违纪处理结果VO")
    public static class ViolationResultVO {
        @ApiModelProperty(value = "结果主键")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long resultId;

        @ApiModelProperty(value = "类别名称")
        private String lbmc;

        @ApiModelProperty(value = "处理时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate clsj;

        @ApiModelProperty(value = "处理单位")
        private String cldw;

        @ApiModelProperty(value = "处理结果")
        private String cljg;
    }

    /**
     * 从实体类构建VO
     */
    public static ViolationPersonVO fromEntities(PoliceViolationPerson person, 
                                                PoliceViolationSummary summary, 
                                                List<PoliceViolationResult> results) {
        ViolationPersonVO vo = new ViolationPersonVO();
        
        // 人员信息
        if (person != null) {
            vo.setPersonId(person.getId());
            vo.setXxzjbh(person.getXxzjbh());
            vo.setWtXxzjbh(person.getWtXxzjbh());
            vo.setName(person.getName());
            vo.setGender(person.getGender());
            vo.setBirthDate(person.getBirthDate());
            vo.setCaseOrg(person.getCaseOrg());
            vo.setPoliceDept(person.getPoliceDept());
            vo.setPosition(person.getPosition());
            vo.setRank(person.getRank());
            vo.setPoliticalStatus(person.getPoliticalStatus());
            vo.setJoinPartyDate(person.getJoinPartyDate());
            vo.setIsAccountability(person.getIsAccountability());
            vo.setDispositionCategory(person.getDispositionCategory());
            vo.setRemark(person.getRemark());
            vo.setCreatedAt(person.getCreatedAt());
            vo.setUpdatedAt(person.getUpdatedAt());
        }
        
        // 问题汇总信息
        if (summary != null) {
            vo.setReportOrg(summary.getReportOrg());
            vo.setClueSource(summary.getClueSource());
            vo.setClueContent(summary.getClueContent());
            vo.setFoundDate(summary.getFoundDate());
            vo.setFoundOrg(summary.getFoundOrg());
            vo.setAcceptDate(summary.getAcceptDate());
            vo.setAcceptOrg(summary.getAcceptOrg());
            vo.setInvestigationStart(summary.getInvestigationStart());
            vo.setInvestigationOrg(summary.getInvestigationOrg());
            vo.setInvestigationResult(summary.getInvestigationResult());
            vo.setPreliminaryStart(summary.getPreliminaryStart());
            vo.setPreliminaryOrg(summary.getPreliminaryOrg());
            vo.setPreliminaryResult(summary.getPreliminaryResult());
            vo.setDetentionDate(summary.getDetentionDate());
            vo.setDetentionOrg(summary.getDetentionOrg());
            vo.setViolationFact(summary.getViolationFact());
            vo.setViolationType(summary.getViolationType());
            vo.setCaseNo(summary.getCaseNo());
        }
        
        // 处理结果列表
        if (results != null && !results.isEmpty()) {
            List<ViolationResultVO> resultVOs = results.stream()
                    .map(result -> {
                        ViolationResultVO resultVO = new ViolationResultVO();
                        resultVO.setResultId(result.getId());
                        resultVO.setLbmc(result.getLbmc());
                        resultVO.setClsj(result.getClsj());
                        resultVO.setCldw(result.getCldw());
                        resultVO.setCljg(result.getCljg());
                        return resultVO;
                    })
                    .collect(java.util.stream.Collectors.toList());
            vo.setResults(resultVOs);
        }
        
        return vo;
    }
}
