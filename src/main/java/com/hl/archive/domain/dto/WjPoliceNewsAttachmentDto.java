package com.hl.archive.domain.dto;

import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

/**
 * 武进公安信息网新闻附件数据传输对象
 */
@Data
public class WjPoliceNewsAttachmentDto {
    
    /**
     * 附件名称
     */
    private String fileName;
    
    /**
     * 附件下载URL
     */
    @Translation(type = TransConstants.FJ_WEB_SITE_URL)
    private String downloadUrl;
    
    /**
     * 附件类型（根据文件扩展名判断）
     */
    private String fileType;
    
    /**
     * 文件大小（如果能获取到）
     */
    private String fileSize;
    
    /**
     * 附件图标URL
     */
    @Translation(type = TransConstants.FJ_WEB_SITE_URL)
    private String iconUrl;
}
