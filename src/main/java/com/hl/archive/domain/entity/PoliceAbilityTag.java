package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 能力标签表
 */
@ApiModel(description = "能力标签表")
@Data
@TableName(value = "police_ability_tag", autoResultMap = true)
public class PoliceAbilityTag {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "身份证号")
    private List<String> idCard;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "idCard")
    private List<JSONObject> userInfo;

    /**
     * 能力标签名称
     */
    @TableField(value = "ability_tag_name",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "能力标签名称")
    private List<String> abilityTagName;

    /**
     * 能力标签编码
     */
    @TableField(value = "ability_tag_code",typeHandler =  Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "能力标签编码")
    private List<String> abilityTagCode;

    /**
     * 时间
     */
    @TableField(value = "obtain_time")
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate obtainTime;

    /**
     * 说明
     */
    @TableField(value = "description")
    @ApiModelProperty(value = "说明")
    private String description;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @TableField(value = "task_id")
    private String taskId;
}