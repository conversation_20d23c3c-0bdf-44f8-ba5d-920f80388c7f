package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 能力参评
 */
@ApiModel(description = "能力参评")
@Data
@TableName(value = "police_capability_eval")
public class PoliceCapabilityEval {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 参评人
     */
    @TableField(value = "participant_name")
    @ApiModelProperty(value = "参评人")
    private String participantName;

    /**
     * 警号
     */
    @TableField(value = "police_number")
    @ApiModelProperty(value = "警号")
    private String policeNumber;

    /**
     * 职务
     */
    @TableField(value = "`position`")
    @ApiModelProperty(value = "职务")
    private String position;

    /**
     * 单位
     */
    @TableField(value = "org_name")
    @ApiModelProperty(value = "单位")
    private String orgName;

    /**
     * 方案名称
     */
    @TableField(value = "plan_name")
    @ApiModelProperty(value = "方案名称")
    private String planName;

    /**
     * 大类名称
     */
    @TableField(value = "category_name")
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 特征名称
     */
    @TableField(value = "feature_name")
    @ApiModelProperty(value = "特征名称")
    private String featureName;

    /**
     * 审核人
     */
    @TableField(value = "reviewer")
    @ApiModelProperty(value = "审核人")
    private String reviewer;

    /**
     * 参评状态
     */
    @TableField(value = "eval_status")
    @ApiModelProperty(value = "参评状态")
    private String evalStatus;

    /**
     * 参评档次
     */
    @TableField(value = "eval_level")
    @ApiModelProperty(value = "参评档次")
    private String evalLevel;

    /**
     * 审核结果
     */
    @TableField(value = "review_result")
    @ApiModelProperty(value = "审核结果")
    private String reviewResult;

    /**
     * 流程id
     */
    @TableField(value = "lcid")
    @ApiModelProperty(value = "流程id")
    private String lcid;

    /**
     * 信息主键编号
     */
    @TableField(value = "xxzjbh")
    @ApiModelProperty(value = "信息主键编号")
    private String xxzjbh;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
}