package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 社团活动报名表
 */
@ApiModel(description="社团活动报名表")
@Data
@TableName(value = "police_club_activity_enroll")
public class PoliceClubActivityEnroll {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联活动编号
     */
    @TableField(value = "activity_id")
    @ApiModelProperty(value="关联活动编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 报名人员
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="报名人员")
    private String idCard;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    private JSONObject personInfo;

    /**
     * 报名时间
     */
    @TableField(value = "enroll_time")
    @ApiModelProperty(value="报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollTime;

    /**
     * 报名状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="报名状态 0 待审核  1 通过 2 驳回")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 审核操作人
     */
    @TableField(value = "approve_person")
    @ApiModelProperty(value="审核操作人")
    private String approvePerson;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;

    /**
     * 是否删除（0=未删除, 1=已删除）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除（0=未删除, 1=已删除）")
    private Boolean isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;
}