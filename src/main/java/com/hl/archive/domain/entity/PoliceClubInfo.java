package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 社团信息表
 */
@ApiModel(description = "社团信息表")
@Data
@TableName(value = "police_club_info", autoResultMap = true)
public class PoliceClubInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 社团名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "社团名称")
    private String name;

    /**
     * 简介
     */
    @TableField(value = "description")
    @ApiModelProperty(value = "简介")
    private String description;

    /**
     * 社长
     */
    @TableField(value = "leader", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "社长")
    private List<String> leader;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "leader")
    private List<JSONObject>  leaderInfo;

    /**
     * 成员 身份证数组
     */
    @TableField(value = "members", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "成员 身份证数组")
    private List<String> members;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "members")
    private List<JSONObject> membersInfo;

    /**
     * 封面图片URL
     */
    @TableField(value = "cover_image", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "封面图片URL")
    private JSONObject coverImage;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;

    /**
     * 是否删除（0=未删除, 1=已删除）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除（0=未删除, 1=已删除）")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "社团状态 0 正常 1 解散")
    private Integer status;
}