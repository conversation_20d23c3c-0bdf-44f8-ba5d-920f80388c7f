package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 留言点赞表
 */
@ApiModel(description = "留言点赞表")
@Data
@TableName(value = "police_club_message_like")
public class PoliceClubMessageLike {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 留言ID
     */
    @TableField(value = "message_id")
    @ApiModelProperty(value = "留言ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long messageId;

    /**
     * 点赞用户身份证号
     */
    @TableField(value = "user_id_card")
    @ApiModelProperty(value = "点赞用户身份证号")
    private String userIdCard;

    /**
     * 点赞时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "点赞时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // 关联信息
    /**
     * 点赞用户信息
     */
    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "userIdCard")
    private JSONObject userInfo;

    /**
     * 留言信息
     */
    @TableField(exist = false)
    private PoliceClubMessage message;
}
