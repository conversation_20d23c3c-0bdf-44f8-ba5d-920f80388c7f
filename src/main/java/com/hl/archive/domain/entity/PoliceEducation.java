package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 学历学位表
 */
@ApiModel(description = "学历学位表")
@Data
@TableName(value = "police_education")
public class PoliceEducation {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 学历
     */
    @TableField(value = "education_level")
    @ApiModelProperty(value = "学历")
    private String educationLevel;

    /**
     * 学校（单位）名称
     */
    @TableField(value = "school_name")
    @ApiModelProperty(value = "学校（单位）名称")
    private String schoolName;

    /**
     * 专业名称
     */
    @TableField(value = "major_name")
    @ApiModelProperty(value = "专业名称")
    private String majorName;

    /**
     * 入学时间
     */
    @TableField(value = "enrollment_date")
    @ApiModelProperty(value = "入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollmentDate;

    /**
     * 毕（肄）业时间
     */
    @TableField(value = "graduation_date")
    @ApiModelProperty(value = "毕（肄）业时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime graduationDate;

    /**
     * 学位
     */
    @TableField(value = "`degree`")
    @ApiModelProperty(value = "学位")
    private String degree;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}