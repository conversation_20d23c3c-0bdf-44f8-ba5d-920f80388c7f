package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 家庭成员表
 */
@ApiModel(description = "家庭成员表")
@Data
@TableName(value = "police_family_members")
public class PoliceFamilyMembers {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 关系名称
     */
    @TableField(value = "relationship")
    @ApiModelProperty(value = "关系名称")
    private String relationship;

    /**
     * 人员姓名
     */
    @TableField(value = "member_name")
    @ApiModelProperty(value = "人员姓名")
    private String memberName;

    /**
     * 出生年月
     */
    @TableField(value = "birth_date")
    @ApiModelProperty(value = "出生年月")
    private String birthDate;

    /**
     * 工作单位及职务
     */
    @TableField(value = "work_unit_position")
    @ApiModelProperty(value = "工作单位及职务")
    private String workUnitPosition;

    /**
     * 人员政治面貌
     */
    @TableField(value = "political_status")
    @ApiModelProperty(value = "人员政治面貌")
    private String politicalStatus;

    /**
     * 手机号码
     */
    @TableField(value = "mobile_phone")
    @ApiModelProperty(value = "手机号码")
    private String mobilePhone;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}