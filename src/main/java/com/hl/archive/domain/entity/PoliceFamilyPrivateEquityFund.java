package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 配偶子女投资私募股权投资基金情况表
 */
@ApiModel(description = "配偶子女投资私募股权投资基金情况表")
@Data
@TableName(value = "police_family_private_equity_fund")
public class PoliceFamilyPrivateEquityFund {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 投资的私募股权投资基金产品名称及编码
     */
    @TableField(value = "fund_name_code")
    @ApiModelProperty(value = "投资的私募股权投资基金产品名称及编码")
    private String fundNameCode;

    /**
     * 基金总实缴金额
     */
    @TableField(value = "total_paid_amount")
    @ApiModelProperty(value = "基金总实缴金额")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal totalPaidAmount;

    /**
     * 个人实缴金额
     */
    @TableField(value = "personal_paid_amount")
    @ApiModelProperty(value = "个人实缴金额")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal personalPaidAmount;

    /**
     * 基金投向
     */
    @TableField(value = "fund_investment_direction")
    @ApiModelProperty(value = "基金投向")
    private String fundInvestmentDirection;

    /**
     * 基金合同签署日
     */
    @TableField(value = "contract_signing_date")
    @ApiModelProperty(value = "基金合同签署日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime contractSigningDate;

    /**
     * 基金合同约定的到期日
     */
    @TableField(value = "contract_expiry_date")
    @ApiModelProperty(value = "基金合同约定的到期日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime contractExpiryDate;

    /**
     * 私募股权投资基金管理人名称及登记编号
     */
    @TableField(value = "fund_manager_name_code")
    @ApiModelProperty(value = "私募股权投资基金管理人名称及登记编号")
    private String fundManagerNameCode;

    /**
     * 是否为该基金管理人的实际控制人(0:否,1:是)
     */
    @TableField(value = "is_actual_controller")
    @ApiModelProperty(value = "是否为该基金管理人的实际控制人(0:否,1:是)")
    private Byte isActualController;

    /**
     * 是否为该基金管理人的股东（合伙人）(0:否,1:是)
     */
    @TableField(value = "is_shareholder")
    @ApiModelProperty(value = "是否为该基金管理人的股东（合伙人）(0:否,1:是)")
    private Byte isShareholder;

    /**
     * 认缴金额
     */
    @TableField(value = "subscription_amount")
    @ApiModelProperty(value = "认缴金额")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal subscriptionAmount;

    /**
     * 认缴比例
     */
    @TableField(value = "subscription_ratio")
    @ApiModelProperty(value = "认缴比例")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal subscriptionRatio;

    /**
     * 认缴时间
     */
    @TableField(value = "subscription_date")
    @ApiModelProperty(value = "认缴时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime subscriptionDate;

    /**
     * 是否担任该基金管理人高级职务(0:否,1:是)
     */
    @TableField(value = "is_senior_position")
    @ApiModelProperty(value = "是否担任该基金管理人高级职务(0:否,1:是)")
    private Byte isSeniorPosition;

    /**
     * 所担任的高级职务名称
     */
    @TableField(value = "senior_position_name")
    @ApiModelProperty(value = "所担任的高级职务名称")
    private String seniorPositionName;

    /**
     * 担任高级职务的时间
     */
    @TableField(value = "senior_position_date")
    @ApiModelProperty(value = "担任高级职务的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime seniorPositionDate;

    /**
     * 基金管理人的经营范围
     */
    @TableField(value = "manager_business_scope")
    @ApiModelProperty(value = "基金管理人的经营范围")
    private String managerBusinessScope;

    /**
     * 该企业或其他市场主体是否与报告人所在单位（系统）直接发生过商品、劳务、服务等经济关系(0:否,1:是)
     */
    @TableField(value = "has_business_relation")
    @ApiModelProperty(value = "该企业或其他市场主体是否与报告人所在单位（系统）直接发生过商品、劳务、服务等经济关系(0:否,1:是)")
    private Byte hasBusinessRelation;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}