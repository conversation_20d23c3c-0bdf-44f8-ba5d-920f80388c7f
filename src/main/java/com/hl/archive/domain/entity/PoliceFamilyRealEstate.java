package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 本人配偶子女房产情况表
 */
@ApiModel(description = "本人配偶子女房产情况表")
@Data
@TableName(value = "police_family_real_estate")
public class PoliceFamilyRealEstate {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 产权人姓名
     */
    @TableField(value = "property_owner_name")
    @ApiModelProperty(value = "产权人姓名")
    private String propertyOwnerName;

    /**
     * 房产来源
     */
    @TableField(value = "property_source")
    @ApiModelProperty(value = "房产来源")
    private String propertySource;

    /**
     * 具体地址
     */
    @TableField(value = "property_address")
    @ApiModelProperty(value = "具体地址")
    private String propertyAddress;

    /**
     * 建筑面积（㎡）
     */
    @TableField(value = "building_area")
    @ApiModelProperty(value = "建筑面积（㎡）")
    @JsonSerialize(using = ToStringSerializer.class)
    private String buildingArea;

    /**
     * 房产类型
     */
    @TableField(value = "property_type")
    @ApiModelProperty(value = "房产类型")
    private String propertyType;

    /**
     * 交易时间
     */
    @TableField(value = "transaction_date")
    @ApiModelProperty(value = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transactionDate;

    /**
     * 交易价格（万元）
     */
    @TableField(value = "transaction_price")
    @ApiModelProperty(value = "交易价格（万元）")
    private String transactionPrice;

    /**
     * 房产去向
     */
    @TableField(value = "property_disposition")
    @ApiModelProperty(value = "房产去向")
    private String propertyDisposition;

    /**
     * 出售时间
     */
    @TableField(value = "sale_date")
    @ApiModelProperty(value = "出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime saleDate;

    /**
     * 出售价格（万元）
     */
    @TableField(value = "sale_price")
    @ApiModelProperty(value = "出售价格（万元）")
    @JsonSerialize(using = ToStringSerializer.class)
    private String salePrice;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}