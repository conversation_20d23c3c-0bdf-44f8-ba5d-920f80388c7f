package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 本人配偶子女股票基金投资型保险情况表
 */
@ApiModel(description = "本人配偶子女股票基金投资型保险情况表")
@Data
@TableName(value = "police_family_securities_insurance")
public class PoliceFamilySecuritiesInsurance {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 持有人(投保人)姓名
     */
    @TableField(value = "holder_name")
    @ApiModelProperty(value = "持有人(投保人)姓名")
    private String holderName;

    /**
     * 股票(基金、投资型保险)名称和代码(保单号)
     */
    @TableField(value = "security_name_code")
    @ApiModelProperty(value = "股票(基金、投资型保险)名称和代码(保单号)")
    private String securityNameCode;

    /**
     * 持有数量(份额)
     */
    @TableField(value = "holding_quantity")
    @ApiModelProperty(value = "持有数量(份额)")
    private String holdingQuantity;

    /**
     * 填报前一交易日净值(累计缴纳保费)
     */
    @TableField(value = "net_value_premium")
    @ApiModelProperty(value = "填报前一交易日净值(累计缴纳保费)")
    private String netValuePremium;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}