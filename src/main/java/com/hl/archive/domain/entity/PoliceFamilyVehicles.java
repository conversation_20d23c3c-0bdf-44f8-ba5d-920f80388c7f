package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 本人配偶子女购置50万以上车辆表
 */
@ApiModel(description = "本人配偶子女购置50万以上车辆表")
@Data
@TableName(value = "police_family_vehicles")
public class PoliceFamilyVehicles {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 所有人姓名
     */
    @TableField(value = "owner_name")
    @ApiModelProperty(value = "所有人姓名")
    private String ownerName;

    /**
     * 车辆来源
     */
    @TableField(value = "vehicle_source")
    @ApiModelProperty(value = "车辆来源")
    private String vehicleSource;

    /**
     * 车辆品牌
     */
    @TableField(value = "vehicle_brand")
    @ApiModelProperty(value = "车辆品牌")
    private String vehicleBrand;

    /**
     * 车辆号牌
     */
    @TableField(value = "license_plate")
    @ApiModelProperty(value = "车辆号牌")
    private String licensePlate;

    /**
     * 交易金额（万元）
     */
    @TableField(value = "transaction_amount")
    @ApiModelProperty(value = "交易金额（万元）")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal transactionAmount;

    /**
     * 交易时间
     */
    @TableField(value = "transaction_date")
    @ApiModelProperty(value = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transactionDate;

    /**
     * 车辆去向
     */
    @TableField(value = "vehicle_disposition")
    @ApiModelProperty(value = "车辆去向")
    private String vehicleDisposition;

    /**
     * 出售金额（万元）
     */
    @TableField(value = "sale_amount")
    @ApiModelProperty(value = "出售金额（万元）")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal saleAmount;

    /**
     * 出售时间
     */
    @TableField(value = "sale_date")
    @ApiModelProperty(value = "出售时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime saleDate;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}