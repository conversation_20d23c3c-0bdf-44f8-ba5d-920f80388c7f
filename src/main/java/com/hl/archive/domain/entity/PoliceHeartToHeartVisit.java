package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 谈心家访记录表
 */
@ApiModel(description = "谈心家访记录表")
@Data
@TableName(value = "police_heart_to_heart_visit",autoResultMap = true)
public class PoliceHeartToHeartVisit {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;


    /**
     * 地点
     */
    @TableField(value = "`location`")
    @ApiModelProperty(value = "地点")
    private String location;

    /**
     * 原因
     */
    @TableField(value = "reason")
    @ApiModelProperty(value = "原因")
    private String reason;

    /**
     * 形式
     */
    @TableField(value = "form")
    @ApiModelProperty(value = "形式")
    private String form;

    /**
     * 谈心/家访人姓名
     */
    @TableField(value = "visitor_name")
    @ApiModelProperty(value = "谈心/家访人姓名")
    private String visitorName;

    /**
     * 谈心/家访人单位
     */
    @TableField(value = "visitor_unit")
    @ApiModelProperty(value = "谈心/家访人单位")
    private String visitorUnit;

    /**
     * 谈心/家访人职务
     */
    @TableField(value = "visitor_position")
    @ApiModelProperty(value = "谈心/家访人职务")
    private String visitorPosition;

    /**
     * 谈心 家访人 身份证
     */
    @TableField(value = "visitor_id_card")
    @ApiModelProperty(value = "谈心 家访人 身份证")
    private String visitorIdCard;

    /**
     * 被谈心/家访人姓名
     */
    @TableField(value = "visitee_name")
    @ApiModelProperty(value = "被谈心/家访人姓名")
    private String visiteeName;

    /**
     * 被谈心/家访人单位
     */
    @TableField(value = "visitee_unit")
    @ApiModelProperty(value = "被谈心/家访人单位")
    private String visiteeUnit;

    /**
     * 被谈心/家访人职务
     */
    @TableField(value = "visitee_position")
    @ApiModelProperty(value = "被谈心/家访人职务")
    private String visiteePosition;

    /**
     * 被谈心 身份证
     */
    @TableField(value = "visitee_id_card")
    @ApiModelProperty(value = "被谈心 身份证")
    private String visiteeIdCard;

    /**
     * 谈心谈话家访联系内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value = "谈心谈话家访联系内容")
    private String content;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;


    @TableField(value = "confirm_status")
    @ApiModelProperty(value = "确认状态")
    private Integer confirmStatus;


    @TableField(value = "files",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "附件")
    private JSONArray files;

}