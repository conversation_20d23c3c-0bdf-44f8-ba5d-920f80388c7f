package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 个人借贷信息表
 */
@ApiModel(description = "个人借贷信息表")
@Data
@TableName(value = "police_loan_info")
public class PoliceLoanInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 借贷信息
     */
    @TableField(value = "loan_info")
    @ApiModelProperty(value = "借贷信息")
    private String loanInfo;

    /**
     * 借贷对象名称
     */
    @TableField(value = "lender_name")
    @ApiModelProperty(value = "借贷对象名称")
    private String lenderName;

    /**
     * 借贷用途
     */
    @TableField(value = "loan_purpose")
    @ApiModelProperty(value = "借贷用途")
    private String loanPurpose;

    /**
     * 借贷金额（万元）
     */
    @TableField(value = "loan_amount")
    @ApiModelProperty(value = "借贷金额（万元）")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal loanAmount;

    /**
     * 借贷日期
     */
    @TableField(value = "loan_date")
    @ApiModelProperty(value = "借贷日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loanDate;

    /**
     * 还款期限
     */
    @TableField(value = "repayment_deadline")
    @ApiModelProperty(value = "还款期限")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repaymentDeadline;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}