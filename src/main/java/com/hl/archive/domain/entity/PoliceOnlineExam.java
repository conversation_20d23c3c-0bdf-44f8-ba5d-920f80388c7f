package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 网上考试表
 */
@ApiModel(description = "网上考试表")
@Data
@TableName(value = "police_online_exam")
public class PoliceOnlineExam {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 试卷名称
     */
    @TableField(value = "exam_paper_name")
    @ApiModelProperty(value = "试卷名称")
    private String examPaperName;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 题目个数
     */
    @TableField(value = "question_count")
    @ApiModelProperty(value = "题目个数")
    private Integer questionCount;

    /**
     * 考试时长（分）
     */
    @TableField(value = "exam_duration")
    @ApiModelProperty(value = "考试时长（分）")
    private Integer examDuration;

    /**
     * 可中断次数
     */
    @TableField(value = "interruption_count")
    @ApiModelProperty(value = "可中断次数")
    private Integer interruptionCount;

    /**
     * 可重考次数
     */
    @TableField(value = "retake_count")
    @ApiModelProperty(value = "可重考次数")
    private Integer retakeCount;

    /**
     * 提交状态
     */
    @TableField(value = "submit_status")
    @ApiModelProperty(value = "提交状态")
    private String submitStatus;

    /**
     * 得分
     */
    @TableField(value = "score")
    @ApiModelProperty(value = "得分")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal score;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}