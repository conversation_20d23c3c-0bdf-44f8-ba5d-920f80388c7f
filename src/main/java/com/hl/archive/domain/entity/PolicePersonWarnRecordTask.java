package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 人员预警关联任务记录表
 */
@ApiModel(description="人员预警关联任务记录表")
@Data
@TableName(value = "police_person_warn_record_task")
public class PolicePersonWarnRecordTask {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联预警id
     */
    @TableField(value = "warn_id")
    @ApiModelProperty(value="关联预警id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long warnId;

    /**
     * 任务类型
     */
    @TableField(value = "task_type")
    @ApiModelProperty(value="任务类型")
    private String taskType;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    @ApiModelProperty(value="任务id")
    private String taskId;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value="修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value="修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;

    /**
     * 是否删除（0-否，1-是）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除（0-否，1-是）")
    @TableLogic
    private Boolean isDeleted;
}