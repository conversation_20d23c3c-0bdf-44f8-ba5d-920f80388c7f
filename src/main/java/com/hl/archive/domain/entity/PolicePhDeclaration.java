package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 个人健康申报表
 */
@ApiModel(description="个人健康申报表")
@Data
@TableName(value = "police_ph_declaration",autoResultMap = true)
public class PolicePhDeclaration {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    @JsonSerialize(using =  ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号")
    private String idCard;

    @TableField(value = "name")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 申报时间
     */
    @TableField(value = "declaration_time")
    @ApiModelProperty(value="申报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime declarationTime;

    /**
     * 健康说明
     */
    @TableField(value = "health_description")
    @ApiModelProperty(value="健康说明")
    private String healthDescription;

    /**
     * 疾病分类（多选）代码
     */
    @TableField(value = "disease_type_codes",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="疾病分类（多选）代码")
    private List<String> diseaseTypeCodes;

    /**
     * 疾病分类 名称
     */
    @TableField(value = "disease_type_names",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="疾病分类 名称")
    private List<String> diseaseTypeNames;

    /**
     * 疾病名称
     */
    @TableField(value = "disease_name")
    @ApiModelProperty(value="疾病名称")
    private String diseaseName;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 修改人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="修改人")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updatedAt;

    /**
     * 是否删除（0-否，1-是）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除（0-否，1-是）")
    @TableLogic
    private Byte isDeleted;
}