package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 体检报告记录表
 */
@ApiModel(description="体检报告记录表")
@Data
@TableName(value = "police_physical_exam_report",autoResultMap = true)
public class PolicePhysicalExamReport {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号")
    private String idCard;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="姓名")
    private String name;

    /**
     * 申报时间
     */
    @TableField(value = "report_time")
    @ApiModelProperty(value="申报时间")
    private String reportTime;

    /**
     * 名称
     */
    @TableField(value = "disease_names",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="名称")
    private List<String> diseaseNames;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 修改人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="修改人")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updatedAt;

    /**
     * 是否删除（0-否，1-是）
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除（0-否，1-是）")
    @TableLogic
    private Byte isDeleted;
}