package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

import javax.validation.Valid;

/**
 * 星火计划个人详情-图文资料
 */
@Data
@TableName(value = "police_project_material")
public class PoliceProjectMaterial {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 图片名称
     */
    @TableField(value = "image_name")
    private String imageName;

    /**
     * 图片链接
     */
    @TableField(value = "image_url")
    @Translation(type = TransConstants.PROJECT_FILE_URL)
    private String imageUrl;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0 否，1 是）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    @TableField(value = "zjbh")
    private String zjbh;

    @TableField(value = "xhjh_zjbh")
    private String xhjhZjbh;
}