package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 星火计划个人详情-先进事迹
 */
@Data
@TableName(value = "police_project_story")
public class PoliceProjectStory {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 事迹标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 事迹内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 登记人员
     */
    @TableField(value = "registered_by")
    private String registeredBy;

    /**
     * 登记时间
     */
    @TableField(value = "register_time")
    private LocalDateTime registerTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0 否，1 是）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    @TableField(value = "zjbh")
    private String zjbh;

    @TableField(value = "xhjh_zjbh")
    private String xhjhZjbh;

}