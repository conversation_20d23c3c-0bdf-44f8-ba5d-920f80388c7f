package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 任职能力资格记录表
 */
@ApiModel(description = "任职能力资格记录表")
@Data
@TableName(value = "police_qualification_record")
public class PoliceQualificationRecord {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using =  ToStringSerializer.class)
    private Long id;



    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 单位
     */
    @TableField(value = "organization_id")
    @ApiModelProperty(value = "单位")
    private String organizationId;

    /**
     * 资格类目
     */
    @TableField(value = "category")
    @ApiModelProperty(value = "资格类目")
    private String category;

    /**
     * 具体项目
     */
    @TableField(value = "project")
    @ApiModelProperty(value = "具体项目")
    private String project;

    /**
     * 成绩
     */
    @TableField(value = "score")
    @ApiModelProperty(value = "成绩")
    private String score;

    /**
     * 记录日期
     */
    @TableField(value = "issue_date")
    @ApiModelProperty(value = "记录日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime issueDate;

    /**
     * 过期日期
     */
    @TableField(value = "expire_date")
    @ApiModelProperty(value = "过期日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    /**
     * 状态：0=有效，1=无效
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态：0=有效，1=无效")
    private Byte status;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 修改人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatedAt;

    /**
     * 是否删除：0=否，1=是
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "是否删除：0=否，1=是")
    @TableLogic
    private Byte isDeleted;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    private JSONObject personInfo;

    @TableField(value = "score_qualified")
    @ApiModelProperty(value = "是否合格")
    private String scoreQualified;
}