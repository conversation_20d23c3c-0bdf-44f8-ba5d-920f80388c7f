package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 民警标签表
 */
@ApiModel(description = "民警标签表")
@Data
@TableName(value = "police_tag_info", autoResultMap = true)
public class PoliceTagInfo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_cards", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "身份证号")
    private List<String> idCards;

    /**
     * 标签类别
     */
    @TableField(value = "tag_type")
    @ApiModelProperty(value = "标签类别")
    private String tagType;

    /**
     * 标签名称
     */
    @TableField(value = "tag_name", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "标签名称")
    private List<String> tagName;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;

    /**
     * 字典时需要的id
     */
    @TableField(value = "dict_id",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "字典时需要的id")
    private List<String> dictId;

    /**
     * 获得时间
     */
    @TableField(value = "award_date")
    @ApiModelProperty(value = "获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime awardDate;

    /**
     * 说明
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "说明")
    private String remark;


    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARDS_TO_USER_LIST, mapper = "idCards")
    private List<JSONObject> personInfoList;
}