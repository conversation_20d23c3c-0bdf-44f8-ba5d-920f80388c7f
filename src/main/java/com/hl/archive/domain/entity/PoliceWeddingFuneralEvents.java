package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 操办婚丧嫁娶表
 */
@ApiModel(description = "操办婚丧嫁娶表")
@Data
@TableName(value = "police_wedding_funeral_events")
public class PoliceWeddingFuneralEvents {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 操办事项类型
     */
    @TableField(value = "event_type")
    @ApiModelProperty(value = "操办事项类型")
    private String eventType;

    /**
     * 与当事人关系
     */
    @TableField(value = "relationship_with_party")
    @ApiModelProperty(value = "与当事人关系")
    private String relationshipWithParty;

    /**
     * 当事人姓名
     */
    @TableField(value = "party_name")
    @ApiModelProperty(value = "当事人姓名")
    private String partyName;

    /**
     * 操办日期
     */
    @TableField(value = "event_date")
    @ApiModelProperty(value = "操办日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventDate;

    /**
     * 参与人数
     */
    @TableField(value = "participant_count")
    @ApiModelProperty(value = "参与人数")
    private Integer participantCount;

    /**
     * 花费金额（万元）
     */
    @TableField(value = "expense_amount")
    @ApiModelProperty(value = "花费金额（万元）")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal expenseAmount;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;
}