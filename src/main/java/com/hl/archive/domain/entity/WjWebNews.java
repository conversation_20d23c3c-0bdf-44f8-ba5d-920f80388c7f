package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.domain.dto.WjPoliceNewsAttachmentDto;
import lombok.Data;

/**
 * 武进分局主页数据
 */
@Data
@TableName(value = "wj_web_news",autoResultMap = true)
public class WjWebNews {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 新闻ID
     */
    @TableField(value = "news_id")
    private String newsId;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 详情页URL
     */
    @TableField(value = "detail_url")
    private String detailUrl;

    /**
     * 发布时间
     */
    @TableField(value = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate publishTime;

    /**
     * 浏览数
     */
    @TableField(value = "view_count")
    private Integer viewCount;

    /**
     * 新闻内容（HTML格式）
     */
    @TableField(value = "content")
    private String content;

    /**
     * 新闻内容（纯文本）
     */
    @TableField(value = "text_content")
    private String textContent;

    /**
     * 详情附件
     */
    @TableField(value = "attachments",typeHandler = Fastjson2TypeHandler.class)
    private List<WjPoliceNewsAttachmentDto> attachments;
}