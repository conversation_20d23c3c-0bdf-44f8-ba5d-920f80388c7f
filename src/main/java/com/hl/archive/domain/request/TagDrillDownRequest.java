package com.hl.archive.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标签穿透查询请求DTO
 */
@Data
@ApiModel(description = "标签穿透查询请求DTO")
public class TagDrillDownRequest {

    /**
     * 组织ID（可选）
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 标签类型
     * dengfeng_training: 登峰训练营
     * combat_ability: 实战能力体系
     */
    @ApiModelProperty(value = "标签类型", required = true)
    private String tagType;

    /**
     * 标签名称（可选，用于查询特定标签的人员）
     */
    @ApiModelProperty(value = "标签名称")
    private String tagName;

    /**
     * 页码（从1开始）
     */
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer limit = 20;
}
