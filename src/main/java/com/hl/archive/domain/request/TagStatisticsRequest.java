package com.hl.archive.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标签统计请求DTO
 */
@Data
@ApiModel(description = "标签统计请求DTO")
public class TagStatisticsRequest {

    /**
     * 组织ID（可选，用于按部门统计）
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 标签类型（内部使用，用于单个类型查询）
     */
    private String tagType;
}
