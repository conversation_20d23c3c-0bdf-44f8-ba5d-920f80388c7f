package com.hl.archive.enums;

import lombok.Getter;

/**
 * 人员预警类别枚举
 */
@Getter
public enum PersonWarnTypeEnum {
    /** 警情 */
    SHE_JING("shejing", "涉警"),
    /** 案件 */
    SHE_AN("shean", "涉案"),
    SHE_SU("shesu","涉诉"),
    SHE_JU_BAO_TOU_SU("sjbts", "涉举报投诉"),
    CHU_RU_YU_LE_CHANG_SUO("crylcs","出入娱乐场所"),
    HUN_YIN_BIAN_DIAN("hybd","婚姻变动"),
    JIAO_TONG_WEI_ZHANG("jtwz","交通违章");



    private final String code;
    private final String label;

    PersonWarnTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

}