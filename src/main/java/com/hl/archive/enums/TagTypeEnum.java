package com.hl.archive.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标签类型枚举
 * 定义民警标签的四种类型
 */
@Getter
@AllArgsConstructor
public enum TagTypeEnum {

    /**
     * 登封训练营
     */
    DENGFENG_TRAINING("dengfeng_training", "登锋训练营", "登锋训练营相关标签", true),

    /**
     * 实战能力体系
     */
    COMBAT_ABILITY("combat_ability", "龙城英才计划", "龙城英才计划", true),

    /**
     * 重点关注
     */
    KEY_FOCUS("key_focus", "重点关注", "重点关注人员标签", true),

    /**
     * 表彰奖励
     */
    HONORS_AWARDS("honors_awards", "表彰奖励", "表彰奖励相关标签，存储在单独表中", false),

    XINGHUO_PLAN("xing_huo", "星火计划", "星火计划", false),

    JINGYING_XIANFENG("jingying_xianfeng", "警营先锋", "警营先锋", true);


    /**
     * 标签类型代码
     */
    private final String code;

    /**
     * 标签类型名称
     */
    private final String name;

    /**
     * 标签类型描述
     */
    private final String description;

    /**
     * 是否有字典值支持
     * true: 前端传值 + 字典值
     * false: 特殊处理（如表彰奖励存储在单独表中）
     */
    private final boolean hasDictSupport;

    /**
     * 根据代码获取枚举
     *
     * @param code 标签类型代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TagTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TagTypeEnum tagType : values()) {
            if (tagType.getCode().equals(code)) {
                return tagType;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 标签类型名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static TagTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (TagTypeEnum tagType : values()) {
            if (tagType.getName().equals(name)) {
                return tagType;
            }
        }
        return null;
    }

    /**
     * 获取所有支持字典的标签类型
     *
     * @return 支持字典的标签类型数组
     */
    public static TagTypeEnum[] getDictSupportedTypes() {
        return java.util.Arrays.stream(values())
                .filter(TagTypeEnum::isHasDictSupport)
                .toArray(TagTypeEnum[]::new);
    }

    /**
     * 获取所有标签类型代码
     *
     * @return 所有标签类型代码数组
     */
    public static String[] getAllCodes() {
        TagTypeEnum[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有标签类型名称
     *
     * @return 所有标签类型名称数组
     */
    public static String[] getAllNames() {
        TagTypeEnum[] values = values();
        String[] names = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            names[i] = values[i].getName();
        }
        return names;
    }

    /**
     * 验证标签类型代码是否有效
     *
     * @param code 标签类型代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 验证标签类型名称是否有效
     *
     * @param name 标签类型名称
     * @return true表示有效，false表示无效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }
}
