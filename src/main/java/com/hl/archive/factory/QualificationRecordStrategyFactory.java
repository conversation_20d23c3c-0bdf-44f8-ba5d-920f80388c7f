package com.hl.archive.factory;

import com.hl.archive.strategy.QualificationRecordStrategy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QualificationRecordStrategyFactory {


    private final Map<String, QualificationRecordStrategy> strategyMap = new HashMap<>();

    public QualificationRecordStrategyFactory(List<QualificationRecordStrategy> strategies) {
        for (QualificationRecordStrategy strategy : strategies) {
            strategyMap.put(strategy.getProject(), strategy);
        }
    }

    public QualificationRecordStrategy getStrategy(String project) {
        return strategyMap.get(project);
    }

}
