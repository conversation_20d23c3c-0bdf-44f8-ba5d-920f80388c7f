package com.hl.archive.feign;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.common.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "hl-task")
public interface TaskApi {


    @PostMapping("/task/info/one")
    R<?> getOneTask(@RequestHeader("token") String token,
                    @RequestBody JSONObject param);

    @PostMapping("/task/info/list")
    R<JSONObject> getTaskList(@RequestHeader("token") String token,
                              @RequestBody JSONObject param);

    @PostMapping("/task/info/add")
    R<JSONArray> add(@RequestHeader(name = "token") String Token, @RequestBody JSONObject params);

    @PostMapping("/task/info/audit")
    R<JSONArray> audit(@RequestHeader(name = "token") String Token, @RequestBody JSONObject params);


}
