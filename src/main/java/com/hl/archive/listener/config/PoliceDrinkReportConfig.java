package com.hl.archive.listener.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "extend-task.drink-report")
@Data
@Component
public class PoliceDrinkReportConfig {
    private String configUuid;
    private String drinkTime;
    private String reason;
    private String location;
    private String inviter;
    private String participants;
    private String payer;
    private String travelMode;
    private String remark;
}
