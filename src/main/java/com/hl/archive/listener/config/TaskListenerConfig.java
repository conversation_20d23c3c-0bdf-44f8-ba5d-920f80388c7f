package com.hl.archive.listener.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 任务监听器配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "task-listener")
public class TaskListenerConfig {
    
    /**
     * 是否启用任务监听器
     */
    private boolean enabled = true;
    
    /**
     * 监听的队列名称
     */
    private String queueName = "wj_task_archive_s";
}
