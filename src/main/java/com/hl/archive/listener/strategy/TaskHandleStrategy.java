package com.hl.archive.listener.strategy;

import com.alibaba.fastjson2.JSONObject;

/**
 * 任务处理策略接口
 */
public interface TaskHandleStrategy {
    
    /**
     * 处理任务数据
     * 
     * @param contentData 任务内容数据
     * @param opt 操作类型
     */
    void handle(JSONObject contentData, String opt);
    
    /**
     * 获取支持的配置UUID
     * 
     * @return 配置UUID
     */
    String getSupportedConfigUuid();
}
