package com.hl.archive.listener.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务处理策略工厂
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskHandleStrategyFactory {
    
    private final List<TaskHandleStrategy> strategies;
    private final Map<String, TaskHandleStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        for (TaskHandleStrategy strategy : strategies) {
            String configUuid = strategy.getSupportedConfigUuid();
            strategyMap.put(configUuid, strategy);
            log.info("注册任务处理策略: {} -> {}", configUuid, strategy.getClass().getSimpleName());
        }
    }
    
    /**
     * 根据配置UUID获取对应的处理策略
     * 
     * @param configUuid 配置UUID
     * @return 处理策略，如果没有找到则返回null
     */
    public TaskHandleStrategy getStrategy(String configUuid) {
        return strategyMap.get(configUuid);
    }
    
    /**
     * 检查是否支持指定的配置UUID
     * 
     * @param configUuid 配置UUID
     * @return 是否支持
     */
    public boolean isSupported(String configUuid) {
        return strategyMap.containsKey(configUuid);
    }
}
