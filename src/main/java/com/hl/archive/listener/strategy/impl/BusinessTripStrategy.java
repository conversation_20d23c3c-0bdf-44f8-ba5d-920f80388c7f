package com.hl.archive.listener.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.event.RefreshDutyStatusEvent;
import com.hl.archive.listener.event.RefreshDutyStatusWithDeleteEvent;
import com.hl.archive.service.LCBBService;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 出差处理策略
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BusinessTripStrategy implements TaskHandleStrategy {

    private final ApplicationEventPublisher eventPublisher;
    private final LCBBService lcbbService;

    @Override
    public void handle(JSONObject contentData, String opt) {
        log.info("处理出差任务，操作类型: {}", opt);

        if ("audit".equals(opt)) {
            eventPublisher.publishEvent(new RefreshDutyStatusEvent(contentData));
            lcbbService.addLCBB(contentData, getSupportedConfigUuid());
        } else if ("delete".equals(opt)) {
            eventPublisher.publishEvent(new RefreshDutyStatusWithDeleteEvent(contentData));
        }
    }

    @Override
    public String getSupportedConfigUuid() {
        return "CG9Z9HJ3FJW";
    }
}
