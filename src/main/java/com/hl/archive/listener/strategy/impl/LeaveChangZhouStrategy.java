package com.hl.archive.listener.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import com.hl.archive.service.PoliceLeaveRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LeaveChangZhouStrategy implements TaskHandleStrategy {

    private PoliceLeaveRecordService policeLeaveRecordService;

    @Override
    public void handle(JSONObject contentData, String opt) {

        log.info("处理离常报备任务 操作类型{}", opt);
        if ("audit".equals(opt)){
//            policeLeaveRecordService.saveLeaveChangZhouRecord(contentData);
        }


    }

    @Override
    public String getSupportedConfigUuid() {
        return "C09ZZEL29S8";
    }
}
