package com.hl.archive.listener.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.event.PoliceOverseasTravelApproveEvent;
import com.hl.archive.listener.event.PoliceOverseasTravelDeleteEvent;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 因私出国处理策略
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OverseasTravelStrategy implements TaskHandleStrategy {
    
    private final ApplicationEventPublisher eventPublisher;
    
    @Override
    public void handle(JSONObject contentData, String opt) {
        log.info("处理因私出国任务，操作类型: {}", opt);
        
        if ("audit".equals(opt)) {
            eventPublisher.publishEvent(new PoliceOverseasTravelApproveEvent(contentData));
        } else if ("delete".equals(opt)) {
            eventPublisher.publishEvent(new PoliceOverseasTravelDeleteEvent(contentData));
        }
    }
    
    @Override
    public String getSupportedConfigUuid() {
        return "CKSWWQQWW7H";
    }
}
