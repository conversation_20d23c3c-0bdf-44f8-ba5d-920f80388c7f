package com.hl.archive.listener.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.event.PoliceAbilityTagDeleteEvent;
import com.hl.archive.listener.event.PoliceAbilityTagEvent;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 职业能力处理策略
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PoliceAbilityStrategy implements TaskHandleStrategy {
    
    private final ApplicationEventPublisher eventPublisher;
    
    @Override
    public void handle(JSONObject contentData, String opt) {
        log.info("处理职业能力任务，操作类型: {}", opt);
        
        if ("audit".equals(opt)) {
            eventPublisher.publishEvent(new PoliceAbilityTagEvent(contentData));
        } else if ("delete".equals(opt)) {
            eventPublisher.publishEvent(new PoliceAbilityTagDeleteEvent(contentData));
        }
    }
    
    @Override
    public String getSupportedConfigUuid() {
        return "CNE2TZD2GTE";
    }
}
