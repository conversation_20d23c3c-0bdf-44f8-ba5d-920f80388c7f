package com.hl.archive.listener.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.config.PoliceClubActivityConfig;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import com.hl.archive.service.PoliceClubActivityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 社团活动任务处理策略
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PoliceClubActivityStrategy implements TaskHandleStrategy {

    private final PoliceClubActivityService policeClubActivityService;

    @Override
    public void handle(JSONObject contentData, String opt) {
        log.info("处理社团活动任务，操作类型: {}", opt);
        if ("audit".equals(opt)) {
            String customId = contentData.getByPath("data.custom_id").toString();
            if ("ZZCSP".equals(customId)) {
                policeClubActivityService.saveClubActivity(contentData);
            }
            if ("ZZCSPJG".equals(customId)) {
                policeClubActivityService.updateActivityStatusEnd(contentData);
            }
        } else if ("delete".equals(opt)) {
            policeClubActivityService.deleteClubActivity(contentData);
        } else if ("work".equals(opt)) {
            policeClubActivityService.updateClubActivityMedia(contentData);
        } else if ("add".equals(opt)) {
            policeClubActivityService.addTaskEventHandle(contentData);
        }
    }

    @Override
    public String getSupportedConfigUuid() {
        return "CXQNJMIAR1C";
    }
}
