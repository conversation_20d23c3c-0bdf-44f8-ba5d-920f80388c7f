package com.hl.archive.listener.strategy.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceDrinkReportConfig;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import com.hl.archive.service.PoliceDrinkReportService;
import com.hl.common.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * 饮酒报备处理策略
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PoliceDrinkReportStrategy implements TaskHandleStrategy {

    private final PoliceDrinkReportService policeDrinkReportService;





    @Override
    public void handle(JSONObject contentData, String opt) {
        log.info("处理饮酒报备任务，操作类型: {}", opt);
        if ("audit".equals(opt)) {
            policeDrinkReportService.saveDrinkReport(contentData);
        }
        if ("delete".equals(opt)) {
            policeDrinkReportService.deleteDrinkReport(contentData);
        }
    }

    @Override
    public String getSupportedConfigUuid() {
        return "C30NIBEJEGI";
    }
}
