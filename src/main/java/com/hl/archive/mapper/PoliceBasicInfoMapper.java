package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceStatisticsDTO;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PoliceBasicInfoMapper extends BaseMapper<PoliceBasicInfo> {
    List<PoliceStatisticsDTO> getPoliceStatisticsByDepartment();

    PoliceStatisticsDTO getPoliceStatisticsByOrgId(StatisticsQueryRequest request);

    /**
     * 统计数字穿透查询 - 分页查询人员基本信息
     */
    Page<PoliceBasicInfo> getPoliceListByStatisticsType(Page<PoliceBasicInfo> page, StatisticsDrillDownRequest request);


}