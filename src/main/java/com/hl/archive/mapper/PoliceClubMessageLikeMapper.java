package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.entity.PoliceClubMessageLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 留言点赞Mapper
 */
@Mapper
public interface PoliceClubMessageLikeMapper extends BaseMapper<PoliceClubMessageLike> {

    /**
     * 查询用户对指定留言的点赞记录
     *
     * @param messageId 留言ID
     * @param userIdCard 用户身份证号
     * @return 点赞记录
     */
    PoliceClubMessageLike selectByMessageIdAndUserId(@Param("messageId") Long messageId, 
                                                    @Param("userIdCard") String userIdCard);

    /**
     * 删除用户对指定留言的点赞记录
     *
     * @param messageId 留言ID
     * @param userIdCard 用户身份证号
     * @return 影响行数
     */
    int deleteByMessageIdAndUserId(@Param("messageId") Long messageId, 
                                  @Param("userIdCard") String userIdCard);

    /**
     * 查询留言的点赞用户列表
     *
     * @param messageId 留言ID
     * @param limit 限制数量
     * @return 点赞用户列表
     */
    List<PoliceClubMessageLike> selectLikeUsersByMessageId(@Param("messageId") Long messageId, 
                                                           @Param("limit") Integer limit);

    /**
     * 统计留言的点赞数量
     *
     * @param messageId 留言ID
     * @return 点赞数量
     */
    int countLikesByMessageId(@Param("messageId") Long messageId);

    /**
     * 批量查询用户的点赞状态
     *
     * @param messageIds 留言ID列表
     * @param userIdCard 用户身份证号
     * @return 已点赞的留言ID列表
     */
    List<Long> selectUserLikedMessageIds(@Param("messageIds") List<Long> messageIds, 
                                        @Param("userIdCard") String userIdCard);
}
