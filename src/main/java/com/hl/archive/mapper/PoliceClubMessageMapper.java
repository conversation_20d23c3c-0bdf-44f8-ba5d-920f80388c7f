package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.MessageQueryDTO;
import com.hl.archive.domain.entity.PoliceClubMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 社团留言Mapper
 */
@Mapper
public interface PoliceClubMessageMapper extends BaseMapper<PoliceClubMessage> {

    /**
     * 分页查询留言列表（包含点赞状态）
     *
     * @param page 分页对象
     * @param dto  查询条件
     * @return 留言列表
     */
    Page<PoliceClubMessage> selectMessagePage(@Param("page") Page<PoliceClubMessage> page, @Param("dto") MessageQueryDTO dto);

    /**
     * 查询留言的回复列表
     *
     * @param parentId 父留言ID
     * @param currentUserIdCard 当前用户身份证号
     * @param limit 限制数量
     * @return 回复列表
     */
    List<PoliceClubMessage> selectRepliesByParentId(@Param("parentId") Long parentId, 
                                                    @Param("currentUserIdCard") String currentUserIdCard,
                                                    @Param("limit") Integer limit);

    /**
     * 查询用户在指定留言的点赞状态
     *
     * @param messageIds 留言ID列表
     * @param userIdCard 用户身份证号
     * @return 已点赞的留言ID列表
     */
    List<Long> selectUserLikedMessageIds(@Param("messageIds") List<Long> messageIds, 
                                        @Param("userIdCard") String userIdCard);

    /**
     * 更新留言的点赞数
     *
     * @param messageId 留言ID
     * @param increment 增量（正数为增加，负数为减少）
     * @return 影响行数
     */
    int updateLikeCount(@Param("messageId") Long messageId, @Param("increment") int increment);

    /**
     * 更新留言的回复数
     *
     * @param messageId 留言ID
     * @param increment 增量（正数为增加，负数为减少）
     * @return 影响行数
     */
    int updateReplyCount(@Param("messageId") Long messageId, @Param("increment") int increment);

    /**
     * 查询留言详情（包含关联信息）
     *
     * @param messageId 留言ID
     * @param currentUserIdCard 当前用户身份证号
     * @return 留言详情
     */
    PoliceClubMessage selectMessageDetail(@Param("messageId") Long messageId, 
                                         @Param("currentUserIdCard") String currentUserIdCard);

    /**
     * 批量查询留言的回复数量
     *
     * @param messageIds 留言ID列表
     * @return 留言ID和回复数量的映射
     */
    List<PoliceClubMessage> selectReplyCountByMessageIds(@Param("messageIds") List<Long> messageIds);
}
