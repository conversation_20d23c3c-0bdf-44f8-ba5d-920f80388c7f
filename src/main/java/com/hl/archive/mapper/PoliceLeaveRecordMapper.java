package com.hl.archive.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.entity.PoliceLeaveRecord;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PoliceLeaveRecordMapper extends BaseMapper<PoliceLeaveRecord> {

    @Select("select id_card,leave_type from police_leave_record where start_date <= #{startDate} and end_date >= #{endDate} and is_deleted = 0  group by " +
            " id_card,leave_type")
    List<JSONObject> queryLeaveRecord(String startDate, String endDate);
}