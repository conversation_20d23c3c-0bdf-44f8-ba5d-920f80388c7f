package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.dto.PoliceQualificationRecordRequestDTO;
import com.hl.archive.domain.entity.PoliceQualificationRecord;

import java.util.List;

public interface PoliceQualificationRecordMapper extends BaseMapper<PoliceQualificationRecord> {

    List<PoliceQualificationRecord> queryLatestRecord(PoliceQualificationRecordRequestDTO requestDTO);
}