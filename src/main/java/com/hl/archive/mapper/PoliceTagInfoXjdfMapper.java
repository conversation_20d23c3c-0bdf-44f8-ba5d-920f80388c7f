package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfReturnDTO;
import com.hl.archive.domain.entity.PoliceTagInfoXjdf;
import org.apache.ibatis.annotations.Param;

public interface PoliceTagInfoXjdfMapper extends BaseMapper<PoliceTagInfoXjdf> {
    Page<PoliceTagInfoXjdfReturnDTO> listTag(@Param("page") Page<PoliceTagInfoXjdfReturnDTO> objectPage,
                                             @Param("request") PoliceTagInfoXjdfQueryDTO dto);
}