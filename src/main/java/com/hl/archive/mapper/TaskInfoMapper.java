package com.hl.archive.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.hl.common.config.datasource.DataSource;
import com.hl.common.config.datasource.DataSourceType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DataSource(value = DataSourceType.DATASOURCE2)
public interface TaskInfoMapper {

    List<JSONObject> queryCcTask(@Param("request") JSONObject data);
}
