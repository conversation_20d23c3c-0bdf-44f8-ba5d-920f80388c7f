package com.hl.archive.search.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 搜索配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "search")
public class SearchConfig {

    /**
     * 是否启用搜索功能
     */
    private boolean enabled = true;

    /**
     * 同步配置
     */
    private SyncConfig sync = new SyncConfig();

    /**
     * 搜索配置
     */
    private QueryConfig query = new QueryConfig();

    /**
     * 高亮配置
     */
    private HighlightConfig highlight = new HighlightConfig();

    @Data
    public static class SyncConfig {
        /**
         * 是否启用定时同步
         */
        private boolean enabled = false;

        /**
         * 批量同步大小
         */
        private int batchSize = 1000;

        /**
         * 同步超时时间（秒）
         */
        private int timeout = 300;

        /**
         * 是否在启动时自动同步
         */
        private boolean autoSyncOnStartup = false;
    }

    @Data
    public static class QueryConfig {
        /**
         * 默认页大小
         */
        private int defaultPageSize = 20;

        /**
         * 最大页大小
         */
        private int maxPageSize = 100;

        /**
         * 默认模糊度
         */
        private int defaultFuzziness = 2;

        /**
         * 搜索超时时间（毫秒）
         */
        private int timeout = 5000;

        /**
         * 是否启用缓存
         */
        private boolean cacheEnabled = true;

        /**
         * 缓存过期时间（秒）
         */
        private int cacheExpireTime = 300;
    }

    @Data
    public static class HighlightConfig {
        /**
         * 高亮前缀标签
         */
        private String preTag = "<em class=\"highlight\">";

        /**
         * 高亮后缀标签
         */
        private String postTag = "</em>";

        /**
         * 高亮片段大小
         */
        private int fragmentSize = 100;

        /**
         * 高亮片段数量
         */
        private int numberOfFragments = 3;
    }
}
