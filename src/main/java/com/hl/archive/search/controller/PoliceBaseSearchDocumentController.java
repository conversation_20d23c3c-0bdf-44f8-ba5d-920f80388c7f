package com.hl.archive.search.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.search.document.PoliceBaseSearchDocument;
import com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper;
import com.hl.archive.search.service.PoliceBaseSearchDocumentService;
import com.hl.archive.search.service.PoliceDataSyncService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.elasticsearch.core.Map;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/base-search")
@RequiredArgsConstructor
@Api(tags = "聚合搜索功能")
public class PoliceBaseSearchDocumentController {

    private final PoliceBaseSearchDocumentMapper policeBaseSearchDocumentMapper;

    private final PoliceDataSyncService policeDataSyncService;

    @GetMapping("/createIndex")
    public R<?> createIndex() {
        Boolean b = policeBaseSearchDocumentMapper.deleteIndex();
        Boolean index = policeBaseSearchDocumentMapper.createIndex();
        return R.ok(index);
    }

    @GetMapping("/sync")
    public R<?> sync() {
        policeDataSyncService.syncPoliceBasicInfo();
        return R.ok();
    }

    @PostMapping("/search")
    public R<?> search(@RequestBody JSONObject jsonObject) {
        String query = jsonObject.getString("query");
        Integer page = jsonObject.getInteger("page");
        Integer limit = jsonObject.getInteger("limit");
        LambdaEsQueryWrapper<PoliceBaseSearchDocument> queryWrapper = new LambdaEsQueryWrapper<>();

        queryWrapper.like(PoliceBaseSearchDocument::getName, query, 10.0f)
                .or().match(PoliceBaseSearchDocument::getContent, query, 1.0f);
        if (StrUtil.isNotBlank(jsonObject.getString("organizationId")) && !"320412000000".equals(jsonObject.getString("organizationId"))) {
            queryWrapper.likeRight(PoliceBaseSearchDocument::getOrganizationId, jsonObject.getString("organizationId").substring(0, 8));
        }
        EsPageInfo<PoliceBaseSearchDocument> policeBaseSearchDocumentEsPageInfo = policeBaseSearchDocumentMapper.pageQuery(queryWrapper, page, limit);
        return R.ok(policeBaseSearchDocumentEsPageInfo.getList(), (int) policeBaseSearchDocumentEsPageInfo.getTotal());
    }
}
