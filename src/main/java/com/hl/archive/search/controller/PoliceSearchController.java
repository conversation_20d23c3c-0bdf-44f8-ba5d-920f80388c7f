//package com.hl.archive.search.controller;
//
//import com.hl.archive.search.document.PoliceSearchDocument;
//import com.hl.archive.search.dto.SearchRequest;
//import com.hl.archive.search.enums.DataTypeEnum;
//import com.hl.archive.search.service.DataSyncService;
//import com.hl.archive.search.service.PoliceSearchService;
//import com.hl.common.domain.R;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.easyes.core.biz.EsPageInfo;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Arrays;
//import java.util.List;
//
///**
// * 民警搜索控制器
// */
//@Slf4j
//@RestController
//@RequestMapping("/search")
//@Api(tags = "民警搜索接口")
//public class PoliceSearchController {
//
//    @Autowired
//    private PoliceSearchService policeSearchService;
//
//    @Autowired
//    private DataSyncService dataSyncService;
//
//    @PostMapping("/search")
//    @ApiOperation("搜索")
//    public R<List<PoliceSearchDocument>> search(@RequestBody SearchRequest request) {
//        try {
//            log.info("收到搜索请求：{}", request.getKeyword());
//            EsPageInfo<PoliceSearchDocument> search = policeSearchService.search(request);
//            return R.ok(search.getList(), (int) search.getTotal());
//        } catch (Exception e) {
//            log.error("搜索失败", e);
//            return R.fail("搜索失败：" + e.getMessage());
//        }
//    }
//
//
//
//
//    @GetMapping("/dataTypes")
//    @ApiOperation("获取所有数据类型")
//    public R<List<DataTypeEnum>> getDataTypes() {
//        try {
//            List<DataTypeEnum> dataTypes = Arrays.asList(DataTypeEnum.values());
//            return R.ok(dataTypes);
//        } catch (Exception e) {
//            log.error("获取数据类型失败", e);
//            return R.fail("获取数据类型失败：" + e.getMessage());
//        }
//    }
//
//    @PostMapping("/createIndex")
//    @ApiOperation("创建搜索索引")
//    public R<Boolean> createIndex() {
//        try {
//            policeSearchService.deleteIndex();
//            boolean result = policeSearchService.createIndex();
//            if (result) {
//                return R.ok(true);
//            } else {
//                return R.fail("索引创建失败");
//            }
//        } catch (Exception e) {
//            log.error("创建索引失败", e);
//            return R.fail("创建索引失败：" + e.getMessage());
//        }
//    }
//
//    @DeleteMapping("/deleteIndex")
//    @ApiOperation("删除搜索索引")
//    public R<Boolean> deleteIndex() {
//        try {
//            boolean result = policeSearchService.deleteIndex();
//            if (result) {
//                return R.ok(true);
//            } else {
//                return R.fail("索引删除失败");
//            }
//        } catch (Exception e) {
//            log.error("删除索引失败", e);
//            return R.fail("删除索引失败：" + e.getMessage());
//        }
//    }
//
//    @GetMapping("/existsIndex")
//    @ApiOperation("检查索引是否存在")
//    public R<Boolean> existsIndex() {
//        try {
//            boolean exists = policeSearchService.existsIndex();
//            return R.ok(exists);
//        } catch (Exception e) {
//            log.error("检查索引是否存在失败", e);
//            return R.fail("检查索引是否存在失败：" + e.getMessage());
//        }
//    }
//
//    @PostMapping("/sync/all")
//    @ApiOperation("全量同步所有数据")
//    public R<DataSyncService.SyncResult> syncAllData() {
//        try {
//            DataSyncService.SyncResult result = dataSyncService.syncAllData();
//            if (result.isSuccess()) {
//                return R.ok(result);
//            } else {
//                return R.fail(result.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("全量同步失败", e);
//            return R.fail("全量同步失败：" + e.getMessage());
//        }
//    }
//
//    @PostMapping("/sync/type/{dataType}")
//    @ApiOperation("同步指定数据类型")
//    public R<DataSyncService.SyncResult> syncDataByType(@PathVariable String dataType) {
//        try {
//            DataSyncService.SyncResult result = dataSyncService.syncDataByType(dataType);
//            if (result.isSuccess()) {
//                return R.ok(result);
//            } else {
//                return R.fail(result.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("数据类型同步失败，dataType：{}", dataType, e);
//            return R.fail("数据类型同步失败：" + e.getMessage());
//        }
//    }
//
//    @PostMapping("/sync/idCard/{idCard}")
//    @ApiOperation("同步指定人员的所有数据")
//    public R<DataSyncService.SyncResult> syncDataByIdCard(@PathVariable String idCard) {
//        try {
//            DataSyncService.SyncResult result = dataSyncService.syncDataByIdCard(idCard);
//            if (result.isSuccess()) {
//                return R.ok(result);
//            } else {
//                return R.fail(result.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("人员数据同步失败，idCard：{}", idCard, e);
//            return R.fail("人员数据同步失败：" + e.getMessage());
//        }
//    }
//
//    @PostMapping("/sync/rebuild")
//    @ApiOperation("重建索引")
//    public R<DataSyncService.SyncResult> rebuildIndex() {
//        try {
//            DataSyncService.SyncResult result = dataSyncService.rebuildIndex();
//            if (result.isSuccess()) {
//                return R.ok(result);
//            } else {
//                return R.fail(result.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("重建索引失败", e);
//            return R.fail("重建索引失败：" + e.getMessage());
//        }
//    }
//
//    @GetMapping("/sync/status")
//    @ApiOperation("获取同步状态")
//    public R<DataSyncService.SyncStatus> getSyncStatus() {
//        try {
//            DataSyncService.SyncStatus status = dataSyncService.getSyncStatus();
//            return R.ok(status);
//        } catch (Exception e) {
//            log.error("获取同步状态失败", e);
//            return R.fail("获取同步状态失败：" + e.getMessage());
//        }
//    }
//
//    @DeleteMapping("/document/{dataType}/{recordId}")
//    @ApiOperation("删除指定文档")
//    public R<Boolean> deleteDocument(@PathVariable String dataType, @PathVariable Long recordId) {
//        try {
//            boolean result = dataSyncService.deleteDocument(dataType, recordId);
//            if (result) {
//                return R.ok(true);
//            } else {
//                return R.fail("文档删除失败");
//            }
//        } catch (Exception e) {
//            log.error("删除文档失败，dataType：{}，recordId：{}", dataType, recordId, e);
//            return R.fail("删除文档失败：" + e.getMessage());
//        }
//    }
//
//    @DeleteMapping("/documents/idCard/{idCard}")
//    @ApiOperation("删除指定身份证号的所有文档")
//    public R<Long> deleteDocumentsByIdCard(@PathVariable String idCard) {
//        try {
//            long deletedCount = dataSyncService.deleteDocumentsByIdCard(idCard);
//            return R.ok(deletedCount);
//        } catch (Exception e) {
//            log.error("删除身份证号文档失败，idCard：{}", idCard, e);
//            return R.fail("删除文档失败：" + e.getMessage());
//        }
//    }
//}
