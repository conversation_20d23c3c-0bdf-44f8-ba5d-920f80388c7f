package com.hl.archive.search.controller;

import com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper;
import com.hl.common.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/viewArchiveCaseTaskInfo")
@RequiredArgsConstructor
public class ViewArchiveCaseTaskInfoDocumentController {

    private final ViewCaseInfoTaskDocumentMapper viewCaseInfoTaskDocumentMapper;

    @GetMapping("/createIndex")
    public R<?> createIndex() {
        viewCaseInfoTaskDocumentMapper.deleteIndex();
        Boolean index = viewCaseInfoTaskDocumentMapper.createIndex();
        return R.ok(index);
    }
}
