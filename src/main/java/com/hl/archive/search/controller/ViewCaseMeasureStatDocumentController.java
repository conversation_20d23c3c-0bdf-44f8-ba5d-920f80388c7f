package com.hl.archive.search.controller;

import com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper;
import com.hl.common.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/viewCaseMeasureStat")
@Slf4j
@RequiredArgsConstructor
public class ViewCaseMeasureStatDocumentController {

    private final ViewCaseMeasureStatDocumentMapper viewCaseMeasureStatDocumentMapper;

    @GetMapping("/createIndex")
    public R<?> createIndex() {
        viewCaseMeasureStatDocumentMapper.deleteIndex();
        viewCaseMeasureStatDocumentMapper.createIndex();
        return R.ok();
    }
}
