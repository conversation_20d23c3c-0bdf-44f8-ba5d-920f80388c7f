package com.hl.archive.search.document;

import lombok.Data;
import org.dromara.easyes.annotation.HighLight;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;

import java.util.Map;

@Data
@IndexName
public class PoliceBaseSearchDocument {


    @IndexId
    private String id;

    /**
     * 数据类型/分类，用于区分不同的表数据
     * 如：basic_info, education, training, family_members 等
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String dataType;

    /**
     * 数据类型中文名称，用于前端展示
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String dataTypeName;




    @IndexField(fieldType = FieldType.LONG)
    private Long recordId;


    @IndexField(fieldType = FieldType.TEXT)
    @HighLight(mappingField = "highlightContent")
    private String content;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String idCard;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String name;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String imgUrl;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String policeNumber;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String department;


    @IndexField(fieldType = FieldType.KEYWORD)
    private String leadershipLevel;


    @IndexField(fieldType = FieldType.KEYWORD)
    private String positionName;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String organizationId;


    private String highlightContent;

}
