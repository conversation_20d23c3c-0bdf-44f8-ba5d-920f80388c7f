package com.hl.archive.search.document;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 民警搜索统一文档
 * 将所有民警相关数据聚合到一个搜索文档中，便于统一搜索和展示
 */
@Data
@IndexName()
public class PoliceSearchDocument {

    /**
     * 文档唯一ID，格式：{dataType}_{recordId}
     */
    @IndexId
    private String id;

    /**
     * 数据类型/分类，用于区分不同的表数据
     * 如：basic_info, education, training, family_members 等
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String dataType;

    /**
     * 数据类型中文名称，用于前端展示
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String dataTypeName;

    /**
     * 记录ID，原始数据表中的主键
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long recordId;

    /**
     * 身份证号，用于关联人员信息
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String idCard;

    // ==================== 人员基本信息 ====================
    /**
     * 姓名
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String name;


    // ==================== 搜索内容字段 ====================
    /**
     * 主要搜索内容，包含该记录的核心信息
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String mainContent;

    /**
     * 次要搜索内容，包含该记录的补充信息
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String subContent;


    /**
     * 扩展字段，存储原始数据的JSON格式，用于详情展示
     */
    @IndexField(fieldType =  FieldType.OBJECT,nestedOrObjectClass = Map.class)
    private Map<String, Object> originalData;

    /**
     * 创建时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 数据状态：1-正常，0-删除
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer status;


    /**
     * 备注信息
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String remark;
}
