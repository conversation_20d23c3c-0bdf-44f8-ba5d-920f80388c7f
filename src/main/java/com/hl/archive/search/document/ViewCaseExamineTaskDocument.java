package com.hl.archive.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;


import java.util.Date;

@Data
@IndexName(value = "view_case_examine_task")
public class ViewCaseExamineTaskDocument {
    /**
     * 唯一id
     */
    @IndexId(value = "id")
    private String id;

    /**
     * 案件编号
     */
    @IndexField(value = "case_no")
    private String caseNo;

    /**
     * 案件名称
     */
    @IndexField(value = "case_name")
    private String caseName;

    /**
     * 案件考评分类,0表示案件考评，1表示场所考评
     */
    @IndexField(value = "examine_classify")
    private Integer examineClassify;

    /**
     * 考评日期
     */
    @IndexField(value = "examine_date")
    private Date examineDate;

    /**
     * 层级考评
     */
    @IndexField(value = "level_examine")
    private String levelExamine;

    /**
     * 执法风险点
     */
    @IndexField(value = "handle_risk")
    private String handleRisk;

    /**
     * 执法环节
     */
    @IndexField(value = "handle_unit")
    private String handleUnit;

    /**
     * 执法问题说明
     */
    @IndexField(value = "handle_problem")
    private String handleProblem;

    /**
     * 是否列入考核
     */
    @IndexField(value = "`access`")
    private Integer access;

    /**
     * 主办人、整改人
     */
    @IndexField(value = "major_police")
    private String majorPolice;

    /**
     * 整改单位
     */
    @IndexField(value = "rectify_organ")
    private String rectifyOrgan;

    /**
     * 问题描述
     */
    @IndexField(value = "description")
    private String description;

    /**
     * 数据来源0 抓取 1 人工添加
     */
    @IndexField(value = "source_type")
    private Integer sourceType;

    /**
     * 标号
     */
    @IndexField(value = "task_id")
    private String taskId;

    /**
     * 标题
     */
    @IndexField(value = "title")
    private String title;

    /**
     * 1红，2橙，3黄，4蓝
     */
    @IndexField(value = "`level`")
    private Integer level;

    /**
     * 1：一次性任务，2：需要多次完成
     */
    @IndexField(value = "`mode`")
    private Integer mode;

    /**
     * 内容
     */
    @IndexField(value = "content")
    private String content;

    /**
     * 人员
     */
    @IndexField(value = "persons")
    private String persons;

    /**
     * 任务开始时间
     */
    @IndexField(value = "work_starttime")
    private String workStarttime;

    /**
     * 任务结束时间
     */
    @IndexField(value = "work_endtime")
    private String workEndtime;

    /**
     * 项目相关
     */
    @IndexField(value = "project_id")
    private String projectId;

    /**
     * SSO中项目编号
     */
    @IndexField(value = "sso_project")
    private String ssoProject;

    /**
     * 创建人员（如果系统字段，为空）
     */
    @IndexField(value = "police_id")
    private String policeId;

    /**
     * 是否对附件可见，内容如下：{"invisible": ["_work"]}
     */
    @IndexField(value = "permission")
    private String permission;

    /**
     * 任务所属单位
     */
    @IndexField(value = "organization")
    private String organization;

    /**
     * 涉及的相关数据
     */
    @IndexField(value = "relation_data")
    private String relationData;

    /**
     * 附带信息
     */
    @IndexField(value = "annex_info")
    private String annexInfo;

    /**
     * 当前第几步骤
     */
    @IndexField(value = "person_level")
    private Integer personLevel;

    /**
     * 第几次循环工作
     */
    @IndexField(value = "result_level")
    private Integer resultLevel;

    /**
     * 当前状态， -1撤销， 0未开始，1未完成，10已完成
     */
    @IndexField(value = "`status`")
    private Integer status;

    /**
     * 0：未超时，1：已超时
     */
    @IndexField(value = "timeout")
    private Integer timeout;

    /**
     * 任务处理层级
     */
    @IndexField(value = "handle_level")
    private Integer handleLevel;

    /**
     * 流转时间
     */
    @IndexField(value = "last_time")
    private Date lastTime;

    /**
     * 业务子类型
     */
    @IndexField(value = "sub_business_type")
    private Integer subBusinessType;

    /**
     * 业务分类
     */
    @IndexField(value = "business_classify")
    private String businessClassify;

    /**
     * 父任务ID
     */
    @IndexField(value = "parent_id")
    private String parentId;

    @IndexField(value = "finish")
    private Integer finish;

    @IndexField(value = "work_person")
    private String workPerson;

    @IndexField(value = "cowork_person")
    private String coworkPerson;

    @IndexField(value = "approve_person")
    private String approvePerson;
}