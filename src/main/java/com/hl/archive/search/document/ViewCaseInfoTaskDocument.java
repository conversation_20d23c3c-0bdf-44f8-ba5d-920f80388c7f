package com.hl.archive.search.document;



import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

@Data
@IndexName(value = "view_case_info_task")
public class ViewCaseInfoTaskDocument {
    /**
     * 唯一自增id
     */
    @IndexId(value = "id")
    private String id;

    /**
     * 案件编号
     */
    @IndexField(value = "case_no")
    private String caseNo;

    /**
     * 案件类别
     */
    @IndexField(value = "case_type")
    private String caseType;

    /**
     * 案件副类别
     */
    @IndexField(value = "case_detail_type")
    private String caseDetailType;

    /**
     * 案件名称
     */
    @IndexField(value = "case_name")
    private String caseName;

    /**
     * 报案时间
     */
    @IndexField(value = "report_time")
    private Date reportTime;

    /**
     * 发案地址
     */
    @IndexField(value = "case_address")
    private String caseAddress;

    /**
     * 所属责任区
     */
    @IndexField(value = "case_address_community")
    private String caseAddressCommunity;

    /**
     * 受理时间
     */
    @IndexField(value = "accept_time")
    private Date acceptTime;

    /**
     * 受理单位名称
     */
    @IndexField(value = "accept_organ_name")
    private String acceptOrganName;

    /**
     * 受理人名称
     */
    @IndexField(value = "case_accept_name")
    private String caseAcceptName;

    /**
     * 更新人
     */
    @IndexField(value = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @IndexField(value = "update_time")
    private Date updateTime;

    /**
     * 立案审批时间
     */
    @IndexField(value = "register_time")
    private Date registerTime;

    /**
     * 主办单位名称
     */
    @IndexField(value = "handle_organ_name")
    private String handleOrganName;

    /**
     * 主办人
     */
    @IndexField(value = "handle_police")
    private String handlePolice;

    /**
     * 案件所处流程状态
     */
    @IndexField(value = "flow_case")
    private String flowCase;

    /**
     * 协办人
     */
    @IndexField(value = "involved_police")
    private String involvedPolice;

    /**
     * 案件状态
     */
    @IndexField(value = "case_status")
    private String caseStatus;

    /**
     * 是否是刑事案件
     */
    @IndexField(value = "is_crime")
    private Integer isCrime;

    /**
     * 办理单位
     */
    @IndexField(value = "handle_organ")
    private String handleOrgan;

    /**
     * 流转状态
     */
    @IndexField(value = "turn_state")
    private Integer turnState;

    /**
     * 所属部位
     */
    @IndexField(value = "community")
    private String community;

    /**
     * 案件标签
     */
    @IndexField(value = "case_classify")
    private String caseClassify;

    /**
     * 案件级别
     */
    @IndexField(value = "case_level")
    private Integer caseLevel;

    /**
     * 等级颜色
     */
    @IndexField(value = "level_color")
    private String levelColor;

    /**
     * 关联警情
     */
    @IndexField(value = "police_no")
    private String policeNo;

    @IndexField(value = "simple_case_content")
    private String simpleCaseContent;

    /**
     * 案件执法情况
     */
    @IndexField(value = "handle_infos")
    private String handleInfos;

    /**
     * 是否已分配，0表示未分配，1表示已分配
     */
    @IndexField(value = "is_allot")
    private Integer isAllot;

    /**
     * 标号
     */
    @IndexField(value = "task_id")
    private String taskId;

    /**
     * 标题
     */
    @IndexField(value = "title")
    private String title;

    /**
     * 1红，2橙，3黄，4蓝
     */
    @IndexField(value = "`level`")
    private Integer level;

    /**
     * 1：一次性任务，2：需要多次完成
     */
    @IndexField(value = "`mode`")
    private Integer mode;

    /**
     * 内容
     */
    @IndexField(value = "content")
    private String content;

    /**
     * 人员
     */
    @IndexField(value = "persons")
    private String persons;

    /**
     * 案件最新处置情况
     */
    @IndexField(value = "best_handle_info")
    private String bestHandleInfo;

    /**
     * 任务开始时间
     */
    @IndexField(value = "work_starttime")
    private String workStarttime;

    /**
     * 任务结束时间
     */
    @IndexField(value = "work_endtime")
    private String workEndtime;

    /**
     * 项目相关
     */
    @IndexField(value = "project_id")
    private String projectId;

    /**
     * SSO中项目编号
     */
    @IndexField(value = "sso_project")
    private String ssoProject;

    /**
     * 创建人员（如果系统字段，为空）
     */
    @IndexField(value = "police_id")
    private String policeId;

    /**
     * 是否对附件可见，内容如下：{"invisible": ["_work"]}
     */
    @IndexField(value = "permission")
    private String permission;

    /**
     * 任务所属单位
     */
    @IndexField(value = "organization")
    private String organization;

    /**
     * 涉及的相关数据
     */
    @IndexField(value = "relation_data")
    private String relationData;

    /**
     * 附带信息
     */
    @IndexField(value = "annex_info")
    private String annexInfo;

    /**
     * 当前第几步骤
     */
    @IndexField(value = "person_level")
    private Integer personLevel;

    /**
     * 案件收藏人
     */
    @IndexField(value = "favorite")
    private String favorite;

    /**
     * 审批表次数
     */
    @IndexField(value = "approval_count")
    private Integer approvalCount;

    /**
     * 第几次循环工作
     */
    @IndexField(value = "result_level")
    private Integer resultLevel;

    /**
     * 当前状态， -1撤销， 0未开始，1未完成，10已完成
     */
    @IndexField(value = "`status`")
    private Integer status;

    /**
     * 0：未超时，1：已超时
     */
    @IndexField(value = "timeout")
    private Integer timeout;

    /**
     * 任务处理层级
     */
    @IndexField(value = "handle_level")
    private Integer handleLevel;

    /**
     * 流转时间
     */
    @IndexField(value = "last_time")
    private Date lastTime;

    /**
     * 业务子类型
     */
    @IndexField(value = "sub_business_type")
    private Integer subBusinessType;

    /**
     * 业务分类
     */
    @IndexField(value = "business_classify")
    private String businessClassify;

    /**
     * 父任务ID
     */
    @IndexField(value = "parent_id")
    private String parentId;

    @IndexField(value = "work_person")
    private String workPerson;

    @IndexField(value = "cowork_person")
    private String coworkPerson;

    @IndexField(value = "approve_person")
    private String approvePerson;
}