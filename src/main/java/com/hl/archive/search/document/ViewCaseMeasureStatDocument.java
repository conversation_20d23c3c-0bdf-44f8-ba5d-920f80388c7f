package com.hl.archive.search.document;

import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

@Data
@IndexName(value = "view_case_measure_stat")
public class ViewCaseMeasureStatDocument {
    /**
     * 唯一自增id
     */
    @IndexId(value = "id")
    private String id;

    /**
     * 案件编号
     */
    @IndexField(value = "case_no")
    private String caseNo;

    /**
     * 案件名称
     */
    @IndexField(value = "case_name")
    private String caseName;

    /**
     * 案件类别
     */
    @IndexField(value = "case_type")
    private String caseType;

    /**
     * 案件副类别
     */
    @IndexField(value = "case_detail_type")
    private String caseDetailType;

    /**
     * 案件状态
     */
    @IndexField(value = "case_status")
    private String caseStatus;

    /**
     * 是否是刑事案件
     */
    @IndexField(value = "is_crime")
    private Integer isCrime;

    /**
     * 是否已分配，0表示未分配，1表示已分配
     */
    @IndexField(value = "is_allot")
    private Integer isAllot;

    /**
     * 办理单位
     */
    @IndexField(value = "handle_organ")
    private String handleOrgan;

    /**
     * 被决定人身份号码
     */
    @IndexField(value = "person_card")
    private String personCard;

    /**
     * 人员姓名
     */
    @IndexField(value = "person_name")
    private String personName;

    /**
     * 决定时间
     */
    @IndexField(value = "measure_time")
    private Date measureTime;

    /**
     * 决定结果字典值
     */
    @IndexField(value = "result_code")
    private String resultCode;

    /**
     * 组织机构
     */
    @IndexField(value = "pm_tenant_id")
    private String pmTenantId;

    /**
     * 人员
     */
    @IndexField(value = "task_persons")
    private String taskPersons;

    /**
     * 任务所属单位
     */
    @IndexField(value = "organization")
    private String organization;

    /**
     * 任务开始时间
     */
    @IndexField(value = "work_starttime")
    private String workStarttime;

    @IndexField(value = "work_person")
    private String workPerson;

    @IndexField(value = "cowork_person")
    private String coworkPerson;

    @IndexField(value = "approve_person")
    private String approvePerson;
}