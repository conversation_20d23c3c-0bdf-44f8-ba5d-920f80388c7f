package com.hl.archive.search.document;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

@Data
@IndexName(value = "view_police_export")
public class ViewPoliceExportDocument {
    /**
     * 处警接警编号－接警编号
     */
    @IndexId(value = "")
    private String jjbh;

    /**
     * 接警报警人
     */
    @IndexField(value = "bjr")
    private String bjr;

    /**
     * 报警类型
     */
    @IndexField(value = "bjlx")
    private String bjlx;

    /**
     * 接警单位名称
     */
    @IndexField(value = "jjdwmc")
    private String jjdwmc;

    /**
     * 接警报警时间
     */
    @IndexField(value = "bjdhsj_time")
    private String bjdhsjTime;

    /**
     * 接警单位
     */
    @IndexField(value = "jjdw")
    private String jjdw;

    /**
     * 接警报警人联系电话
     */
    @IndexField(value = "lxdh")
    private String lxdh;

    /**
     * 报警内容
     */
    @IndexField(value = "bjnr")
    private String bjnr;

    /**
     * 发生地点
     */
    @IndexField(value = "sfdd")
    private String sfdd;

    /**
     * 报警方式
     */
    @IndexField(value = "bjxs")
    private String bjxs;

    /**
     * 处警处警时间
     */
    @IndexField(value = "cjsj_time")
    private String cjsjTime;

    /**
     * 处警类别
     */
    @IndexField(value = "cjlb")
    private String cjlb;

    /**
     * 处警单位
     */
    @IndexField(value = "cjdw")
    private String cjdw;

    /**
     * 事发场所
     */
    @IndexField(value = "sfcs")
    private String sfcs;

    /**
     * 损失详细情况
     */
    @IndexField(value = "ssxxqk")
    private String ssxxqk;

    /**
     * 补充处理结果
     */
    @IndexField(value = "bccljg")
    private String bccljg;

    /**
     * 事发星期
     */
    @IndexField(value = "sfxq")
    private String sfxq;

    /**
     * 处理结果内容
     */
    @IndexField(value = "cljgnr")
    private String cljgnr;

    /**
     * 登记人
     */
    @IndexField(value = "djr")
    private String djr;

    /**
     * 警情属性
     */
    @IndexField(value = "jqsx")
    private String jqsx;

    /**
     * 天气情况
     */
    @IndexField(value = "tqqk")
    private String tqqk;

    /**
     * 处警编号
     */
    @IndexField(value = "cjbh")
    private String cjbh;

    /**
     * 处警详址
     */
    @IndexField(value = "cjxz")
    private String cjxz;

    /**
     * 处警处警单位名称
     */
    @IndexField(value = "cjdwmc")
    private String cjdwmc;

    /**
     * 处警结果
     */
    @IndexField(value = "cjjg")
    private String cjjg;

    /**
     * 处警人
     */
    @IndexField(value = "cjr")
    private String cjr;

    /**
     * 区域类别
     */
    @IndexField(value = "qylb")
    private String qylb;

    /**
     * 处警信息地点
     */
    @IndexField(value = "cjxxdd")
    private String cjxxdd;
}