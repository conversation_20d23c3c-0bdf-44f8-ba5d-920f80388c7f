package com.hl.archive.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;


import java.util.Date;

/**
 * 内务通报
 */
@Data
@IndexName(value = "view_police_notify")
public class ViewPoliceNotifyDocument {
    /**
     * 主键ID
     */
    @IndexId(value = "id")
    private Long id;

    /**
     * 通知级别
     */
    @IndexField(value = "notify_level")
    private String notifyLevel;

    /**
     * 通知类型
     */
    @IndexField(value = "notify_type")
    private String notifyType;

    /**
     * 通知内容
     */
    @IndexField(value = "notify_content")
    private String notifyContent;

    /**
     * 文件ID
     */
    @IndexField(value = "file_id")
    private String fileId;

    /**
     * 是否已读
     */
    @IndexField(value = "is_read")
    private Integer isRead;

    /**
     * 所属分组
     */
    @IndexField(value = "group_id")
    private Long groupId;

    /**
     * 被通报人
     */
    @IndexField(value = "notify_person")
    private String notifyPerson;

    /**
     * 通报时间
     */
    @IndexField(value = "notify_time")
    private Date notifyTime;

    /**
     * 通报天数
     */
    @IndexField(value = "open_period")
    private Long openPeriod;

    /**
     * 创建人
     */
    @IndexField(value = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @IndexField(value = "create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @IndexField(value = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @IndexField(value = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @IndexField(value = "remark")
    private String remark;

    /**
     * 所属机构
     */
    @IndexField(value = "tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @IndexField(value = "is_deleted")
    private Integer isDeleted;
}