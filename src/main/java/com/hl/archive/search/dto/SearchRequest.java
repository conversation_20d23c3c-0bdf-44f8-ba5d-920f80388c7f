package com.hl.archive.search.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 搜索请求参数
 */
@Data
@ApiModel(description = "搜索请求参数")
public class SearchRequest {

    /**
     * 搜索关键词
     */
    @ApiModelProperty(value = "搜索关键词", example = "张三")
    private String keyword;

    /**
     * 数据类型过滤，可多选
     */
    @ApiModelProperty(value = "数据类型过滤", example = "[\"basic_info\", \"education\"]")
    private List<String> dataTypes;

    /**
     * 页码（从1开始）
     */
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer limit = 20;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", example = "createTime")
    private String sortField = "createTime";

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @ApiModelProperty(value = "排序方向", example = "desc")
    private String sortOrder = "desc";


}
