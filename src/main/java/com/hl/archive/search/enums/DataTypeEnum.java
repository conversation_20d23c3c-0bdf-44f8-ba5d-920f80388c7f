package com.hl.archive.search.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据类型枚举
 * 定义所有可搜索的数据类型
 */
@Getter
@AllArgsConstructor
public enum DataTypeEnum {

    // 基本信息类
    BASIC_INFO("basic_info", "基本信息", "民警基本信息"),
    CONTACT_INFO("contact_info", "联系信息", "民警联系信息"),
    
    // 教育培训类
    EDUCATION("education", "学历信息", "民警学历教育信息"),
    TRAINING("training", "培训信息", "民警培训信息"),
    TRAINING_RECORDS("training_records", "训历档案", "民警训历档案"),
    SPECIALTIES("specialties", "特长信息", "民警特长信息"),
    ABILITY_TAG("ability_tag", "职业能力标签", "民警职业能力标签信息"),
    
    // 工作履历类
    RESUME("resume", "履历信息", "民警工作履历"),
    RANK_INFO("rank_info", "警衔信息", "民警警衔信息"),
    POSITION_RANK("position_rank", "职务职级", "民警职务职级信息"),
    POLITICAL_STATUS("political_status", "政治面貌", "民警政治面貌信息"),
    
    // 考核评估类
    ANNUAL_ASSESSMENT("annual_assessment", "年度考核", "民警年度考核信息"),
    MONTHLY_ASSESSMENT("monthly_assessment", "月度考核", "民警月度考核信息"),
    QUARTERLY_ASSESSMENT("quarterly_assessment", "季度考核", "民警季度考核信息"),
    ONLINE_EXAM("online_exam", "网上考试", "民警网上考试信息"),
    HONORS("honors", "荣誉信息", "民警荣誉奖励信息"),
    
    // 健康状况类
    HEALTH_STATUS("health_status", "健康状况", "民警健康状况信息"),
    MARRIAGE_STATUS("marriage_status", "婚姻状况", "民警婚姻状况信息"),
    
    // 家庭成员类
    FAMILY_MEMBERS("family_members", "家庭成员", "民警家庭成员信息"),
    CHILDREN_FOREIGN_MARRIAGE("children_foreign_marriage", "子女涉外婚姻", "民警子女涉外婚姻信息"),
    FAMILY_CRIMINAL_LIABILITY("family_criminal_liability", "家属刑事责任", "民警家属刑事责任信息"),
    
    // 财产信息类
    FAMILY_REAL_ESTATE("family_real_estate", "房产信息", "本人配偶子女房产情况"),
    FAMILY_VEHICLES("family_vehicles", "车辆信息", "本人配偶子女车辆信息"),
    INVESTMENT_INFO("investment_info", "投资信息", "民警投资信息"),
    LOAN_INFO("loan_info", "借贷信息", "民警借贷信息"),
    FAMILY_SECURITIES_INSURANCE("family_securities_insurance", "证券保险", "家庭证券保险信息"),
    FAMILY_PRIVATE_EQUITY_FUND("family_private_equity_fund", "私募基金", "家庭私募基金信息"),
    
    // 商业活动类
    FAMILY_BUSINESS("family_business", "经商信息", "家庭经商信息"),
    FAMILY_PAID_INSTITUTIONS("family_paid_institutions", "有偿机构", "家庭有偿机构信息"),
    
    // 出入境类
    PASSPORT("passport", "护照信息", "民警护照信息"),
    HK_MACAU_TAIWAN_PERMIT("hk_macau_taiwan_permit", "港澳台通行证", "民警港澳台通行证信息"),
    OVERSEAS_TRAVEL("overseas_travel", "出国境信息", "民警出国境信息"),
    HK_MACAU_TAIWAN_TRAVEL("hk_macau_taiwan_travel", "港澳台出行", "民警港澳台出行信息"),
    FAMILY_OVERSEAS_MIGRATION("family_overseas_migration", "家属移居境外", "家属移居境外信息"),
    
    // 其他事项类
    OTHER_MATTERS("other_matters", "其他事项", "民警其他事项"),
    ORGANIZATIONAL_INQUIRY("organizational_inquiry", "组织询问", "民警组织询问信息"),
    DISHONEST_EXECUTOR("dishonest_executor", "失信被执行人", "民警失信被执行人信息"),
    WEDDING_FUNERAL_EVENTS("wedding_funeral_events", "婚丧嫁娶", "民警操办婚丧嫁娶信息");

    /**
     * 数据类型代码
     */
    private final String code;

    /**
     * 数据类型名称
     */
    private final String name;

    /**
     * 数据类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static DataTypeEnum getByCode(String code) {
        for (DataTypeEnum dataType : values()) {
            if (dataType.getCode().equals(code)) {
                return dataType;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举
     */
    public static DataTypeEnum getByName(String name) {
        for (DataTypeEnum dataType : values()) {
            if (dataType.getName().equals(name)) {
                return dataType;
            }
        }
        return null;
    }

    /**
     * 获取所有数据类型代码
     */
    public static String[] getAllCodes() {
        DataTypeEnum[] values = values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有数据类型名称
     */
    public static String[] getAllNames() {
        DataTypeEnum[] values = values();
        String[] names = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            names[i] = values[i].getName();
        }
        return names;
    }
}
