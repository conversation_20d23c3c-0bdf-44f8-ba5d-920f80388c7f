package com.hl.archive.search.mapper;

import com.hl.archive.search.document.PoliceSearchDocument;
import org.dromara.easyes.core.kernel.BaseEsMapper;
import org.springframework.stereotype.Repository;

/**
 * 民警搜索文档 Mapper
 * 使用 Easy-ES 框架操作 Elasticsearch
 */
@Repository
public interface PoliceSearchMapper extends BaseEsMapper<PoliceSearchDocument> {
    
    // Easy-ES 提供了丰富的基础方法，包括：
    // - insert(T entity): 插入文档
    // - insertBatch(Collection<T> entityList): 批量插入
    // - deleteById(Serializable id): 根据ID删除
    // - updateById(T entity): 根据ID更新
    // - selectById(Serializable id): 根据ID查询
    // - selectList(LambdaEsQueryWrapper<T> wrapper): 条件查询
    // - search(LambdaEsQueryWrapper<T> wrapper): 搜索查询
    // 等等，可以满足大部分需求
    
    // 如果需要自定义查询，可以在这里添加方法
}
