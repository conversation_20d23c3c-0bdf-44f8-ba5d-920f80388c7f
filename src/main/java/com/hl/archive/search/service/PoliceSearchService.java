package com.hl.archive.search.service;

import com.hl.archive.search.document.PoliceSearchDocument;
import com.hl.archive.search.dto.SearchRequest;
import org.dromara.easyes.core.biz.EsPageInfo;

import java.util.List;

/**
 * 民警搜索服务接口
 */
public interface PoliceSearchService {

    /**
     * 搜索民警信息
     *
     * @param request 搜索请求参数
     * @return 搜索结果
     */
    EsPageInfo<PoliceSearchDocument> search(SearchRequest request);

    /**
     * 根据ID获取搜索文档
     *
     * @param id 文档ID
     * @return 搜索文档
     */
    PoliceSearchDocument getById(String id);

    /**
     * 保存搜索文档
     *
     * @param document 搜索文档
     * @return 是否成功
     */
    boolean save(PoliceSearchDocument document);

    /**
     * 批量保存搜索文档
     *
     * @param documents 搜索文档列表
     * @return 是否成功
     */
    boolean saveBatch(List<PoliceSearchDocument> documents);

    /**
     * 根据ID删除搜索文档
     *
     * @param id 文档ID
     * @return 是否成功
     */
    boolean deleteById(String id);

    /**
     * 根据数据类型和记录ID删除搜索文档
     *
     * @param dataType 数据类型
     * @param recordId 记录ID
     * @return 是否成功
     */
    boolean deleteByDataTypeAndRecordId(String dataType, Long recordId);

    /**
     * 根据身份证号删除所有相关文档
     *
     * @param idCard 身份证号
     * @return 删除的文档数量
     */
    long deleteByIdCard(String idCard);

    /**
     * 更新搜索文档
     *
     * @param document 搜索文档
     * @return 是否成功
     */
    boolean update(PoliceSearchDocument document);

    /**
     * 创建索引
     *
     * @return 是否成功
     */
    boolean createIndex();

    /**
     * 删除索引
     *
     * @return 是否成功
     */
    boolean deleteIndex();

    /**
     * 检查索引是否存在
     *
     * @return 是否存在
     */
    boolean existsIndex();



}
