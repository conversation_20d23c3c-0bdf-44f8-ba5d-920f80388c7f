//package com.hl.archive.search.service.impl;
//
//import com.hl.archive.search.document.PoliceSearchDocument;
//import com.hl.archive.search.dto.SearchRequest;
//import com.hl.archive.search.dto.SearchResponse;
//import com.hl.archive.search.enums.DataTypeEnum;
//import com.hl.archive.search.mapper.PoliceSearchMapper;
//import com.hl.archive.search.service.PoliceSearchService;
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 民警搜索服务简化实现类
// * 使用基础的 Easy-ES 功能，避免复杂的查询语法
// */
//@Slf4j
//@Service("policeSearchServiceSimple")
//public class PoliceSearchServiceSimpleImpl implements PoliceSearchService {
//
//    @Autowired
//    private PoliceSearchMapper policeSearchMapper;
//
//    @Override
//    public SearchResponse search(SearchRequest request) {
//        log.info("开始搜索，关键词：{}", request.getKeyword());
//
//        try {
//            // 构建查询条件
//            LambdaEsQueryWrapper<PoliceSearchDocument> wrapper = new LambdaEsQueryWrapper<>();
//
//            // 基础过滤条件
//            wrapper.eq(PoliceSearchDocument::getStatus, 1);
//
//            // 关键词搜索 - 使用简单的 match 查询
//            if (StringUtils.hasText(request.getKeyword())) {
//                wrapper.match(PoliceSearchDocument::getName, request.getKeyword())
//                       .or()
//                       .match(PoliceSearchDocument::getMainContent, request.getKeyword())
//                       .or()
//                       .match(PoliceSearchDocument::getSubContent, request.getKeyword());
//            }
//
//            // 数据类型过滤
//            if (!CollectionUtils.isEmpty(request.getDataTypes())) {
//                wrapper.in(PoliceSearchDocument::getDataType, request.getDataTypes());
//            }
//
//            // 身份证号过滤
//            if (StringUtils.hasText(request.getIdCard())) {
//                wrapper.eq(PoliceSearchDocument::getIdCard, request.getIdCard());
//            }
//
//            // 姓名过滤
//            if (StringUtils.hasText(request.getName())) {
//                wrapper.match(PoliceSearchDocument::getName, request.getName());
//            }
//
//            // 部门过滤
//            if (StringUtils.hasText(request.getDepartment())) {
//                wrapper.match(PoliceSearchDocument::getDepartment, request.getDepartment());
//            }
//
//            // 设置分页
//            wrapper.from((request.getPageNum() - 1) * request.getPageSize())
//                   .size(request.getPageSize());
//
//            // 设置排序
//            wrapper.orderByDesc(PoliceSearchDocument::getCreateTime);
//
//            // 执行搜索
//            List<PoliceSearchDocument> documents = policeSearchMapper.selectList(wrapper);
//            Long total = policeSearchMapper.selectCount(wrapper);
//
//            // 构建响应结果
//            SearchResponse response = new SearchResponse();
//            response.setTotal(total);
//            response.setPageNum(request.getPageNum());
//            response.setPageSize(request.getPageSize());
//            response.setTotalPages((int) Math.ceil((double) total / request.getPageSize()));
//            response.setTook(0L); // 简化版本不计算耗时
//
//            // 转换搜索结果
//            List<SearchResponse.SearchResultItem> items = documents.stream()
//                    .map(this::convertToSearchResultItem)
//                    .collect(Collectors.toList());
//            response.setItems(items);
//
//            log.info("搜索完成，共找到 {} 条记录", response.getTotal());
//            return response;
//
//        } catch (Exception e) {
//            log.error("搜索失败", e);
//            throw new RuntimeException("搜索失败：" + e.getMessage());
//        }
//    }
//
//    @Override
//    public PoliceSearchDocument getById(String id) {
//        try {
//            return policeSearchMapper.selectById(id);
//        } catch (Exception e) {
//            log.error("根据ID获取文档失败，ID：{}", id, e);
//            return null;
//        }
//    }
//
//    @Override
//    public boolean save(PoliceSearchDocument document) {
//        try {
//            document.setCreateTime(new Date());
//            document.setUpdateTime(new Date());
//            document.setStatus(1);
//
//            Integer result = policeSearchMapper.insert(document);
//            return result != null && result > 0;
//        } catch (Exception e) {
//            log.error("保存文档失败", e);
//            return false;
//        }
//    }
//
//    @Override
//    public boolean saveBatch(List<PoliceSearchDocument> documents) {
//        if (CollectionUtils.isEmpty(documents)) {
//            return true;
//        }
//
//        try {
//            Date now = new Date();
//            documents.forEach(doc -> {
//                doc.setCreateTime(now);
//                doc.setUpdateTime(now);
//                doc.setStatus(1);
//            });
//
//            Integer result = policeSearchMapper.insertBatch(documents);
//            return result != null && result > 0;
//        } catch (Exception e) {
//            log.error("批量保存文档失败", e);
//            return false;
//        }
//    }
//
//    @Override
//    public boolean deleteById(String id) {
//        try {
//            Integer result = policeSearchMapper.deleteById(id);
//            return result != null && result > 0;
//        } catch (Exception e) {
//            log.error("删除文档失败，ID：{}", id, e);
//            return false;
//        }
//    }
//
//    @Override
//    public boolean deleteByDataTypeAndRecordId(String dataType, Long recordId) {
//        try {
//            String id = dataType + "_" + recordId;
//            return deleteById(id);
//        } catch (Exception e) {
//            log.error("根据数据类型和记录ID删除文档失败，dataType：{}，recordId：{}", dataType, recordId, e);
//            return false;
//        }
//    }
//
//    @Override
//    public long deleteByIdCard(String idCard) {
//        try {
//            LambdaEsQueryWrapper<PoliceSearchDocument> wrapper = new LambdaEsQueryWrapper<>();
//            wrapper.eq(PoliceSearchDocument::getIdCard, idCard);
//
//            Integer result = policeSearchMapper.delete(wrapper);
//            return result != null ? result : 0;
//        } catch (Exception e) {
//            log.error("根据身份证号删除文档失败，idCard：{}", idCard, e);
//            return 0;
//        }
//    }
//
//    @Override
//    public boolean update(PoliceSearchDocument document) {
//        try {
//            document.setUpdateTime(new Date());
//            Integer result = policeSearchMapper.updateById(document);
//            return result != null && result > 0;
//        } catch (Exception e) {
//            log.error("更新文档失败", e);
//            return false;
//        }
//    }
//
//    @Override
//    public boolean createIndex() {
//        try {
//            return policeSearchMapper.createIndex();
//        } catch (Exception e) {
//            log.error("创建索引失败", e);
//            return false;
//        }
//    }
//
//    @Override
//    public boolean deleteIndex() {
//        try {
//            return policeSearchMapper.deleteIndex();
//        } catch (Exception e) {
//            log.error("删除索引失败", e);
//            return false;
//        }
//    }
//
//    @Override
//    public boolean existsIndex() {
//        try {
//            return policeSearchMapper.existsIndex("");
//        } catch (Exception e) {
//            log.error("检查索引是否存在失败", e);
//            return false;
//        }
//    }
//
//    @Override
//    public List<String> getSuggestions(String keyword, int size) {
//        // 简化版本暂不实现
//        return new ArrayList<>();
//    }
//
//    @Override
//    public List<String> getHotKeywords(int size) {
//        // 简化版本暂不实现
//        return new ArrayList<>();
//    }
//
//    @Override
//    public List<SearchResponse.DataTypeCount> getDataTypeStatistics() {
//        try {
//            List<SearchResponse.DataTypeCount> counts = new ArrayList<>();
//
//            for (DataTypeEnum dataType : DataTypeEnum.values()) {
//                LambdaEsQueryWrapper<PoliceSearchDocument> wrapper = new LambdaEsQueryWrapper<>();
//                wrapper.eq(PoliceSearchDocument::getDataType, dataType.getCode())
//                       .eq(PoliceSearchDocument::getStatus, 1);
//
//                Long count = policeSearchMapper.selectCount(wrapper);
//
//                SearchResponse.DataTypeCount dataTypeCount = new SearchResponse.DataTypeCount();
//                dataTypeCount.setDataType(dataType.getCode());
//                dataTypeCount.setDataTypeName(dataType.getName());
//                dataTypeCount.setCount(count);
//
//                counts.add(dataTypeCount);
//            }
//
//            return counts;
//        } catch (Exception e) {
//            log.error("获取数据类型统计失败", e);
//            return new ArrayList<>();
//        }
//    }
//
//    @Override
//    public List<PoliceSearchDocument> getDocumentsByIdCard(String idCard) {
//        try {
//            LambdaEsQueryWrapper<PoliceSearchDocument> wrapper = new LambdaEsQueryWrapper<>();
//            wrapper.eq(PoliceSearchDocument::getIdCard, idCard)
//                   .eq(PoliceSearchDocument::getStatus, 1)
//                   .orderByDesc(PoliceSearchDocument::getCreateTime);
//
//            return policeSearchMapper.selectList(wrapper);
//        } catch (Exception e) {
//            log.error("根据身份证号获取文档失败，idCard：{}", idCard, e);
//            return new ArrayList<>();
//        }
//    }
//
//    @Override
//    public SearchResponse advancedSearch(SearchRequest request) {
//        return search(request);
//    }
//
//    @Override
//    public SearchResponse fuzzySearch(String keyword, int fuzziness, int pageNum, int pageSize) {
//        SearchRequest request = new SearchRequest();
//        request.setKeyword(keyword);
//        request.setPageNum(pageNum);
//        request.setPageSize(pageSize);
//
//        return search(request);
//    }
//
//    /**
//     * 转换为搜索结果项
//     */
//    private SearchResponse.SearchResultItem convertToSearchResultItem(PoliceSearchDocument document) {
//        SearchResponse.SearchResultItem item = new SearchResponse.SearchResultItem();
//        item.setId(document.getId());
//        item.setDataType(document.getDataType());
//        item.setDataTypeName(document.getDataTypeName());
//        item.setRecordId(document.getRecordId());
//        item.setIdCard(document.getIdCard());
//        item.setName(document.getName());
//        item.setGender(document.getGender());
//        item.setDepartment(document.getDepartment());
//        item.setPositionName(document.getPositionName());
//        item.setMainContent(document.getMainContent());
//        item.setSubContent(document.getSubContent());
//        item.setRecordTime(document.getRecordTime() != null ? document.getRecordTime().toString() : null);
//        item.setOriginalData(document.getOriginalData());
//        item.setTags(document.getTags());
//        item.setImportance(document.getImportance());
//        item.setRemark(document.getRemark());
//
//        // 简化版本不设置高亮和评分
//        item.setHighlights(new HashMap<>());
//        item.setScore(0.0f);
//
//        return item;
//    }
//}
