package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.hl.archive.domain.dto.WjPoliceNewsAttachmentDto;
import com.hl.archive.domain.dto.WjPoliceNewsDetailDto;
import com.hl.archive.domain.dto.WjPoliceNewsDto;
import com.hl.archive.domain.dto.WjPoliceSpiderResult;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 武进公安信息网爬虫服务
 */
@Slf4j
@Service
public class FjWebSiteSpiderService {

    private static final String BASE_URL = "http://50.56.116.11/ArticleList.asp?mode=%ED%C2%BE%AF%D1%A7%CC%C3";
    private static final String DETAIL_BASE_URL = "http://50.56.116.11/articleshow.asp?id=";
    private static final Pattern DATE_PATTERN = Pattern.compile("\\[(\\d{2})/(\\d{1,2})/(\\d{1,2})\\]");
    private static final Pattern ID_PATTERN = Pattern.compile("id=(\\d+)");
    private static final Pattern PAGE_INFO_PATTERN = Pattern.compile("总计(\\d+)条.*共有(\\d+)/(\\d+)页");
    private static final Pattern DETAIL_TIME_PATTERN = Pattern.compile("发布时间:\\s*(\\d{4})/(\\d{1,2})/(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})");
    private static final Pattern VIEW_COUNT_PATTERN = Pattern.compile("浏览数:\\s*(\\d+)");

    // HTTP请求超时时间
    private static final int TIMEOUT = 30000;

    /**
     * 爬取武进公安信息网数据（包含详情）
     * @param includeDetail 是否包含详情信息
     * @return 爬虫结果
     */
    public WjPoliceSpiderResult crawlWjPoliceNews(boolean includeDetail) {
        WjPoliceSpiderResult result = new WjPoliceSpiderResult();
        result.setSuccess(false);

        try {

            // 首先获取第一页来获取总页数信息
            WjPoliceSpiderResult firstPageResult = crawlSinglePage(BASE_URL, 1, includeDetail);
            if (!firstPageResult.getSuccess()) {
                return firstPageResult;
            }

            result.setTotalPages(firstPageResult.getTotalPages());
            result.setTotalRecords(firstPageResult.getTotalRecords());
            result.setCurrentPage(1);

            // 爬取所有页面的数据
            List<WjPoliceNewsDto> allNews = new ArrayList<>();

            for (int page = 1; page <= firstPageResult.getTotalPages(); page++) {
                log.info("正在爬取第{}页数据...", page);
                WjPoliceSpiderResult pageResult = crawlSinglePage(BASE_URL, page, includeDetail);

                if (pageResult.getSuccess() && pageResult.getNewsList() != null) {
                    allNews.addAll(pageResult.getNewsList());
                }
            }

            result.setNewsList(allNews);
            result.setSuccess(true);

            log.info("爬取完成，共获取{}条新闻数据", allNews.size());

        } catch (Exception e) {
            log.error("爬取武进公安信息网数据失败", e);
            result.setErrorMessage("爬取失败: " + e.getMessage());
        }

        return result;
    }


    /**
     * 爬取单页数据
     * @param baseUrl 基础URL
     * @param page 页码
     * @param includeDetail 是否包含详情信息
     * @return 单页爬虫结果
     */
    private WjPoliceSpiderResult crawlSinglePage(String baseUrl, int page, boolean includeDetail) {
        WjPoliceSpiderResult result = new WjPoliceSpiderResult();
        result.setSuccess(false);
        result.setCurrentPage(page);

        try {
            String url = baseUrl + "&page=" + page;
            log.debug("正在请求URL: {}", url);

            // 使用Hutool发送HTTP请求
            String html = HttpUtil.createGet(url)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .timeout(TIMEOUT)
                    .execute()
                    .body();

            if (StrUtil.isBlank(html)) {
                result.setErrorMessage("获取页面内容为空");
                return result;
            }

            Document doc = Jsoup.parse(html, "GBK");

            // 解析页面信息
            parsePageInfo(doc, result);

            // 解析新闻列表
            List<WjPoliceNewsDto> newsList = parseNewsList(doc, page, includeDetail);
            result.setNewsList(newsList);
            result.setSuccess(true);

            log.debug("第{}页解析完成，获取{}条新闻", page, newsList.size());

        } catch (Exception e) {
            log.error("爬取第{}页失败", page, e);
            result.setErrorMessage("爬取失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析页面信息（总页数、总记录数等）
     */
    private void parsePageInfo(Document doc, WjPoliceSpiderResult result) {
        try {
            // 查找包含页面信息的文本，例如："总计38条　共有3/3页"
            Elements pageInfoElements = doc.select("td.t11");
            for (Element element : pageInfoElements) {
                String text = element.text();
                Matcher matcher = PAGE_INFO_PATTERN.matcher(text);
                if (matcher.find()) {
                    result.setTotalRecords(Integer.parseInt(matcher.group(1)));
                    result.setTotalPages(Integer.parseInt(matcher.group(3)));
                    log.debug("解析到页面信息: 总记录数={}, 总页数={}", result.getTotalRecords(), result.getTotalPages());
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("解析页面信息失败", e);
        }
    }

    /**
     * 解析新闻列表
     */
    private List<WjPoliceNewsDto> parseNewsList(Document doc, int page, boolean includeDetail) {
        List<WjPoliceNewsDto> newsList = new ArrayList<>();

        try {
            // 查找新闻条目，根据HTML结构定位
            Elements newsRows = doc.select("tr[valign=middle]");

            for (Element row : newsRows) {
                try {
                    WjPoliceNewsDto news = parseNewsItem(row, page);
                    if (news != null) {
                        // 如果需要详情信息，则爬取详情页面
                        if (includeDetail && StrUtil.isNotBlank(news.getNewsId())) {
                            WjPoliceNewsDetailDto detail = crawlNewsDetail(news.getNewsId());
                            news.setDetail(detail);
                        }
                        newsList.add(news);
                    }
                } catch (Exception e) {
                    log.warn("解析单条新闻失败", e);
                }
            }

        } catch (Exception e) {
            log.error("解析新闻列表失败", e);
        }

        return newsList;
    }

    /**
     * 解析单条新闻
     */
    private WjPoliceNewsDto parseNewsItem(Element row, int page) {
        try {
            Elements linkElements = row.select("a[href]");
            if (linkElements.isEmpty()) {
                return null;
            }

            Element linkElement = linkElements.first();
            String title = linkElement.text().trim();
            String href = linkElement.attr("href");

            // 跳过空标题
            if (StrUtil.isBlank(title)) {
                return null;
            }

            WjPoliceNewsDto news = new WjPoliceNewsDto();
            news.setTitle(title);
            news.setUrl(href);
            news.setPageNumber(page);

            // 提取新闻ID
            Matcher idMatcher = ID_PATTERN.matcher(href);
            if (idMatcher.find()) {
                news.setNewsId(idMatcher.group(1));
            }

            // 解析日期
            Elements dateElements = row.select("span.t2");
            if (!dateElements.isEmpty()) {
                String dateText = dateElements.first().text();
                LocalDate publishDate = parseDate(dateText);
                news.setPublishDate(publishDate);
            }

            return news;

        } catch (Exception e) {
            log.warn("解析新闻条目失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 爬取新闻详情
     * @param newsId 新闻ID
     * @return 新闻详情
     */
    public WjPoliceNewsDetailDto crawlNewsDetail(String newsId) {
        WjPoliceNewsDetailDto detail = new WjPoliceNewsDetailDto();
        detail.setNewsId(newsId);
        detail.setSuccess(false);

        try {
            String detailUrl = DETAIL_BASE_URL + newsId;
            detail.setDetailUrl(detailUrl);

            log.debug("正在爬取新闻详情: {}", detailUrl);

            // 使用Hutool发送HTTP请求
            String html = HttpUtil.createGet(detailUrl)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .timeout(TIMEOUT)
                    .execute()
                    .body();

            if (StrUtil.isBlank(html)) {
                detail.setErrorMessage("获取详情页面内容为空");
                return detail;
            }

            Document doc = Jsoup.parse(html, "GBK");

            // 解析标题
            Elements titleElements = doc.select("span.aqlx");
            if (!titleElements.isEmpty()) {
                detail.setTitle(titleElements.first().text().trim());
            }

            // 解析发布时间和浏览数
            parseDetailTimeAndViews(doc, detail);

            // 解析内容
            parseDetailContent(doc, detail);

            // 解析附件
            List<WjPoliceNewsAttachmentDto> attachments = parseAttachments(doc);
            detail.setAttachments(attachments);

            detail.setSuccess(true);
            log.debug("新闻详情爬取成功: {}", newsId);

        } catch (Exception e) {
            log.error("爬取新闻详情失败: {}", newsId, e);
            detail.setErrorMessage("爬取详情失败: " + e.getMessage());
        }

        return detail;
    }

    /**
     * 解析日期字符串
     * @param dateText 日期文本，格式如 [24/4/7]
     * @return LocalDate对象
     */
    private LocalDate parseDate(String dateText) {
        try {
            Matcher matcher = DATE_PATTERN.matcher(dateText);
            if (matcher.find()) {
                int year = 2000 + Integer.parseInt(matcher.group(1)); // 24 -> 2024
                int month = Integer.parseInt(matcher.group(2));
                int day = Integer.parseInt(matcher.group(3));
                return LocalDate.of(year, month, day);
            }
        } catch (Exception e) {
            log.warn("解析日期失败: {}", dateText, e);
        }
        return null;
    }

    /**
     * 解析详情页面的时间和浏览数
     */
    private void parseDetailTimeAndViews(Document doc, WjPoliceNewsDetailDto detail) {
        try {
            Elements timeElements = doc.select("span.yh");
            if (!timeElements.isEmpty()) {
                String timeText = timeElements.first().text();

                // 解析发布时间：发布时间: 2024/4/7 14:52:33
                Matcher timeMatcher = DETAIL_TIME_PATTERN.matcher(timeText);
                if (timeMatcher.find()) {
                    int year = Integer.parseInt(timeMatcher.group(1));
                    int month = Integer.parseInt(timeMatcher.group(2));
                    int day = Integer.parseInt(timeMatcher.group(3));
                    int hour = Integer.parseInt(timeMatcher.group(4));
                    int minute = Integer.parseInt(timeMatcher.group(5));
                    int second = Integer.parseInt(timeMatcher.group(6));

                    LocalDateTime publishTime = LocalDateTime.of(year, month, day, hour, minute, second);
                    detail.setPublishTime(publishTime);
                }

                // 解析浏览数：浏览数: 122
                Matcher viewMatcher = VIEW_COUNT_PATTERN.matcher(timeText);
                if (viewMatcher.find()) {
                    detail.setViewCount(Integer.parseInt(viewMatcher.group(1)));
                }
            }
        } catch (Exception e) {
            log.warn("解析时间和浏览数失败", e);
        }
    }

    /**
     * 解析详情页面的内容
     */
    private void parseDetailContent(Document doc, WjPoliceNewsDetailDto detail) {
        try {
            // 查找内容区域，根据HTML结构定位
            Elements contentElements = doc.select("table[width=860]");
            if (!contentElements.isEmpty()) {
                Element contentTable = contentElements.first();

                // 获取HTML内容
                detail.setContent(contentTable.html());

                // 获取纯文本内容
                detail.setTextContent(contentTable.text());
            }
        } catch (Exception e) {
            log.warn("解析内容失败", e);
        }
    }

    /**
     * 解析附件信息
     */
    private List<WjPoliceNewsAttachmentDto> parseAttachments(Document doc) {
        List<WjPoliceNewsAttachmentDto> attachments = new ArrayList<>();

        try {
            // 查找附件链接，根据HTML结构定位
            Elements attachmentElements = doc.select("table[width=860] a[href]");

            for (Element attachmentElement : attachmentElements) {
                try {
                    String href = attachmentElement.attr("href");
                    String fileName = attachmentElement.text().trim();

                    // 跳过空的或无效的链接
                    if (StrUtil.isBlank(href) || StrUtil.isBlank(fileName) ||
                        href.startsWith("javascript:") || href.startsWith("#")) {
                        continue;
                    }

                    WjPoliceNewsAttachmentDto attachment = new WjPoliceNewsAttachmentDto();
                    attachment.setFileName(fileName);
                    attachment.setDownloadUrl(href);

                    // 判断文件类型
                    String fileType = getFileType(fileName);
                    attachment.setFileType(fileType);

                    // 查找图标
                    Element iconElement = attachmentElement.previousElementSibling();
                    if (iconElement != null && "img".equals(iconElement.tagName())) {
                        attachment.setIconUrl(iconElement.attr("src"));
                    }

                    attachments.add(attachment);
                    log.debug("找到附件: {} - {}", fileName, href);

                } catch (Exception e) {
                    log.warn("解析单个附件失败", e);
                }
            }

        } catch (Exception e) {
            log.warn("解析附件列表失败", e);
        }

        return attachments;
    }

    /**
     * 根据文件名获取文件类型
     */
    private String getFileType(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "unknown";
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".pdf")) {
            return "pdf";
        } else if (lowerFileName.endsWith(".doc") || lowerFileName.endsWith(".docx")) {
            return "word";
        } else if (lowerFileName.endsWith(".xls") || lowerFileName.endsWith(".xlsx")) {
            return "excel";
        } else if (lowerFileName.endsWith(".ppt") || lowerFileName.endsWith(".pptx")) {
            return "powerpoint";
        } else if (lowerFileName.endsWith(".txt")) {
            return "text";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg") ||
                   lowerFileName.endsWith(".png") || lowerFileName.endsWith(".gif")) {
            return "image";
        } else if (lowerFileName.endsWith(".zip") || lowerFileName.endsWith(".rar") ||
                   lowerFileName.endsWith(".7z")) {
            return "archive";
        } else {
            return "unknown";
        }
    }

    /**
     * 只爬取指定页面的数据
     * @param baseUrl 基础URL
     * @param page 页码
     * @return 爬虫结果
     */
    public WjPoliceSpiderResult crawlSinglePageOnly(String baseUrl, int page) {
        String url = StrUtil.isNotBlank(baseUrl) ? baseUrl : BASE_URL;
        return crawlSinglePage(url, page, false);
    }

    /**
     * 只爬取指定页面的数据（包含详情）
     * @param baseUrl 基础URL
     * @param page 页码
     * @param includeDetail 是否包含详情
     * @return 爬虫结果
     */
    public WjPoliceSpiderResult crawlSinglePageOnly(String baseUrl, int page, boolean includeDetail) {
        String url = StrUtil.isNotBlank(baseUrl) ? baseUrl : BASE_URL;
        return crawlSinglePage(url, page, includeDetail);
    }
}
