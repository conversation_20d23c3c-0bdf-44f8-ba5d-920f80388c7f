package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceAbilityTagConfig;
import com.hl.archive.listener.event.PoliceAbilityTagDeleteEvent;
import com.hl.archive.listener.event.PoliceAbilityTagEvent;
import com.hl.archive.mapper.PoliceAbilityTagMapper;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 民警职业能力标签服务接口
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceAbilityTagService extends ServiceImpl<PoliceAbilityTagMapper, PoliceAbilityTag> {

    @Value("${spring.security.sso.projectToken}")
    private String token;

    @Resource
    private TaskApi taskApi;

    private final PoliceAbilityTagConfig policeAbilityTagConfig;


    @Resource
    @Qualifier("datasource2DataSource")
    private DataSource taskDataSource;

    @EventListener(PoliceAbilityTagEvent.class)
    public void parseTaskAbilityTag(PoliceAbilityTagEvent event) {
        try {
            JSONObject taskData = event.getTaskData();
            log.info("职业能力标签:{}",taskData);
            String opt = taskData.getByPath("opt").toString();
            if (!"audit".equals(opt)){
                return;
            }
            String passStatus= taskData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)){
                return;
            }
            // 处理内容数据
            handleContentData(taskData);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    private void handleContentData(JSONObject taskData) {
        String taskId = taskData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        JSONObject content = parsed.getJSONObject("all_content");
        List<String> abilityTagCode = content.getList(policeAbilityTagConfig.getAbilityTagCode(), String.class);
        List<String> abilityTagName = new ArrayList<>();
        for (String tagCode : abilityTagCode) {
            if (DictCache.ID_MAP_TREE.containsKey(tagCode)) {
                String dictName = DictCache.ID_MAP_TREE.getJSONObject(tagCode).getString("name");
                abilityTagName.add(dictName);
            }
        }
        List<String> idCard = content.getList(policeAbilityTagConfig.getIdCard(), String.class);
        String description = content.getString(policeAbilityTagConfig.getDescription());
        String obtainTime = content.getString(policeAbilityTagConfig.getObtainTime());
        PoliceAbilityTag policeAbilityTag = new PoliceAbilityTag();
        policeAbilityTag.setAbilityTagCode(abilityTagCode);
        policeAbilityTag.setIdCard(idCard);
        policeAbilityTag.setDescription(description);
        policeAbilityTag.setObtainTime(DateUtil.parse(obtainTime).toLocalDateTime().toLocalDate());
        policeAbilityTag.setAbilityTagName(abilityTagName);
        policeAbilityTag.setTaskId(taskId);
        log.info("任务:{} 审批通过,新增职业能力标签:{}",taskId,policeAbilityTag);
        this.save(policeAbilityTag);
    }


    @EventListener(PoliceAbilityTagDeleteEvent.class)
    public void handleDeleteEvent(PoliceAbilityTagDeleteEvent event) {
        String taskId = null;
        try {
            JSONObject taskData = event.getTaskData();
            taskId = taskData.getByPath("data.task_id").toString();
            log.info("任务:{} 删除职业能力标签", taskId);
            this.remove(Wrappers.<PoliceAbilityTag>lambdaQuery()
                    .eq(PoliceAbilityTag::getTaskId, taskId));
        } catch (Exception e) {
            log.info("任务:{} 删除职业能力标签失败", taskId);
            log.error(e.getMessage(), e);
        }
    }


    @JobExecutor(name = "handleAbilityTag")
    public void handleAbilityTag(JobArgs jobArgs) {
        try {
            Object jobParams = jobArgs.getJobParams();
            if (jobParams == null) {
                SnailJobLog.REMOTE.info("职业能力标签任务参数为空");
                return;
            }
            JSONObject param = JSONObject.parseObject(jobParams.toString());
            List<String> taskId = param.getList("taskId", String.class);
            if (taskId == null || taskId.isEmpty()) {
                SnailJobLog.REMOTE.info("职业能力标签任务id为空");
                return;
            }
            String taskStr = taskId.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            List<Entity> query = DbUtil.use(taskDataSource).query("select * from view_archive_task_event where opt = 'audit' and task_id in (?) ", taskStr);
            for (Entity entity : query) {
                String contentData = entity.getStr("content_data");
                JSONObject contentDataObj = JSONObject.parseObject(contentData);
                SnailJobLog.REMOTE.info("职业能力补偿任务处理:{}", contentDataObj);
                handleContentData(contentDataObj);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }
}
