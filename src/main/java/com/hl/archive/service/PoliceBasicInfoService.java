package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceStatisticsDTO;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import com.hl.archive.utils.SsoCacheUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceBasicInfoMapper;
import com.hl.archive.domain.entity.PoliceBasicInfo;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PoliceBasicInfoService extends ServiceImpl<PoliceBasicInfoMapper, PoliceBasicInfo> {

    private final PoliceBasicInfoMapper policeBasicInfoMapper;

    public List<PoliceStatisticsDTO> getPoliceStatisticsByDepartment() {
        List<PoliceStatisticsDTO> policeStatisticsByDepartment = policeBasicInfoMapper.getPoliceStatisticsByDepartment();

        for (PoliceStatisticsDTO policeStatisticsDTO : policeStatisticsByDepartment) {
            String department = policeStatisticsDTO.getDepartment();
            if (StrUtil.isNotBlank(department)) {
                String organizationName = SsoCacheUtil.getOrganizationName(department);
                policeStatisticsDTO.setDepartmentName(organizationName);
            }
        }

        return policeStatisticsByDepartment;
    }

    public PoliceStatisticsDTO getPoliceStatisticsByOrgId(StatisticsQueryRequest request) {
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (!"320412000000".equals(request.getOrganizationId())) {
                // 非320412000000时，截取前8位
                request.setOrganizationId(request.getOrganizationId().substring(0, 8));
            }
            // 320412000000时保持原值，在SQL中单独处理
        }
        PoliceStatisticsDTO policeStatisticsByOrgId = policeBasicInfoMapper.getPoliceStatisticsByOrgId(request);
        if (policeStatisticsByOrgId == null){
            return new PoliceStatisticsDTO();
        }

        String department = policeStatisticsByOrgId.getDepartment();
        if (StrUtil.isNotBlank(department)) {
            String organizationName = SsoCacheUtil.getOrganizationName(department);
            policeStatisticsByOrgId.setDepartmentName(organizationName);
        }
        return policeStatisticsByOrgId;
    }

    /**
     * 统计数字穿透查询
     */
    public Page<PoliceBasicInfo> getPoliceListByStatisticsType(StatisticsDrillDownRequest request) {
        // 创建分页对象
        Page<PoliceBasicInfo> page = new Page<>(request.getPage(), request.getLimit());
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (!"320412000000".equals(request.getOrganizationId())) {
                // 非320412000000时，截取前8位
                request.setOrganizationId(request.getOrganizationId().substring(0, 8));
            }
            // 320412000000时保持原值，在SQL中单独处理
        }
        // 执行分页查询
        Page<PoliceBasicInfo> resultPage = policeBasicInfoMapper.getPoliceListByStatisticsType(page, request);
        return resultPage;
    }


    /**
     * 根据身份证号查询民警基本信息
     */
    public PoliceBasicInfo getByIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return null;
        }

        LambdaQueryWrapper<PoliceBasicInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PoliceBasicInfo::getIdCard, idCard);
        queryWrapper.last("LIMIT 1");

        return this.getOne(queryWrapper);
    }
}
