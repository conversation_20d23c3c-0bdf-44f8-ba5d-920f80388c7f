package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceCapabilityEvalRequestDTO;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.security.utils.SsoUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceCapabilityEvalMapper;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
@Service
public class PoliceCapabilityEvalService extends ServiceImpl<PoliceCapabilityEvalMapper, PoliceCapabilityEval> {

    public Page<PoliceCapabilityEval> pageList(PoliceCapabilityEvalRequestDTO requestDTO) {

        Page<PoliceCapabilityEval> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PoliceCapabilityEval>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getPoliceNumber()), PoliceCapabilityEval::getPoliceNumber, requestDTO.getPoliceNumber()));
        return page;
    }
}
