package com.hl.archive.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceClubMessageLike;
import com.hl.archive.mapper.PoliceClubMessageLikeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 留言点赞服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceClubMessageLikeService extends ServiceImpl<PoliceClubMessageLikeMapper, PoliceClubMessageLike> {

    /**
     * 查询用户对指定留言的点赞记录
     *
     * @param messageId 留言ID
     * @param userIdCard 用户身份证号
     * @return 点赞记录
     */
    public PoliceClubMessageLike getByMessageIdAndUserId(Long messageId, String userIdCard) {
        return this.baseMapper.selectByMessageIdAndUserId(messageId, userIdCard);
    }

    /**
     * 删除用户对指定留言的点赞记录
     *
     * @param messageId 留言ID
     * @param userIdCard 用户身份证号
     * @return 是否成功
     */
    public boolean deleteByMessageIdAndUserId(Long messageId, String userIdCard) {
        return this.baseMapper.deleteByMessageIdAndUserId(messageId, userIdCard) > 0;
    }

    /**
     * 查询留言的点赞用户列表
     *
     * @param messageId 留言ID
     * @param limit 限制数量
     * @return 点赞用户列表
     */
    public List<PoliceClubMessageLike> getLikeUsersByMessageId(Long messageId, Integer limit) {
        return this.baseMapper.selectLikeUsersByMessageId(messageId, limit);
    }

    /**
     * 统计留言的点赞数量
     *
     * @param messageId 留言ID
     * @return 点赞数量
     */
    public int countLikesByMessageId(Long messageId) {
        return this.baseMapper.countLikesByMessageId(messageId);
    }

    /**
     * 批量查询用户的点赞状态
     *
     * @param messageIds 留言ID列表
     * @param userIdCard 用户身份证号
     * @return 已点赞的留言ID列表
     */
    public List<Long> getUserLikedMessageIds(List<Long> messageIds, String userIdCard) {
        return this.baseMapper.selectUserLikedMessageIds(messageIds, userIdCard);
    }
}
