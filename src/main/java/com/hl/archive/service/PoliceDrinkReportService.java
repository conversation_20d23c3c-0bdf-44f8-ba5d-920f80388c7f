package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.config.PoliceDrinkReportConfig;
import com.hl.common.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.archive.mapper.PoliceDrinkReportMapper;

import javax.annotation.Resource;
import javax.sql.DataSource;

@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceDrinkReportService extends ServiceImpl<PoliceDrinkReportMapper, PoliceDrinkReport> {
    private final PoliceDrinkReportConfig policeDrinkReportConfig;

    private final TaskApi taskApi;

    @Value("${spring.security.sso.projectToken}")
    private String token;


    @Resource
    @Qualifier("datasource2DataSource")
    private DataSource taskDataSource;

    public void deleteDrinkReport(JSONObject contentData) {
        String taskId = contentData.getByPath("data.task_id").toString();
        this.remove(Wrappers.<PoliceDrinkReport>lambdaQuery()
                .eq(PoliceDrinkReport::getTaskId, taskId));
        log.info("饮酒报备删除成功:{}", taskId);
    }

    public void saveDrinkReport(JSONObject contentData) {
        String customId = contentData.getByPath("data.custom_id").toString();
        if (!"ZGLDSP".equals(customId) && !"FGJLDSP".equals(customId) && !"JLDSP".equals(customId)) return;
        String passStatus = contentData.getByPath("data.content.pass").toString();
        if (!"1".equals(passStatus)) return;

        String taskId = contentData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        String idCard = parsed.getByPath("all_content.id_card").toString();
        JSONObject content = parsed.getJSONObject("all_content");
        PoliceDrinkReport drinkReport = new PoliceDrinkReport();
        drinkReport.setIdCard(idCard);
        drinkReport.setDrinkTime(DateUtil.parse(content.getString(policeDrinkReportConfig.getDrinkTime())).toLocalDateTime());
        drinkReport.setReason(content.getString(policeDrinkReportConfig.getReason()));
        drinkReport.setLocation(content.getString(policeDrinkReportConfig.getLocation()));
        drinkReport.setInviter(content.getString(policeDrinkReportConfig.getInviter()));
        drinkReport.setParticipants(content.getString(policeDrinkReportConfig.getParticipants()));
        drinkReport.setPayer(content.getString(policeDrinkReportConfig.getPayer()));
        drinkReport.setTravelMode(content.getString(policeDrinkReportConfig.getTravelMode()));
        drinkReport.setRemark(content.getString(policeDrinkReportConfig.getRemark()));
        drinkReport.setTaskId(taskId);
        log.info("饮酒报备保存成功:{}", drinkReport);
        this.save(drinkReport);
    }

    @JobExecutor(name = "handleAuditDrinkReport")
    public void handleDrinkReport(JobArgs jobArgs) {
        try {
            Object jobParams = jobArgs.getJobParams();
            if (jobParams == null) {
                SnailJobLog.REMOTE.info("饮酒报备任务参数为空");
                return;
            }
            JSONObject param = JSONObject.parseObject(jobParams.toString());
            List<String> taskId = param.getList("taskId", String.class);
            if (taskId == null || taskId.isEmpty()) {
                SnailJobLog.REMOTE.info("饮酒报备任务id为空");
                return;
            }
            String taskStr = taskId.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            List<Entity> query = DbUtil.use(taskDataSource).query("select * from view_archive_task_event where opt = 'audit' and task_id in (?) ", taskStr);
            for (Entity entity : query) {
                String contentData = entity.getStr("content_data");
                JSONObject contentDataObj = JSONObject.parseObject(contentData);
                SnailJobLog.REMOTE.info("饮酒报备任务处理:{}", contentDataObj);
                saveDrinkReport(contentDataObj);
            }
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
    }
}
