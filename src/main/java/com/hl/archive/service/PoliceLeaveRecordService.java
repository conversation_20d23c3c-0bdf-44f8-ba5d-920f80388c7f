package com.hl.archive.service;

import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.PoliceLeaveRecordCountReturnDTO;
import com.hl.archive.domain.dto.PoliceLeaveRecordQueryDTO;
import com.hl.archive.domain.dto.PoliceLeaveRecordTypeQueryDTO;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PoliceLeaveRecord;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.event.RefreshDutyStatusEvent;
import com.hl.archive.listener.event.RefreshDutyStatusWithDeleteEvent;
import com.hl.archive.mapper.PoliceLeaveRecordMapper;
import com.hl.archive.utils.LeaveRecordUtil;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;
import com.hl.security.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PoliceLeaveRecordService extends ServiceImpl<PoliceLeaveRecordMapper, PoliceLeaveRecord> {

    @Value("${spring.security.sso.projectToken}")
    private String token;


    @Resource
    private TaskApi taskApi;

    @Resource
    private PoliceBasicInfoService policeBasicInfoService;


    @Scheduled(cron = "0 10 0 * * *")
    public void refreshLeaveRecord() {
        log.info("接收刷新在岗状态事件");

        DateTime start = DateUtil.endOfDay(DateUtil.date());
        DateTime end = DateUtil.beginOfDay(DateUtil.date());

        List<JSONObject> idCards = this.baseMapper.queryLeaveRecord(start.toString(), end.toString());
        if (idCards.isEmpty()) {
            policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                    .set(PoliceBasicInfo::getDutyStatus, "在岗"));
        } else {
            Set<String> idSet = idCards.stream().map(a -> a.getString("id_card")).collect(Collectors.toSet());
            log.info("当天休假身份证:{}", idSet);
            policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                    .set(PoliceBasicInfo::getDutyStatus, "在岗")
                    .notIn(PoliceBasicInfo::getIdCard, idSet));
            for (JSONObject object : idCards) {
                String idCard = object.getString("id_card");
                String leaveType = object.getString("leave_type");
                policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                        .set(PoliceBasicInfo::getDutyStatus, leaveType)
                        .eq(PoliceBasicInfo::getIdCard, idCard));
            }
        }
    }


    @EventListener(RefreshDutyStatusEvent.class)
    public void taskListen(RefreshDutyStatusEvent event) {
        JSONObject contentData = event.getContentData();

        String configUuid = contentData.getByPath("data.config_uuid").toString();

        switch (configUuid) {
            case "CC42ITDEHRQ":
                praseQXJ(contentData);
                break;
            case "CG9Z9HJ3FJW":
                praseCC(contentData);
                break;
        }
        // 刷新在岗状态
        this.refreshLeaveRecord();
    }

    // 请休假
    public void praseQXJ(JSONObject contentData) {
        try {
            String passStatus = contentData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) {
                return;
            }
            String customId = contentData.getByPath("data.custom_id").toString();
            if (!"ZGLDSP".equals(customId)
                    && !"FGJLD".equals(customId)
                    && !"JLDSP".equals(customId)) {
                return;
            }
            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            String time = parsed.getByPath("content.qjkssj").toString();
            String[] timeArr = time.split("_");
            String qjkssj = timeArr[0];
            String qjjssj = timeArr[1];
            String qjlb = parsed.getByPath("content.qjlb").toString();
            List<String> idCardList = JSONArray.from(parsed.getByPath("content.qjry")).toJavaList(String.class);
            for (String s : idCardList) {
                PoliceLeaveRecord leaveRecord = new PoliceLeaveRecord();
                leaveRecord.setStartDate(DateUtil.beginOfDay(DateUtil.parseDate(qjkssj)).toLocalDateTime());
                leaveRecord.setEndDate(DateUtil.endOfDay(DateUtil.parseDate(qjjssj)).toLocalDateTime());
                leaveRecord.setIdCard(s);
                leaveRecord.setLeaveType(DictCache.ID_MAP_TREE.getJSONObject(qjlb).getString("name"));
                leaveRecord.setTaskId(taskId);
                this.save(leaveRecord);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    // 出差
    private void praseCC(JSONObject contentData) {
        try {
            String passStatus = contentData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) {
                return;
            }
            String customId = contentData.getByPath("data.custom_id").toString();
            if (!"ZGLDSP".equals(customId)
                    && !"FGJLD".equals(customId)
                    && !"JLDSP".equals(customId)) {
                return;
            }
            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            String ccsj = parsed.getByPath("content.ccsj").toString();
            String[] timeArr = ccsj.split("_");
            List<String> idCardList = JSONArray.from(parsed.getByPath("content.ccyy")).toJavaList(String.class);
            for (String s : idCardList) {
                PoliceLeaveRecord leaveRecord = new PoliceLeaveRecord();
                leaveRecord.setStartDate(DateUtil.beginOfDay(DateUtil.parseDate(timeArr[0])).toLocalDateTime());
                leaveRecord.setEndDate(DateUtil.endOfDay(DateUtil.parseDate(timeArr[1])).toLocalDateTime());
                leaveRecord.setIdCard(s);
                leaveRecord.setLeaveType("出差");
                leaveRecord.setTaskId(taskId);
                this.save(leaveRecord);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    @EventListener(RefreshDutyStatusWithDeleteEvent.class)
    public void handleDeleteEvent(RefreshDutyStatusWithDeleteEvent event) {
        try {
            JSONObject contentData = event.getContentData();
            String taskId = contentData.getByPath("data.task_id").toString();
            this.remove(Wrappers.<PoliceLeaveRecord>lambdaQuery()
                    .eq(PoliceLeaveRecord::getTaskId, taskId));
            this.refreshLeaveRecord();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    /**
     * 计算出差统计
     */
    private PoliceLeaveRecordCountReturnDTO.BusinessTripStatistics calculateBusinessTripStatistics(
            List<PoliceLeaveRecord> records) {
        PoliceLeaveRecordCountReturnDTO.BusinessTripStatistics stats =
                new PoliceLeaveRecordCountReturnDTO.BusinessTripStatistics();

        List<PoliceLeaveRecord> businessTripRecords = records.stream()
                .filter(record -> "出差".equals(record.getLeaveType()))
                .collect(Collectors.toList());
        if (businessTripRecords.isEmpty()) {
            return stats;
        }

        stats.setTripCount(businessTripRecords.size());

        List<LeaveRecordUtil.LeaveRange> ranges = businessTripRecords.stream()
                .map(l ->
                        new LeaveRecordUtil.LeaveRange(l.getStartDate(), l.getEndDate()))
                .collect(Collectors.toList());
        stats.setTotalDays((int) LeaveRecordUtil.calculateLeaveDays(ranges));

        return stats;
    }

    /**
     * 计算请假统计
     */
    private PoliceLeaveRecordCountReturnDTO.LeaveStatistics calculateLeaveStatistics(
            List<PoliceLeaveRecord> records) {
        PoliceLeaveRecordCountReturnDTO.LeaveStatistics stats =
                new PoliceLeaveRecordCountReturnDTO.LeaveStatistics();

        // 过滤掉出差记录，只统计请假
        List<PoliceLeaveRecord> leaveRecords = records.stream()
                .filter(record -> !"出差".equals(record.getLeaveType()))
                .collect(Collectors.toList());
        if (leaveRecords.isEmpty()) {
            return stats;
        }
        stats.setTotalLeaveCount(leaveRecords.size());

        List<LeaveRecordUtil.LeaveRange> ranges = leaveRecords.stream()
                .map(l ->
                        new LeaveRecordUtil.LeaveRange(l.getStartDate(), l.getEndDate()))
                .collect(Collectors.toList());
        stats.setTotalLeaveDays((int) LeaveRecordUtil.calculateLeaveDays(ranges));


        // 按请假类型统计次数
        Map<String, Long> leaveTypeCount = leaveRecords.stream()
                .collect(Collectors.groupingBy(PoliceLeaveRecord::getLeaveType, Collectors.counting()));
        List<PoliceLeaveRecordCountReturnDTO.TypeCount> leaveTypeDetails = leaveTypeCount.entrySet().stream()
                .map(entry -> {
                    PoliceLeaveRecordCountReturnDTO.TypeCount typeCount =
                            new PoliceLeaveRecordCountReturnDTO.TypeCount();
                    typeCount.setType(entry.getKey());
                    typeCount.setCount(entry.getValue().intValue());
                    return typeCount;
                })
                .collect(Collectors.toList());
        stats.setLeaveTypeDetails(leaveTypeDetails);

        return stats;
    }




    public PoliceLeaveRecordCountReturnDTO countLeaveRecord(PoliceLeaveRecordQueryDTO policeLeaveRecordQueryDTO) {
        PoliceLeaveRecordCountReturnDTO leaveRecordCountReturnDTO = new PoliceLeaveRecordCountReturnDTO();
        List<PoliceLeaveRecord> list = this.list(Wrappers.<PoliceLeaveRecord>lambdaQuery()
                .eq(PoliceLeaveRecord::getIdCard, policeLeaveRecordQueryDTO.getIdCard()));
        if (list.isEmpty()) {
            return leaveRecordCountReturnDTO;
        }

        PoliceLeaveRecordCountReturnDTO.LeaveStatistics leaveStatistics = calculateLeaveStatistics(list);
        PoliceLeaveRecordCountReturnDTO.BusinessTripStatistics businessTripStatistics = calculateBusinessTripStatistics(list);
        leaveRecordCountReturnDTO.setLeaveStatistics(leaveStatistics);
        leaveRecordCountReturnDTO.setBusinessTripStatistics(businessTripStatistics);
        return leaveRecordCountReturnDTO;

    }

    public R<JSONObject> queryTaskInfo(PoliceLeaveRecordTypeQueryDTO policeLeaveRecordTypeQueryDTO) {
        String queryType = policeLeaveRecordTypeQueryDTO.getQueryType();
        LambdaQueryWrapper<PoliceLeaveRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PoliceLeaveRecord::getIdCard, policeLeaveRecordTypeQueryDTO.getIdCard());
        JSONObject param = new JSONObject();
        if ("请假".equals(queryType)) {
            queryWrapper.ne(PoliceLeaveRecord::getLeaveType, "出差");
            param.put("config_uuid", "CC42ITDEHRQ");
        }else if ("出差".equals(queryType)) {
            queryWrapper.eq(PoliceLeaveRecord::getLeaveType, "出差");
            param.put("config_uuid", "CG9Z9HJ3FJW");
        }else {
            queryWrapper.eq(PoliceLeaveRecord::getLeaveType, queryType);
            param.put("config_uuid", "CC42ITDEHRQ");
        }
        List<PoliceLeaveRecord> list = this.list(queryWrapper);
        if (list.isEmpty()) {
            return R.ok(new JSONObject());
        }
        List<String> taskIds = list.stream().map(PoliceLeaveRecord::getTaskId).collect(Collectors.toList());
        param.put("task_ids", taskIds);
        String userToken = UserUtils.getUser().getToken();
        R<JSONObject> taskList = taskApi.getTaskList(userToken, param);
       return taskList;

    }
}
