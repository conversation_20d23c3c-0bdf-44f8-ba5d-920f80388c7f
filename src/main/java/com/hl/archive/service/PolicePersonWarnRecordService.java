package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PolicePersonWarnRecordRequestDTO;
import com.hl.archive.domain.dto.PolicePersonWarnRecordTxTaskCreateDTO;
import com.hl.archive.domain.dto.PolicePersonWarnRecordTxTaskQueryDTO;
import com.hl.archive.domain.entity.PolicePersonWarnRecordTask;
import com.hl.archive.feign.TaskApi;
import com.hl.common.config.exception.HlErrException;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PolicePersonWarnRecord;
import com.hl.archive.mapper.PolicePersonWarnRecordMapper;

import javax.servlet.http.HttpServletResponse;

@Service
@RequiredArgsConstructor
public class PolicePersonWarnRecordService extends ServiceImpl<PolicePersonWarnRecordMapper, PolicePersonWarnRecord> {

    private final TaskApi taskApi;

    private final PolicePersonWarnRecordTaskService policePersonWarnRecordTaskService;


    public Page<PolicePersonWarnRecord> pageList(PolicePersonWarnRecordRequestDTO requestDTO) {

        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if (!"320412000000".equals(requestDTO.getOrganizationId())) {
                // 非320412000000时，截取前8位
                requestDTO.setOrganizationId(requestDTO.getOrganizationId().substring(0, 8));
            } else {
                requestDTO.setOrganizationId(null);
            }
        }
        Page<PolicePersonWarnRecord> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), Wrappers.<PolicePersonWarnRecord>lambdaQuery()
                .like(StrUtil.isNotBlank(requestDTO.getIdCard()), PolicePersonWarnRecord::getIdCard, requestDTO.getIdCard())
                .eq(StrUtil.isNotBlank(requestDTO.getWarnType()), PolicePersonWarnRecord::getWarnType, requestDTO.getWarnType())
                .like(StrUtil.isNotBlank(requestDTO.getName()), PolicePersonWarnRecord::getName, requestDTO.getName())
                .likeRight(StrUtil.isNotBlank(requestDTO.getOrganizationId()), PolicePersonWarnRecord::getOrganizationId, requestDTO.getOrganizationId())
                .ge(requestDTO.getWarnStartTime() != null, PolicePersonWarnRecord::getWarnTime, requestDTO.getWarnStartTime())
                .le(requestDTO.getWarnEndTime() != null, PolicePersonWarnRecord::getWarnTime, requestDTO.getWarnEndTime())
                .eq(requestDTO.getSignStatus() != null, PolicePersonWarnRecord::getSignStatus, requestDTO.getSignStatus())
                .and(StrUtil.isNotBlank(requestDTO.getQuery()), w ->
                        w.like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getName, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getIdCard, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getPoliceNumber, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getDescription, requestDTO.getQuery())
                                .or()
                                .like(StrUtil.isNotBlank(requestDTO.getQuery()), PolicePersonWarnRecord::getDataKey, requestDTO.getQuery())
                )
                .orderByDesc(PolicePersonWarnRecord::getWarnTime));
        return page;
    }

    public void exportWarnInfo(PolicePersonWarnRecordRequestDTO requestDTO, HttpServletResponse response) throws IOException {
        requestDTO.setLimit(Integer.MAX_VALUE);
        Page<PolicePersonWarnRecord> policePersonWarnRecordPage = this.pageList(requestDTO);
        List<PolicePersonWarnRecord> records = policePersonWarnRecordPage.getRecords();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        FastExcel.write(response.getOutputStream(), PolicePersonWarnRecord.class).autoCloseStream(Boolean.FALSE).sheet("数据导出")
                .doWrite(records);
    }

    public JSONArray createTxTask(PolicePersonWarnRecordTxTaskCreateDTO requestDTO) {
        JSONObject param = new JSONObject();
        param.put("config_uuid","CIHS71KS27P");
        param.put("btxjfr",requestDTO.getBtxjfr());
        param.put("txthr",requestDTO.getTxthr());
        param.put("txthyy",requestDTO.getTxthyy());
        String token = UserUtils.getUser().getToken();
        R<JSONArray> taskRes = taskApi.add(token, param);
        if (taskRes.getErrno() == 200) {
            String taskId = taskRes.getData().getString(0);
            PolicePersonWarnRecordTask recordTask = new PolicePersonWarnRecordTask();
            recordTask.setWarnId(Long.valueOf(requestDTO.getDataId()));
            // 谈心谈话类型数据
            recordTask.setTaskType("txth");
            recordTask.setTaskId(taskId);
            boolean save = policePersonWarnRecordTaskService.save(recordTask);
            if (!save) {
                throw new HlErrException("任务创建失败");
            }
            return taskRes.getData();
        }else {
            throw new HlErrException("任务创建失败");
        }
    }

    public R<?> listTaskInfo(PolicePersonWarnRecordTxTaskQueryDTO requestDTO) {
        List<PolicePersonWarnRecordTask> list = policePersonWarnRecordTaskService.list(Wrappers.<PolicePersonWarnRecordTask>lambdaQuery()
                .eq(PolicePersonWarnRecordTask::getWarnId, requestDTO.getDataId())
                .eq(StrUtil.isNotBlank(requestDTO.getTaskType()), PolicePersonWarnRecordTask::getTaskType, requestDTO.getTaskType()));
        List<String> taskIdList = list.stream().map(PolicePersonWarnRecordTask::getTaskId).collect(Collectors.toList());
        if (taskIdList.isEmpty()) {
            return R.ok();
        }
        JSONObject param = new JSONObject();
        // 后期多个任务类型
        param.put("config_uuid","CIHS71KS27P");
        param.put("task_ids", taskIdList);
        String token = UserUtils.getUser().getToken();
        R<JSONObject> taskList = taskApi.getTaskList(token, param);
        return taskList;
    }
}
