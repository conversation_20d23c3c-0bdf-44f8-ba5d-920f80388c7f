package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzAddDTO;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoZdgzReturnDTO;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.security.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceTagInfoZdgzMapper;
import com.hl.archive.domain.entity.PoliceTagInfoZdgz;
@Service
public class PoliceTagInfoZdgzService extends ServiceImpl<PoliceTagInfoZdgzMapper, PoliceTagInfoZdgz> {

    public boolean addTag(PoliceTagInfoZdgzAddDTO dto) {
        List<PoliceTagInfoZdgz> list = new ArrayList<>();
        List<String> idCardList = dto.getIdCardList();
        for (String s : idCardList) {
            List<String> tagNameList = dto.getTagNameList();
            for (String string : tagNameList) {
                PoliceTagInfoZdgz policeTagInfoZdgz = new PoliceTagInfoZdgz();
                policeTagInfoZdgz.setIdCard(s);
                policeTagInfoZdgz.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(s));
                policeTagInfoZdgz.setTagName(string);
                policeTagInfoZdgz.setRemark(dto.getRemark());
                policeTagInfoZdgz.setCreatedBy(UserUtils.getUser().getIdCard());
                list.add(policeTagInfoZdgz);
            }

        }

        return saveBatch(list);
    }

    public Page<PoliceTagInfoZdgzReturnDTO> listTag(PoliceTagInfoZdgzQueryDTO dto) {
        Page<PoliceTagInfoZdgzReturnDTO> page = Page.of(dto.getPage(), dto.getLimit());

        String organizationId = dto.getOrganizationId();
        if (StrUtil.isNotBlank(organizationId)) {
            if ("320412000000".equals(organizationId)) {
                dto.setOrganizationId(null);
            }else {
                dto.setOrganizationId(organizationId.substring(0, 8));
            }
        }
        Page<PoliceTagInfoZdgzReturnDTO> resultPage = this.baseMapper.listTag(page,dto);
        return resultPage;
    }
}
