package com.hl.archive.service;

import com.hl.archive.domain.dto.PoliceTagInfoZdpyAddDTO;
import com.hl.archive.utils.SsoCacheUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceTagInfoZdpy;
import com.hl.archive.mapper.PoliceTagInfoZdpyMapper;
@Service
public class PoliceTagInfoZdpyService extends ServiceImpl<PoliceTagInfoZdpyMapper, PoliceTagInfoZdpy> {

    public boolean addTag(PoliceTagInfoZdpyAddDTO dto) {
        List<String> idCardList = dto.getIdCardList();

        List<PoliceTagInfoZdpy> list = new ArrayList<>();
        for (String s : idCardList) {
            PoliceTagInfoZdpy policeTagInfoZdpy = new PoliceTagInfoZdpy();
            policeTagInfoZdpy.setIdCard(s);
            policeTagInfoZdpy.setOrganizationId(SsoCacheUtil.getUserOrgIdByIdCard(s));
            policeTagInfoZdpy.setAwardDate(dto.getAwardDate());
            policeTagInfoZdpy.setRemark(dto.getRemark());
            list.add(policeTagInfoZdpy);
        }
        return saveBatch(list);
    }
}
