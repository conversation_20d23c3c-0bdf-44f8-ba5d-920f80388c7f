package com.hl.archive.service;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.hl.archive.domain.dto.WjPoliceNewsDetailDto;
import com.hl.archive.domain.dto.WjPoliceNewsDto;
import com.hl.archive.domain.dto.WjPoliceSpiderResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.WjWebNewsMapper;
import com.hl.archive.domain.entity.WjWebNews;
@Service
@RequiredArgsConstructor
public class WjWebNewsService extends ServiceImpl<WjWebNewsMapper, WjWebNews> {



    private final FjWebSiteSpiderService fjWebSiteSpiderService;

    /**
     * 分局网站爬虫
     */
    @JobExecutor(name = "spiderFjWebData")
    public void spiderData(){

        WjPoliceSpiderResult result = fjWebSiteSpiderService.crawlWjPoliceNews(true);
        List<WjPoliceNewsDto> newsList = result.getNewsList();

        for (WjPoliceNewsDto newsDto : newsList) {
            WjWebNews news = new WjWebNews();

            news.setNewsId(newsDto.getNewsId());
            news.setTitle(newsDto.getTitle());
            news.setDetailUrl(newsDto.getUrl());

            WjPoliceNewsDetailDto detail = newsDto.getDetail();
            news.setContent(detail.getContent());
            news.setTextContent(detail.getTextContent());
            news.setViewCount(detail.getViewCount());
            news.setAttachments(detail.getAttachments());
            news.setPublishTime(newsDto.getPublishDate());

            WjWebNews webNews = this.lambdaQuery()
                    .eq(WjWebNews::getNewsId, news.getNewsId())
                    .last(" limit 1")
                    .one();
            if (webNews != null) {
                webNews.setNewsId(news.getNewsId());
            }
            this.saveOrUpdate(news);
        }

    }
}
