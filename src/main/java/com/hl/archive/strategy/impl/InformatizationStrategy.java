package com.hl.archive.strategy.impl;

import com.hl.archive.strategy.QualificationRecordStrategy;
import org.springframework.stereotype.Component;

@Component
public class InformatizationStrategy implements QualificationRecordStrategy {
    @Override
    public String getProject() {
        return "信息化";
    }

    @Override
    public double calculatePoint(String idCard, String rawScore) {
        return Double.parseDouble(rawScore);
    }

    @Override
    public boolean isPass(double point) {
      return point >= 80;
    }
}
