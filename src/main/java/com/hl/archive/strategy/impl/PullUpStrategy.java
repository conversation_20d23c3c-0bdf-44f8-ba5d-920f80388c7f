package com.hl.archive.strategy.impl;

import cn.hutool.core.util.IdcardUtil;
import com.hl.archive.strategy.QualificationRecordStrategy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PullUpStrategy implements QualificationRecordStrategy {

    // 评分标准配置：年龄组 -> 次数与分数的映射
    private static final Map<Integer, Map<Integer, Double>> SCORE_STANDARDS = new HashMap<>();

    static {
        // <=24岁年龄组 (原逻辑：6=60, 7=65, 9=70, 10=75, 11=80, 12=85, 13=90, 14=95, >=15=100)
        Map<Integer, Double> age24Standards = new HashMap<>();
        age24Standards.put(6, 60.0);
        age24Standards.put(7, 65.0);
        age24Standards.put(9, 70.0);
        age24Standards.put(10, 75.0);
        age24Standards.put(11, 80.0);
        age24Standards.put(12, 85.0);
        age24Standards.put(13, 90.0);
        age24Standards.put(14, 95.0);
        age24Standards.put(15, 100.0); // >=15 = 100
        SCORE_STANDARDS.put(24, age24Standards);

        // 25-29岁年龄组 (原逻辑：4=60, 5=65, 6=70, 7=80, 8=85, 9=90, 10=95, >10=100)
        Map<Integer, Double> age29Standards = new HashMap<>();
        age29Standards.put(4, 60.0);
        age29Standards.put(5, 65.0);
        age29Standards.put(6, 70.0);
        age29Standards.put(7, 80.0);
        age29Standards.put(8, 85.0);
        age29Standards.put(9, 90.0);
        age29Standards.put(10, 95.0);
        age29Standards.put(11, 100.0); // >10 = 100
        SCORE_STANDARDS.put(29, age29Standards);

        // 30-34岁年龄组 (原逻辑：4=60, 5=70, 6=80, 7=85, 8=90, 9=95, >9=100)
        Map<Integer, Double> age34Standards = new HashMap<>();
        age34Standards.put(4, 60.0);
        age34Standards.put(5, 70.0);
        age34Standards.put(6, 80.0);
        age34Standards.put(7, 85.0);
        age34Standards.put(8, 90.0);
        age34Standards.put(9, 95.0);
        age34Standards.put(10, 100.0); // >9 = 100
        SCORE_STANDARDS.put(34, age34Standards);

        // 35-39岁年龄组 (原逻辑：3=60, 4=70, 5=80, 6=90, >6=100)
        Map<Integer, Double> age39Standards = new HashMap<>();
        age39Standards.put(3, 60.0);
        age39Standards.put(4, 70.0);
        age39Standards.put(5, 80.0);
        age39Standards.put(6, 90.0);
        age39Standards.put(7, 100.0); // >6 = 100
        SCORE_STANDARDS.put(39, age39Standards);
    }

    @Override
    public String getProject() {
        return "引体向上";
    }

    @Override
    public double calculatePoint(String idCard, String rawScore) {
        int age = IdcardUtil.getAgeByIdCard(idCard);
        int score = Integer.parseInt(rawScore);

        // 找到对应的年龄组标准
        Map<Integer, Double> ageStandards = findAgeGroupStandards(age);
        if (ageStandards == null) {
            return 100; // 超过39岁默认100分
        }

        // 根据次数计算分数
        return calculateScoreByCount(score, ageStandards);
    }

    /**
     * 根据年龄找到对应的评分标准
     */
    private Map<Integer, Double> findAgeGroupStandards(int age) {
        for (Map.Entry<Integer, Map<Integer, Double>> entry : SCORE_STANDARDS.entrySet()) {
            if (age <= entry.getKey()) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 根据引体向上次数计算分数
     */
    private double calculateScoreByCount(int count, Map<Integer, Double> standards) {
        // 查找精确匹配的分数
        Double exactScore = standards.get(count);
        if (exactScore != null) {
            return exactScore;
        }

        // 找到最高的次数要求
        int maxCount = standards.keySet().stream().mapToInt(Integer::intValue).max().orElse(0);

        // 如果超过最高要求，返回100分
        if (count > maxCount) {
            return 100;
        }

        // 找到最低的次数要求
        int minCount = standards.keySet().stream().mapToInt(Integer::intValue).min().orElse(0);

        // 如果低于最低要求，返回0分
        if (count < minCount) {
            return 0;
        }

        // 在范围内但没有精确匹配的情况，原逻辑中这种情况不存在
        // 因为我们已经配置了所有可能的分数点
        return 0;
    }
}
