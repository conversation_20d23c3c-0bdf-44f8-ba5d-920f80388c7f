package com.hl.archive.strategy.impl;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.hl.archive.strategy.QualificationRecordStrategy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class TwoKmStrategy implements QualificationRecordStrategy {

    // 评分标准配置：性别 -> 年龄组 -> 时间阈值数组（从100分到60分）
    private static final Map<Integer, Map<Integer, int[]>> SCORE_STANDARDS = new HashMap<>();

    static {
        // 男性评分标准
        Map<Integer, int[]> maleStandards = new HashMap<>();
        maleStandards.put(24, new int[]{530, 540, 550, 560, 570, 580, 590, 600, 610}); // <=24岁
        maleStandards.put(29, new int[]{550, 560, 570, 580, 590, 600, 610, 620, 630}); // <=29岁
        maleStandards.put(34, new int[]{580, 590, 600, 610, 620, 630, 640, 650, 660}); // <=34岁
        maleStandards.put(39, new int[]{620, 630, 640, 650, 660, 670, 680, 690, 700}); // <=39岁
        maleStandards.put(44, new int[]{670, 680, 690, 700, 710, 720, 730, 740, 750}); // <=44岁
        maleStandards.put(49, new int[]{730, 740, 750, 760, 770, 780, 790, 800, 810}); // <=49岁
        SCORE_STANDARDS.put(1, maleStandards);

        // 女性评分标准
        Map<Integer, int[]> femaleStandards = new HashMap<>();
        femaleStandards.put(24, new int[]{650, 660, 670, 680, 690, 700, 710, 720, 730}); // <=24岁
        femaleStandards.put(29, new int[]{680, 690, 700, 710, 720, 730, 740, 750, 760}); // <=29岁
        femaleStandards.put(34, new int[]{710, 720, 730, 740, 750, 760, 770, 780, 790}); // <=34岁
        femaleStandards.put(39, new int[]{750, 760, 770, 780, 790, 800, 810, 820, 830}); // <=39岁
        femaleStandards.put(44, new int[]{800, 810, 820, 830, 840, 850, 860, 870, 880}); // <=44岁
        SCORE_STANDARDS.put(0, femaleStandards);
    }

    @Override
    public String getProject() {
        return "2000米";
    }

    @Override
    public double calculatePoint(String idCard, String rawScore) {
        int gender = IdcardUtil.getGenderByIdCard(idCard);
        int age = IdcardUtil.getAgeByIdCard(idCard);
        int seconds = parseToSeconds(rawScore);

        Map<Integer, int[]> genderStandards = SCORE_STANDARDS.get(gender);
        if (genderStandards == null) {
            return 0;
        }

        // 找到对应的年龄组
        int[] thresholds = findAgeGroupThresholds(genderStandards, age);
        if (thresholds == null) {
            return 0;
        }

        // 根据时间计算分数
        return calculateScoreByThresholds(seconds, thresholds);
    }

    /**
     * 根据年龄找到对应的评分标准
     */
    private int[] findAgeGroupThresholds(Map<Integer, int[]> genderStandards, int age) {
        for (Map.Entry<Integer, int[]> entry : genderStandards.entrySet()) {
            if (age <= entry.getKey()) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 根据时间阈值数组计算分数
     * 数组从100分到60分的时间阈值
     */
    private double calculateScoreByThresholds(int seconds, int[] thresholds) {
        // 100分：小于等于第一个阈值
        if (seconds <= thresholds[0]) {
            return 100;
        }

        // 95分到60分：在对应区间内
        for (int i = 1; i < thresholds.length; i++) {
            if (seconds <= thresholds[i]) {
                return 100 - (i * 5); // 100, 95, 90, 85, 80, 75, 70, 65, 60
            }
        }

        // 超过最后一个阈值，0分
        return 0;
    }


    private int parseToSeconds(String input) {
        if (StrUtil.isBlank(input)) {
            throw new IllegalArgumentException("成绩不能为空");
        }
        int minutes = 0;
        int seconds = 0;

        if (input.contains("分")) {
            String[] parts = input.split("分");
            minutes = Integer.parseInt(parts[0].trim());
            if (parts.length > 1 && parts[1].contains("秒")) {
                seconds = Integer.parseInt(parts[1].replace("秒", "").trim());
            }
        } else if (input.contains("秒")) {
            seconds = Integer.parseInt(input.replace("秒", "").trim());
        }

        return minutes * 60 + seconds;
    }
}
