package com.hl.archive.task;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.domain.entity.*;
import com.hl.archive.service.*;
import com.hl.security.utils.SsoUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

@Component
@RequiredArgsConstructor
public class BasicInfoSyncTask {

    private final PoliceBasicInfoService policeBasicInfoService;

    private final PoliceEducationService policeEducationService;

    private final PoliceMarriageStatusService policeMarriageStatusService;

    private final PolicePositionRankService policePositionRankService;

    private final PolicePoliticalStatusService policePoliticalStatusService;

    private final PoliceContactInfoService policeContactInfoService;

    @JobExecutor(name = "syncExtractBasicInfo")
//    @EventListener(ApplicationReadyEvent.class)
    public void syncBasicInfo() {
        // 数据量不大所以全部查询
        List<PoliceBasicInfo> policeBasicInfos = policeBasicInfoService.list();

        List<PoliceEducation> policeEducations = policeEducationService.list();

        List<PoliceMarriageStatus> policeMarriageStatuses = policeMarriageStatusService.list();

        List<PolicePositionRank> policePositionRanks = policePositionRankService.list();

        List<PolicePoliticalStatus> policePoliticalStatus = policePoliticalStatusService.list();

        List<PoliceContactInfo> list = policeContactInfoService.list();

        for (PoliceBasicInfo policeBasicInfo : policeBasicInfos) {
            String idCard = policeBasicInfo.getIdCard();
            // 获取最新的学历
            policeEducations.stream()
                    .filter(education -> idCard.equals(education.getIdCard()))
                    // 按批准日期排序，获取最新的学历信息。
                    .max(Comparator.comparing(PoliceEducation::getGraduationDate,
                            Comparator.nullsFirst(Comparator.naturalOrder())))
                    .ifPresent(latestEducation -> {
                        policeBasicInfo.setEducationLevel(latestEducation.getEducationLevel());
                    });

            // 获取最新的婚姻状况
            policeMarriageStatuses.stream()
                    .filter(marriageStatus -> idCard.equals(marriageStatus.getIdCard()))
                    // 按变动日期排序，获取最新的婚姻状况。
                    .max(Comparator.comparing(PoliceMarriageStatus::getChangeDate,
                            Comparator.nullsFirst(java.util.Comparator.naturalOrder())))
                    .ifPresent(latestMarriageStatus -> policeBasicInfo
                            .setMarriageStatus(latestMarriageStatus.getMarriageStatus()));

            // 获取最新的职务职级
            policePositionRanks.stream()
                    .filter(positionRank -> idCard.equals(positionRank.getIdCard()))
                    // 按任职日期排序，获取最新的职务职级。
                    .max(Comparator.comparing(PolicePositionRank::getCurrentPositionDate,
                            Comparator.nullsFirst(java.util.Comparator.naturalOrder())))
                    .ifPresent(latestPositionRank -> {
                        policeBasicInfo.setPositionName(latestPositionRank.getPositionName());
                    });
            // 获取最新政治面貌
            policePoliticalStatus.stream()
                    .filter(politicalStatus -> idCard.equals(politicalStatus.getIdCard()))
                    // 按加入日期排序，获取最新的政治面貌。
                    .max(Comparator.comparing(PolicePoliticalStatus::getJoinPartyDate,
                            Comparator.nullsFirst(Comparator.naturalOrder())))
                    .ifPresent(latestPoliticalStatus -> policeBasicInfo.setPoliticalIdentity(latestPoliticalStatus.getPoliticalIdentity()));

            PoliceContactInfo policeContactInfo = list.stream().filter(p -> p.getIdCard().equals(idCard)).findFirst().orElse(null);
            if (policeContactInfo != null) {
                policeBasicInfo.setHomeAddress(policeContactInfo.getHomeAddress());
                policeBasicInfo.setMobilePhone(policeContactInfo.getMobilePhone());
            }

            JSONObject ssoUser = SsoUtils.me.getSsoUser(idCard);
            if (ssoUser != null) {
                JSONObject object = ssoUser.getJSONObject(idCard);
                if (object != null) {
                    JSONArray organization = object.getJSONArray("organization");
                    if (organization != null && !organization.isEmpty()) {
                        String organizationId = organization.getJSONObject(0).getString("organization_id");
                        policeBasicInfo.setOrganizationId(organizationId);
                    }
                }

            }

        }

        policeBasicInfoService.updateBatchById(policeBasicInfos);

    }

}
