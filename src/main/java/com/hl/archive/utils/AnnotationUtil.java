package com.hl.archive.utils;

import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class AnnotationUtil {

    // 缓存结构：ClassName -> fieldName -> 注解值
    private static final Map<Class<?>, Map<String, String>> FIELD_DESC_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取某个类中字段的 @ApiModelProperty 注解值（如：身份证号）
     *
     * @param clazz     类
     * @param fieldName 字段名
     * @return 注解 value（如未找到返回 null）
     */
    public static String getApiModelPropertyValue(Class<?> clazz, String fieldName) {
        // 尝试从缓存中获取
        Map<String, String> fieldDescMap = FIELD_DESC_CACHE.computeIfAbsent(clazz, k -> {
            Map<String, String> map = new ConcurrentHashMap<>();
            for (Field field : clazz.getDeclaredFields()) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                if (annotation != null) {
                    map.put(field.getName(), annotation.value());
                }
            }
            return map;
        });

        return fieldDescMap.get(fieldName);
    }
}
