package com.hl.archive.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;

public class DictUtils {

    public static R<String> getDictValue(String dictName, String dictKey) {
        JSONObject dict = DictCache.ID_MAP_TREE.getJSONObject(dictName);
        if (dict == null) {
            return R.fail("未找到字典");
        }
        // 先检查当前字典本身是否匹配
        if (dictKey.equals(dict.getString("id"))) {
            return R.ok(dict.getString("name"));
        }
        // 递归查找子节点
        return findInChildren(dict, dictKey);
    }

    private static R<String> findInChildren(JSONObject node, String dictKey) {
        JSONArray children = node.getJSONArray("children");
        if (children == null || children.isEmpty()) {
            return R.fail("未找到值");
        }
        for (int i = 0; i < children.size(); i++) {
            JSONObject item = children.getJSONObject(i);
            // 检查当前节点是否匹配
            if (dictKey.equals(item.getString("id"))) {
                return R.ok(item.getString("name"));
            }
            // 递归检查子节点
            if (item.getJSONArray("children") != null && !item.getJSONArray("children").isEmpty()) {
                R<String> result = findInChildren(item, dictKey);
                if (R.isSuccess(result)) {
                    return result;
                }
            }
        }
        return R.fail("未找到值");
    }
}
