package com.hl.archive.utils;

import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@TranslationType(type = TransConstants.FJ_WEB_SITE_URL)
@Configuration
@Slf4j
public class FjWebSiteUrlTransImpl implements TranslationInterface<String> {
    @Override
    public String translation(Object o, String s) {
        String url = o.toString();
        return "http://50.56.116.11" + url;
    }
}
