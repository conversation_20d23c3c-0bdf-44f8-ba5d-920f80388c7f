package com.hl.archive.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class LeaveRecordUtil {

    /**
     * 请假区间对象
     */
    @Data
    @AllArgsConstructor
    public static class LeaveRange {
        private LocalDateTime start;
        private LocalDateTime end;
    }

    /**
     * 计算请假总天数（自动合并重叠区间）
     * @param ranges 请假区间列表
     * @return 总天数（包含首尾，所以 +1）
     */
    public static long calculateLeaveDays(List<LeaveRange> ranges) {
        if (ranges == null || ranges.isEmpty()) {
            return 0;
        }

        // 按开始时间排序
        List<LeaveRange> sorted = ranges.stream()
                .sorted(Comparator.comparing(LeaveRange::getStart))
                .collect(Collectors.toList());

        List<LeaveRange> merged = new ArrayList<>();
        LeaveRange current = sorted.get(0);

        for (int i = 1; i < sorted.size(); i++) {
            LeaveRange next = sorted.get(i);
            // 判断是否重叠
            if (!next.getStart().isAfter(current.getEnd())) {
                // 有重叠 → 合并
                current = new LeaveRange(
                        current.getStart(),
                        next.getEnd().isAfter(current.getEnd()) ? next.getEnd() : current.getEnd()
                );
            } else {
                merged.add(current);
                current = next;
            }
        }
        merged.add(current);

        // 计算总天数（含首尾）
        long total = 0;
        for (LeaveRange r : merged) {
            total += LocalDateTimeUtil.between(r.getStart(), r.getEnd()).toDays() ;
        }
        return total;
    }
}
