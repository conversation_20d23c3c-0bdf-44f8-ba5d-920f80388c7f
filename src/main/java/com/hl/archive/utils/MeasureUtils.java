package com.hl.archive.utils;

import com.google.common.collect.Sets;

import java.util.Set;

public class MeasureUtils {

    /**
     * 行政拘留
     */
    public static final Set<String> XING_ZHENG_JU_LIU = Sets.newHashSet("23113",
            "23115",
            "24119",
            "2411A");

    /**
     * 传唤
     */
    public static final Set<String> XING_ZHENG_CHUAN_HUAN = Sets.newHashSet(
            "21111","21121","21131","21141"
    );

    /**
     * 警告
     */
    public static final Set<String> JING_GAO = Sets.newHashSet(
            "24111","24115","23111","23114"
    );

    /**
     * 罚款
     */
    public static final Set<String> FA_KUAN = Sets.newHashSet(
            "24112","24115","24116","2411A","23112","23114","23115"
    );

    /**
     * 不予处罚
     */
    public static final Set<String> BU_YU_CHU_FA = Sets.newHashSet(
            "23119","2411C","2411Z"
    );

    /**
     * 刑事传唤
     */
    public static final Set<String> XING_SHI_CHUAN_HUAN = Sets.newHashSet(
            "11111"
    );

    /**
     * 刑事拘留
     */
    public static final Set<String> XING_SHI_JU_LIU = Sets.newHashSet(
            "12211"
    );

    /**
     * 取保候审
     */
    public static final Set<String> QU_BAO_HOU_SHEN = Sets.newHashSet(
            "12411","12412"
    );

    /**
     * 监视居住
     */
    public static final Set<String> JIAN_SHI_JU_ZHU = Sets.newHashSet(
            "12331","12311"
    );

    /**
     *  逮捕
     */
    public static final Set<String> DAI_BU = Sets.newHashSet(
            "12512"
    );

    /**
     * 移送直诉
     */
    public static final Set<String> YI_SONG_ZHI_SU = Sets.newHashSet(
            "12554","12552"
    );

    /**
     * 提请起诉
     */
    public static final Set<String> TI_QING_QI_SU = Sets.newHashSet("12512");

}
