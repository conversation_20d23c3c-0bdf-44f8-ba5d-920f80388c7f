package com.hl.archive.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@TranslationType(type = TransConstants.ORGANIZATION_TO_NAME)
@Configuration
@Slf4j
public class OrganizationNameTransImpl implements TranslationInterface<String> {

    @Override
    public String translation(Object o, String s) {
        // 如果是 List<String>
        try {
            return SsoCacheUtil.getOrganizationName(o.toString());
        } catch (Exception e) {
            log.error("转换用户名称失败: {}", s, e);
        }
        return o.toString();

    }
}
