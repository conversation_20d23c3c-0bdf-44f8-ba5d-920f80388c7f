package com.hl.archive.utils;

import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@TranslationType(type = TransConstants.PROJECT_FILE_URL)
@Configuration
@Slf4j
public class ProjectFileUrlTransImpl implements TranslationInterface<String> {
    @Override
    public String translation(Object o, String s) {
        String url = o.toString();
        return url.replace("E:/UploadDocument", "http://50.56.40.105:90/fj2");
    }
}
