package com.hl.archive.utils;

import com.alibaba.fastjson2.JSONObject;
import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@TranslationType(type = TransConstants.ID_CARD_TO_USER_OBJ)
@Configuration
@Slf4j
public class UserObjTransImpl implements TranslationInterface<JSONObject> {

    @Override
    public JSONObject translation(Object o, String s) {

        // 如果是 List<String>
        try {
            Object idCard = SsoCacheUtil.getUserObjByIdCard(o.toString());
            JSONObject data = JSONObject.from(idCard);
           return data;
        } catch (Exception e) {
            log.error("转换用户名称失败: {}", s, e);
        }
        return null;
    }
}
