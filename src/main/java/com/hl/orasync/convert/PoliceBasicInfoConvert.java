package com.hl.orasync.convert;

import cn.hutool.core.convert.impl.DateConverter;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.orasync.domain.VWjRyjbxx;
import com.hl.orasync.util.ConversionUtils;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Date;

@Mapper(uses = {ConversionUtils.class})
public interface PoliceBasicInfoConvert {

    PoliceBasicInfoConvert INSTANCE = Mappers.getMapper(PoliceBasicInfoConvert.class);


    @Mappings({
            @Mapping(source = "zp", target = "imgUrl"),
            @Mapping(source = "xm", target = "name"),
            @Mapping(source = "gmsfhm", target = "idCard"),
            @Mapping(source = "xb", target = "gender", qualifiedByName = "mapGender"),
            @Mapping(source = "csrq", target = "birthDate", qualifiedByName = "strToLocalDate"),
            @Mapping(source = "jg", target = "nativePlace"),
            @Mapping(source = "mz", target = "nation"),
            @Mapping(source = "jh", target = "policeNumber"),
            @Mapping(source = "dwmc", target = "unitName"),
            @Mapping(source = "bm", target = "department"),
            @Mapping(source = "ldzwcj", target = "leadershipLevel"),
            @Mapping(source = "cjgagzrq", target = "policeWorkStartDate", qualifiedByName = "strToLocalDate"),
            @Mapping(source = "cjgzrq", target = "workStartDate", qualifiedByName = "strToDate"),
//            @Mapping(source = "sg", target = "height", qualifiedByName = "strToBigDecimal"),
//            @Mapping(source = "tz", target = "weight", qualifiedByName = "strToBigDecimal"),
            @Mapping(source = "xx", target = "bloodType"),
            @Mapping(source = "rjqd",target = "entryChannel")
    })
    PoliceBasicInfo convertPoliceBasicInfo(VWjRyjbxx vWjRyjbxx);
}
