package com.hl.orasync.convert;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.orasync.domain.VWjRyzwzj;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PolicePositionRankConvert {

    PolicePositionRankConvert INSTANCE = Mappers.getMapper(PolicePositionRankConvert.class);

    @Mappings({
            @Mapping(source = "zwmc", target = "positionName"),
            @Mapping(source = "gazwjb", target = "policePositionLevel"),
            @Mapping(source = "zwsxsj", target = "currentPositionDate"),
            @Mapping(source = "xzjsj", target = "currentRankDate"),
            @Mapping(source = "rzwh", target = "appointmentDocument"),
            @Mapping(source = "gmsfhm", target = "idCard")
    })
    PolicePositionRank convertPolicePositionRank(VWjRyzwzj vwjRyzwzj);
}
