package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceHkMacauTaiwanPermit;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 港澳台通行证
 */
@Data
@TableName(value = "ZTJC.V_WJ_BR_GATTXZ")
@AutoMapper(target = PoliceHkMacauTaiwanPermit.class, uses = {ConversionUtils.class})
public class VWjBrGattxz {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "ZJHM")
    @AutoMapping(target = "documentNumber")
    private String zjhm;

    @TableField(value = "ZJMC")
    @AutoMapping(target = "documentName")
    private String zjmc;

    @TableField(value = "QFRQ")
    @AutoMapping(target = "issueDate",qualifiedByName = "strToDate")
    private String qfrq;

    @TableField(value = "YXQZ")
    @AutoMapping(target = "expiryDate",qualifiedByName = "strToDate")
    private String yxqz;

    @TableField(value = "BGJGMC")
    @AutoMapping(target = "custodyOrganization")
    private String bgjgmc;

    @TableField(value = "BZ")
    @AutoMapping(target = "remarks")
    private String bz;
}