package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceLoanInfo;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BR_GRJD")
@AutoMapper(target = PoliceLoanInfo.class, uses = {ConversionUtils.class})
public class VWjBrGrjd {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_JDDX")
    @AutoMapping(target = "lenderName")
    private String xmJddx;

    @TableField(value = "JDLXMC")
    @AutoMapping(target = "loanInfo")
    private String jdlxmc;

    @TableField(value = "JDYT")
    @AutoMapping(target = "loanPurpose")
    private String jdyt;

    @TableField(value = "JE")
    @AutoMapping(target = "loanAmount",qualifiedByName = "strToBigDecimal")
    private String je;

    @TableField(value = "HKQX")
    @AutoMapping(target = "repaymentDeadline",qualifiedByName = "strToDate")
    private String hkqx;

    @TableField(value = "JDRQ")
    @AutoMapping(target = "loanDate",qualifiedByName = "strToDate")
    private String jdrq;
}