package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import com.hl.archive.domain.entity.PoliceWeddingFuneralEvents;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BR_HSJQ")
@AutoMapper(target = PoliceWeddingFuneralEvents.class,uses = {ConversionUtils.class})
public class VWjBrHsjq {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_DSR")
    @AutoMapping(target = "partyName")
    private String xmDsr;

    @TableField(value = "GX_DSR")
    @AutoMapping(target = "relationshipWithParty")
    private String gxDsr;

    @TableField(value = "CBLXMC")
    @AutoMapping(target = "eventType")
    private String cblxmc;

    @TableField(value = "JE")
    @AutoMapping(target = "expenseAmount",qualifiedByName = "strToBigDecimal")
    private String je;

    @TableField(value = "CYRS")
    @AutoMapping(target = "participantCount",qualifiedByName = "bigDecimalToInteger")
    private BigDecimal cyrs;

    @TableField(value = "CBRQ")
    @AutoMapping(target = "eventDate",qualifiedByName = "strToDate")
    private String cbrq;
}