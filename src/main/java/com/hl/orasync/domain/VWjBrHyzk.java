package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceMarriageStatus;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;


/**
 * 婚姻状态
 */
@Data
@TableName(value = "ZTJC.V_WJ_BR_HYZK")
@AutoMapper(target = PoliceMarriageStatus.class, uses = {ConversionUtils.class})
public class VWjBrHyzk {
    @TableField(value = "XM")
    @AutoMapping(target = "name")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "BHSJ")
    @AutoMapping(target = "changeDate", qualifiedByName = "strToDate")
    private String bhsj;

    @TableField(value = "DJSJ")
    private String djsj;

    @TableField(value = "HYZTMC")
    @AutoMapping(target = "marriageStatus")
    private String hyztmc;

    @TableField(value = "BHHYZTMC")
    @AutoMapping(target = "changeStatus")
    private String bhhyztmc;
}