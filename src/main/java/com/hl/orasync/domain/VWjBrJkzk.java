package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceHealthStatus;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BR_JKZK")
@AutoMapper(target = PoliceHealthStatus.class, uses = {ConversionUtils.class})
public class VWjBrJkzk {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "JBMC")
    @AutoMapping(target = "illnessName")
    private String jbmc;

    @TableField(value = "JBMS")
    private String jbms;

    @TableField(value = "ZCLZ_PDBZMC")
    private String zclzPdbzmc;

    @TableField(value = "ZYSJ")
    @AutoMapping(target = "diagnosisDate", qualifiedByName = "strToDate")
    private String zysj;

    @TableField(value = "ZYJSSJ")
    private String zyjssj;

    @TableField(value = "ZYDD")
    private String zydd;
}