package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PolicePassport;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 普通护照
 */
@Data
@TableName(value = "ZTJC.V_WJ_BR_PTHZ")
@AutoMapper(target = PolicePassport.class, uses = {ConversionUtils.class})
public class VWjBrPthz {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "GLBH")
    private String glbh;

    @TableField(value = "HZHM")
    @AutoMapping(target = "passportNumber")
    private String hzhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "QFRQ")
    @AutoMapping(target = "issueDate",qualifiedByName = "strToDate")
    private String qfrq;

    @TableField(value = "YXQZ")
    @AutoMapping(target = "expiryDate",qualifiedByName = "strToDate")
    private String yxqz;

    @TableField(value = "BGJGMC")
    @AutoMapping(target = "custodyOrganization")
    private String bgjgmc;

    @TableField(value = "BZ")
    @AutoMapping(target = "remarks")
    private String bz;
}