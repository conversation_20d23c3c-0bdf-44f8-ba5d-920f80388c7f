package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceInvestmentInfo;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BR_TZQK")
@AutoMapper(target = PoliceInvestmentInfo.class, uses = {ConversionUtils.class})
public class VWjBrTzqk {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "TZCPMC")
    @AutoMapping(target = "investmentEntity")
    private String tzcpmc;

    @TableField(value = "JE")
    @AutoMapping(target = "investmentAmount", qualifiedByName = "strToBigDecimal")
    private String je;

    @TableField(value = "TZSJ")
    @AutoMapping(target = "transactionDate", qualifiedByName = "strToDate")
    private String tzsj;

    @TableField(value = "TZQX")
    @AutoMapping(target = "investmentSource")
    private String tzqx;
}