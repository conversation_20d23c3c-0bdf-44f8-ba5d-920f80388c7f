package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceOrganizationalInquiry;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BR_WJDC")
@AutoMapper(target = PoliceOrganizationalInquiry.class,uses = {ConversionUtils.class})
public class VWjBrWjdc {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "DCRQ")
    @AutoMapping(target = "handlingDate",qualifiedByName = "strToDate")
    private String dcrq;

    @TableField(value = "DCDD")
    private String dcdd;

    @TableField(value = "DCJG")
    @AutoMapping(target = "handlingAuthority")
    private String dcjg;

    @TableField(value = "WFQK")
    @AutoMapping(target = "suspectedViolations")
    private String wfqk;

    @TableField(value = "JTQK")
    private String jtqk;
}