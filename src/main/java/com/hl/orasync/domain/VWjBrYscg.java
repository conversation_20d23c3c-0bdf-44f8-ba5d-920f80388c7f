package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceOverseasTravel;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BR_YSCG")
@AutoMapper(target = PoliceOverseasTravel.class,uses = {ConversionUtils.class})
public class VWjBrYscg {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "HZHM")
    @AutoMapping(target = "passportNumber")
    private String hzhm;

    @TableField(value = "KSSJ")
    @AutoMapping(target = "startDate",qualifiedByName = "strToDate")
    private String kssj;

    @TableField(value = "JSSJ")
    @AutoMapping(target = "endDate",qualifiedByName = "strToDate")
    private String jssj;

    @TableField(value = "SDGJ")
    @AutoMapping(target = "destinationCountry")
    private String sdgj;

    @TableField(value = "SY")
    @AutoMapping(target = "travelReason")
    private String sy;

    @TableField(value = "SPJGMC")
    @AutoMapping(target = "approvalAuthority")
    private String spjgmc;
}