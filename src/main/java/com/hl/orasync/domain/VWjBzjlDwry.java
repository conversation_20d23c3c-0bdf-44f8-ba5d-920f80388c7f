package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceUnitAward;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BZJL_DWRY")
@AutoMapper(target = PoliceUnitAward.class, uses = {ConversionUtils.class})
public class VWjBzjlDwry {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "JLMC")
    @AutoMapping(target = "awardName")
    private String jlmc;

    @TableField(value = "BZSJ")
    @AutoMapping(target = "awardTime", qualifiedByName = "strToDate")
    private String bzsj;

    @TableField(value = "JLJGMC")
    @AutoMapping(target = "awardOrgan")
    private String jljgmc;

    @TableField(value = "BZWH")
    @AutoMapping(target = "documentNumber")
    private String bzwh;

    @TableField(value = "XM")
    @AutoMapping(target = "supervisorName")
    private String xm;

    @TableField(value = "JH")
    @AutoMapping(target = "supervisorCode")
    private String jh;

    @TableField(value = "DWBM")
    private String dwbm;

    @TableField(value = "DWDM")
    private String dwdm;

    @TableField(value = "DWMC")
    @AutoMapping(target = "unit")
    private String dwmc;
}