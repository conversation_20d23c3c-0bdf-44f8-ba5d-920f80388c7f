package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_BZJL_GRRY")
@AutoMapper(target = PoliceHonors.class, uses = {ConversionUtils.class})
public class VWjBzjlGrry {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "bh")
    private String xxzjbh;

    /**
     * 姓名
     */
    @TableField(value = "XM")
    private String xm;

    /**
     * 性别
     */
    @TableField(value = "XBMC")
    private String xbmc;

    /**
     * 警号
     */
    @TableField(value = "JH")
    private String jh;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    /**
     * 所在单位
     */
    @TableField(value = "DWMC")
    @AutoMapping(target = "organizationName")
    private String dwmc;

    /**
     * 奖励名称
     */
    @TableField(value = "JLMC")
    @AutoMapping(target = "honorName")
    private String jlmc;

    /**
     * 授奖时间
     */
    @TableField(value = "BZSJ")
    @AutoMapping(target = "awardDate", qualifiedByName = "strToLocalDate")
    private String bzsj;

    /**
     * 奖励机关
     */
    @TableField(value = "JLJGMC")
    @AutoMapping(target = "approveAuthority")
    private String jljgmc;

    /**
     * 表彰文件文号
     */
    @TableField(value = "BZWH")
    @AutoMapping(target = "awardDocNo")
    private String bzwh;

    /**
     * 荣誉级别
     */
    @TableField(value = "RYJBMC")
    @AutoMapping(target = "honorLevel")
    private String ryjbmc;
}