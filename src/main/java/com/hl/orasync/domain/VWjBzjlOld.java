package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="FJXX.V_WJ_BZJL_OLD")
@Data
@TableName(value = "FJXX.V_WJ_BZJL_OLD")
public class VWjBzjlOld {
    /**
     * 主键编号
     */
    @TableField(value = "ID")
    @ApiModelProperty(value="主键编号")
    private String id;

    /**
     * 人员主键编号
     */
    @TableField(value = "PERSONID")
    @ApiModelProperty(value="人员主键编号")
    private String personid;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    @ApiModelProperty(value="身份证号")
    private String gmsfhm;

    /**
     * 奖惩名称
     */
    @TableField(value = "JCMC")
    @ApiModelProperty(value="奖惩名称")
    private String jcmc;

    /**
     * 奖惩类别
     */
    @TableField(value = "JCLB")
    @ApiModelProperty(value="奖惩类别")
    private String jclb;

    /**
     * 奖惩等级
     */
    @TableField(value = "JCDJ")
    @ApiModelProperty(value="奖惩等级")
    private String jcdj;

    /**
     * 奖惩时间
     */
    @TableField(value = "JCSJ")
    @ApiModelProperty(value="奖惩时间")
    private String jcsj;

    /**
     * 批准机关
     */
    @TableField(value = "DWMC")
    @ApiModelProperty(value="批准机关")
    private String dwmc;

    /**
     * 奖惩文号
     */
    @TableField(value = "JCBH")
    @ApiModelProperty(value="奖惩文号")
    private String jcbh;
}