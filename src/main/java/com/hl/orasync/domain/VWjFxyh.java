package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_FXYH")
public class VWjFxyh {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    private String xxzjbh;

    /**
     * 姓名
     */
    @TableField(value = "XM")
    private String xm;

    /**
     * 年代
     */
    @TableField(value = "ND")
    private String nd;

    /**
     * 月份
     */
    @TableField(value = "YF")
    private String yf;

    /**
     * 警号
     */
    @TableField(value = "JH")
    private String jh;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    private String gmsfhm;

    /**
     * 单位名称
     */
    @TableField(value = "DWMC")
    private String dwmc;

    /**
     * 职务
     */
    @TableField(value = "ZWMC")
    private String zwmc;

    /**
     * 性别
     */
    @TableField(value = "XB")
    private String xb;

    /**
     * 出生日期
     */
    @TableField(value = "CSRQ")
    private String csrq;

    /**
     * 工作日期
     */
    @TableField(value = "GZRQ")
    private String gzrq;

    /**
     * 政治面貌
     */
    @TableField(value = "ZZMMMC")
    private String zzmmmc;

    /**
     * 入党(团)时间
     */
    @TableField(value = "CJDPSJ")
    private String cjdpsj;

    /**
     * 职务层次(一)
     */
    @TableField(value = "ZWCCMC1")
    private String zwccmc1;

    /**
     * 职务层次(二)
     */
    @TableField(value = "ZWCCMC2")
    private String zwccmc2;

    /**
     * 职级
     */
    @TableField(value = "ZJMC")
    private String zjmc;

    /**
     * 警种岗位
     */
    @TableField(value = "JZGWMC")
    private String jzgwmc;

    /**
     * 风险状况
     */
    @TableField(value = "FXZK_XXQK")
    private String fxzkXxqk;

    /**
     * 风险类型
     */
    @TableField(value = "FXLX")
    private String fxlx;

    /**
     * 风险等级
     */
    @TableField(value = "FXJBMC")
    private String fxjbmc;

    /**
     * 关爱措施
     */
    @TableField(value = "GACS")
    private String gacs;

    /**
     * 关爱记录
     */
    @TableField(value = "GAJL")
    private String gajl;

    /**
     * 当月变化
     */
    @TableField(value = "DYBH")
    private String dybh;

    /**
     * 处理处分时间
     */
    @TableField(value = "CFSJ")
    private String cfsj;

    /**
     * 处理处分分类
     */
    @TableField(value = "CFFLMC")
    private String cfflmc;

    /**
     * 处理处分类别
     */
    @TableField(value = "CFLBMC")
    private String cflbmc;

    /**
     * 责任领导
     */
    @TableField(value = "ZRLDXM")
    private String zrldxm;

    /**
     * 政工主管
     */
    @TableField(value = "ZGZGXM")
    private String zgzgxm;

    /**
     * 分管领导
     */
    @TableField(value = "FGLDXM")
    private String fgldxm;

    /**
     * 家庭住址
     */
    @TableField(value = "DZMC")
    private String dzmc;

    /**
     * 联系电话
     */
    @TableField(value = "SJHM")
    private String sjhm;

    /**
     * 家庭成员
     */
    @TableField(value = "JTCY_XXQK")
    private String jtcyXxqk;
}