package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_JCSJ_SCSB")
@AutoMapper(target = PoliceMomentSubmission.class, uses = {ConversionUtils.class})
public class VWjJcsjScsb {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "BSDW")
    private String bsdw;

    @TableField(value = "BSDWMC")
    @AutoMapping(target = "submitUnit")
    private String bsdwmc;

    @TableField(value = "BSR")
    private String bsr;

    @TableField(value = "BSRXM")
    @AutoMapping(target = "submitter")
    private String bsrxm;

    @TableField(value = "CJMJ")
    @AutoMapping(target = "officerName")
    private String cjmj;

    @TableField(value = "CLLX")
    private String cllx;

    @TableField(value = "CLLXMC")
    @AutoMapping(target = "materialType")
    private String cllxmc;

    @TableField(value = "BSLX")
    private String bslx;

    @TableField(value = "BSLXMC")
    @AutoMapping(target = "submissionType")
    private String bslxmc;

    @TableField(value = "DJSJ")
    @AutoMapping(target = "submissionTime", qualifiedByName = "strToDate")
    private String djsj;

    @TableField(value = "SCJJ")
    @AutoMapping(target = "materialIntro")
    private String scjj;

    @TableField(value = "SHJG")
    private String shjg;

    @TableField(value = "SHJGMC")
    @AutoMapping(target = "auditResult")
    private String shjgmc;
}