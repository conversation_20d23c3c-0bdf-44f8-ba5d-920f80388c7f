package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_JCSJ_SCSB_WJ")
@AutoMapper(target = PoliceMomentSubmissionVideo.class, uses = {ConversionUtils.class}
)
public class VWjJcsjScsbWj {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "SB_XXZJBH")
    @AutoMapping(target = "sbZjbh")
    private String sbXxzjbh;

    @TableField(value = "WJLX")
    private String wjlx;

    @TableField(value = "WJLXMC")
    @AutoMapping(target = "fileType")
    private String wjlxmc;

    @TableField(value = "WJMC")
    @AutoMapping(target = "fileName")
    private String wjmc;

    @TableField(value = "FJCL2")
    @AutoMapping(target = "fileUrl")
    private String fjcl2;
}