package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_LZFX")
public class VWjLzfx {
    @TableField(value = "DWBM")
    private String dwbm;

    @TableField(value = "DWMC")
    private String dwmc;

    @TableField(value = "CZD01")
    private String czd01;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "YF")
    private String yf;

    @TableField(value = "FXDID")
    private String fxdid;

    @TableField(value = "FXDMC")
    private String fxdmc;

    @TableField(value = "FXID")
    private String fxid;

    @TableField(value = "FXMC")
    private String fxmc;

    @TableField(value = "FKCS")
    private String fkcs;

    @TableField(value = "JTLSQK")
    private String jtlsqk;

    @TableField(value = "FXJB")
    private String fxjb;

    @TableField(value = "SJYJ")
    private String sjyj;

    @TableField(value = "FXJBMC")
    private String fxjbmc;
}