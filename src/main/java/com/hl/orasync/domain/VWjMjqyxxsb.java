package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceInjuryDeclare;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_MJQYXXSB")
@AutoMapper(target = PoliceInjuryDeclare.class)
public class VWjMjqyxxsb {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    private String xxzjbh;

    /**
     * 姓名
     */
    @TableField(value = "XM")
    @AutoMapping(target = "name")
    private String xm;

    /**
     * 警号
     */
    @TableField(value = "JH")
    @AutoMapping(target = "policeNumber")
    private String jh;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    /**
     * 工作单位
     */
    @TableField(value = "GZDW_GAJGMC")
    @AutoMapping(target = "orgName")
    private String gzdwGajgmc;

    /**
     * 职务
     */
    @TableField(value = "ZWMC")
    @AutoMapping(target = "position")
    private String zwmc;

    /**
     * 申报类别
     */
    @TableField(value = "SQLBMC")
    @AutoMapping(target = "declareType")
    private String sqlbmc;

    /**
     * 当前状态
     */
    @TableField(value = "HCRDZTMC")
    @AutoMapping(target = "currentStatus")
    private String hcrdztmc;

    /**
     * 申请理由
     */
    @TableField(value = "SQLY")
    @AutoMapping(target = "injuryEvent")
    private String sqly;
}