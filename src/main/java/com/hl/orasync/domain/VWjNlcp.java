package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_NLCP")
@AutoMapper(target = PoliceCapabilityEval.class)
public class VWjNlcp {
    /**
     * 流程ID
     */
    @TableField(value = "LCID")
    @AutoMapping(target = "lcid")
    private String lcid;

    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "xxzjbh")
    private String xxzjbh;

    /**
     * 参评人
     */
    @TableField(value = "SQRXM")
    @AutoMapping(target = "participantName")
    private String sqrxm;

    /**
     * 警号
     */
    @TableField(value = "JH")
    @AutoMapping(target = "policeNumber")
    private String jh;

    /**
     * 职务
     */
    @TableField(value = "ZWMC")
    @AutoMapping(target = "position")
    private String zwmc;

    /**
     * 单位
     */
    @TableField(value = "DWMC")
    @AutoMapping(target = "orgName")
    private String dwmc;

    /**
     * 方案名称
     */
    @TableField(value = "FAMC")
    @AutoMapping(target = "planName")
    private String famc;

    /**
     * 大类名称
     */
    @TableField(value = "DLMC")
    @AutoMapping(target = "categoryName")
    private String dlmc;

    /**
     * 特征名称
     */
    @TableField(value = "BQ_MC")
    @AutoMapping(target = "featureName")
    private String bqMc;

    /**
     * 审核人
     */
    @TableField(value = "SHRXM")
    @AutoMapping(target = "reviewer")
    private String shrxm;

    /**
     * 参评状态
     */
    @TableField(value = "FSZT")
    @AutoMapping(target = "evalStatus")
    private String fszt;

    /**
     * 参评档次
     */
    @TableField(value = "BQZ_MC")
    @AutoMapping(target = "evalLevel")
    private String bqzMc;

    /**
     * 审核结果
     */
    @TableField(value = "SHJGMC")
    @AutoMapping(target = "reviewResult")
    private String shjgmc;
}