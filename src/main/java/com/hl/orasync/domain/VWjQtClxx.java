package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyVehicles;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_QT_CLXX")
@AutoMapper(target = PoliceFamilyVehicles.class,uses = {ConversionUtils.class})
public class VWjQtClxx {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_CQR")
    @AutoMapping(target = "ownerName")
    private String xmCqr;

    @TableField(value = "CLPP")
    @AutoMapping(target = "vehicleBrand")
    private String clpp;

    @TableField(value = "HPHM")
    @AutoMapping(target = "licensePlate")
    private String hphm;

    @TableField(value = "JE")
    @AutoMapping(target = "transactionAmount",qualifiedByName = "strToBigDecimal")
    private String je;

    @TableField(value = "GMSJ")
    @AutoMapping(target = "transactionDate",qualifiedByName = "strToDate")
    private String gmsj;

    @TableField(value = "CLLYMC")
    @AutoMapping(target = "vehicleSource")
    private String cllymc;

    @TableField(value = "CLQXMC")
    @AutoMapping(target = "vehicleDisposition")
    private String clqxmc;

    @TableField(value = "CSSJ")
    @AutoMapping(target = "saleDate",qualifiedByName = "strToDate")
    private String cssj;

    @TableField(value = "CSJG")
    @AutoMapping(target = "saleAmount",qualifiedByName = "strToBigDecimal")
    private String csjg;
}