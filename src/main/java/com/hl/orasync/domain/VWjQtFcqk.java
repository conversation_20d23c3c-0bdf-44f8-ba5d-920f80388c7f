package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyRealEstate;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

@Data
@TableName(value = "ZTJC.V_WJ_QT_FCQK")
@AutoMapper(target = PoliceFamilyRealEstate.class, uses = {ConversionUtils.class})
public class VWjQtFcqk {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_CQR")
    @AutoMapping(target = "propertyOwnerName")
    private String xmCqr;

    @TableField(value = "FCLY")
    private String fcly;

    @TableField(value = "DZ")
    @AutoMapping(target = "propertyAddress")
    private String dz;

    @TableField(value = "JZMJ")
    @AutoMapping(target = "buildingArea")
    private String jzmj;

    @TableField(value = "FCLX")

    private String fclx;

    @TableField(value = "FCQX")
    private String fcqx;

    @TableField(value = "JYSJ")
    @AutoMapping(target = "transactionDate",qualifiedByName = "strToDate")
    private String jysj;

    @TableField(value = "JYJG")
    @AutoMapping(target = "transactionPrice")
    private String jyjg;

    @TableField(value = "FCLYMC")
    @AutoMapping(target = "propertySource")
    private String fclymc;

    @TableField(value = "FCLXMC")
    @AutoMapping(target = "propertyType")
    private String fclxmc;

    @TableField(value = "FCQXMC")
    @AutoMapping(target = "propertyDisposition")
    private String fcqxmc;

    @TableField(value = "CSSJ")
    @AutoMapping(target = "saleDate",qualifiedByName = "strToDate")
    private String cssj;

    @TableField(value = "CSJG")
    @AutoMapping(target = "salePrice")
    private String csjg;
}