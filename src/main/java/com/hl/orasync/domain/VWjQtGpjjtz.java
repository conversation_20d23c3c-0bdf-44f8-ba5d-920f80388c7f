package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilySecuritiesInsurance;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_QT_GPJJTZ")
@AutoMapper(target = PoliceFamilySecuritiesInsurance.class, uses = {ConversionUtils.class})
public class VWjQtGpjjtz {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "XM_CYR")
    @AutoMapping(target = "holderName")
    private String xmCyr;

    @TableField(value = "MCDM")
    @AutoMapping(target = "securityNameCode")
    private String mcdm;

    @TableField(value = "SL")
    @AutoMapping(target = "holdingQuantity")
    private String sl;

    @TableField(value = "RJZ")
    @AutoMapping(target = "netValuePremium")
    private String rjz;

    @TableField(value = "FCQX")
    private String fcqx;

    @TableField(value = "CSSJ")
    private String cssj;

    @TableField(value = "CSJG")
    private String csjg;

    @TableField(value = "FCQXMC")
    private String fcqxmc;
}