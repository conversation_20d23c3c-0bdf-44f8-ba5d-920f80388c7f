package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceDishonestExecutor;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_QT_POSXBZXR")
@AutoMapper(target = PoliceDishonestExecutor.class, uses = {ConversionUtils.class})
public class VWjQtPosxbzxr {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "RYGX")
    private String rygx;

    @TableField(value = "RYGXMC")
    @AutoMapping(target = "relationship")
    private String rygxmc;

    @TableField(value = "ZXJG")
    @AutoMapping(target = "executionUnit")
    private String zxjg;

    @TableField(value = "JTQK")
    @AutoMapping(target = "dishonestReason")
    private String jtqk;
}