package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceOtherMatters;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_QT_QTSX")
@AutoMapper(target = PoliceOtherMatters.class, uses = {ConversionUtils.class})
public class VWjQtQtsx {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "BZ")
    @AutoMapping(target = "reportContent")
    private String bz;

    @TableField(value = "DJRQ")
    @AutoMapping(target = "registrationDate",qualifiedByName = "strToDate")
    private String djrq;
}