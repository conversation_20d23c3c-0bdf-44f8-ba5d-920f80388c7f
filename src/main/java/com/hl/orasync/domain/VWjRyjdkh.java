package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceQuarterlyAssessment;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 季度考核
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJDKH")
@AutoMapper(target = PoliceQuarterlyAssessment.class)
public class VWjRyjdkh {
    @TableField(value = "DWMC")
    @AutoMapping(target = "assessmentUnit")
    private String dwmc;

    @TableField(value = "ND")
    @AutoMapping(target = "assessmentYear")
    private String nd;

    @TableField(value = "JD")
    @AutoMapping(target = "assessmentQuarter")
    private String jd;

    @TableField(value = "JGMC")
    @AutoMapping(target = "assessmentResult")
    private String jgmc;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}