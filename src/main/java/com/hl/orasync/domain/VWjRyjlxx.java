package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceResume;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 简历信息
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJLXX")
@AutoMapper(target = PoliceResume.class, uses = {ConversionUtils.class})
public class VWjRyjlxx {
    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "QSSJ")
    @AutoMapping(target = "startDate",qualifiedByName = "strToDate")
    private String qssj;

    @TableField(value = "JZSJ")
    @AutoMapping(target = "endDate",qualifiedByName = "strToDate")
    private String jzsj;

    @TableField(value = "SZDW")
    @AutoMapping(target = "workUnit")
    private String szdw;

    @TableField(value = "ZW")
    @AutoMapping(target = "position")
    private String zw;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}