package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyMembers;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 家庭成员
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJTCY")
@AutoMapper(target = PoliceFamilyMembers.class,uses = {ConversionUtils.class})
public class VWjRyjtcy {
    @TableField(value = "RYGX")
    @AutoMapping(target = "relationship")
    private String rygx;

    @TableField(value = "XM")
    @AutoMapping(target = "memberName")
    private String xm;

    @TableField(value = "CSRQ")
    @AutoMapping(target = "birthDate")
    private String csrq;

    @TableField(value = "GZDW")
    @AutoMapping(target = "workUnitPosition")
    private String gzdw;

    @TableField(value = "ZZMM")
    @AutoMapping(target = "politicalStatus")
    private String zzmm;

    @TableField(value = "JTCYBH")
    private String jtcybh;

    @TableField(value = "SJHM")
    @AutoMapping(target = "mobilePhone")
    private String sjhm;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}