package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceContactInfo;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 家庭住址
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJTZZ")
@AutoMapper(target = PoliceContactInfo.class,uses = {ConversionUtils.class})
public class VWjRyjtzz {
    @TableField(value = "JTDZ")
    @AutoMapping(target = "homeAddress")
    private String jtdz;

    @TableField(value = "JTDH")
    @AutoMapping(target = "homePhone")
    private String jtdh;

    @TableField(value = "SHHM")
    @AutoMapping(target = "mobilePhone")
    private String shhm;

    @TableField(value = "SHHMDH")
    @AutoMapping(target = "mobileShortNumber")
    private String shhmdh;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}