package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceRankInfo;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;


/**
 * 警衔信息
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJXXX")
@AutoMapper(target = PoliceRankInfo.class,uses = {ConversionUtils.class})
public class VWjRyjxxx {
    @TableField(value = "XC")
    @AutoMapping(target = "rankTitle")
    private String xc;

    @TableField(value = "SXSJ")
    @AutoMapping(target = "promotionDate",qualifiedByName = "strToDate")
    private String sxsj;

    @TableField(value = "SXYY")
    @AutoMapping(target = "promotionReason")
    private String sxyy;

    @TableField(value = "SXZL")
    @AutoMapping(target = "rankType")
    private String sxzl;

    @TableField(value = "SXSXZZJ")
    @AutoMapping(target = "adminLevelAtPromotion")
    private String sxsxzzj;

    @TableField(value = "XCZZRQ")
    @AutoMapping(target = "rankEndDate",qualifiedByName = "strToDate")
    private String xczzrq;

    @TableField(value = "XCQSRQ")
    @AutoMapping(target = "rankStartDate",qualifiedByName = "strToDate")
    private String xcqsrq;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}