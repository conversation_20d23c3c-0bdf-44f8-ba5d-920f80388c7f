package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceTraining;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 教育训练
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJYXL")
@AutoMapper(target = PoliceTraining.class,uses = {ConversionUtils.class})
public class VWjRyjyxl {
    @TableField(value = "PXBMC")
    @AutoMapping(target = "trainingName")
    private String pxbmc;

    @TableField(value = "PXQZSJ")
    @AutoMapping(target = "trainingStartDate",qualifiedByName = "strToDate")
    private String pxqzsj;

    @TableField(value = "PXZZSJ")
    @AutoMapping(target = "trainingEndDate",qualifiedByName = "strToDate")
    private String pxzzsj;

    @TableField(value = "PXZBDWMC")
    @AutoMapping(target = "organizerName")
    private String pxzbdwmc;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}