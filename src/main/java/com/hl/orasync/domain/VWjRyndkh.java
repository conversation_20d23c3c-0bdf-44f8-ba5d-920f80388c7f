package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceAnnualAssessment;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 年度考核
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYNDKH")
@AutoMapper(target = PoliceAnnualAssessment.class)
public class VWjRyndkh {
    @TableField(value = "KCLB")
    @AutoMapping(target = "assessmentCategory")
    private String kclb;

    @TableField(value = "KHJG")
    @AutoMapping(target = "assessmentResult")
    private String khjg;

    @TableField(value = "KCND")
    @AutoMapping(target = "assessmentYear")
    private String kcnd;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}