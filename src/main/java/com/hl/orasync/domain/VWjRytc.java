package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceSpecialties;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;


/**
 * 特长
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYTC")
@AutoMapper(target = PoliceSpecialties.class, uses = {ConversionUtils.class})
public class VWjRytc {

    @TableField(value = "JCMC")
    @AutoMapping(target = "specialtyName")
    private String jcmc;

    @TableField(value = "JCSJ")
    @AutoMapping(target = "awardDate",qualifiedByName = "strToDate")
    private String jcsj;

    @TableField(value = "JCPJJGMC")
    @AutoMapping(target = "approveAuthority")
    private String jcpjjgmc;

    @TableField(value = "BH")
    private String bh;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}