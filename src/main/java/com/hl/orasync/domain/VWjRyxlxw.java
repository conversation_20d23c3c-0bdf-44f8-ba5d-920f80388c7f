package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceEducation;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 学历学位
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYXLXW")
@AutoMapper(target = PoliceEducation.class,uses = {ConversionUtils.class})
public class VWjRyxlxw {
    @TableField(value = "XL")
    @AutoMapping(target = "educationLevel")
    private String xl;

    @TableField(value = "XXMC")
    @AutoMapping(target = "schoolName")
    private String xxmc;

    @TableField(value = "ZYMC")
    @AutoMapping(target = "majorName")
    private String zymc;

    @TableField(value = "RXSJ")
    @AutoMapping(target = "enrollmentDate",qualifiedByName = "strToDate")
    private String rxsj;

    @TableField(value = "BYSJ")
    @AutoMapping(target = "graduationDate",qualifiedByName = "strToDate")
    private String bysj;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}