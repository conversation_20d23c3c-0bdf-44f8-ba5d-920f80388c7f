package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import com.hl.archive.domain.entity.PoliceMonthlyAssessment;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;


/**
 * 月度考核
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYYDKH")
@AutoMapper(target = PoliceMonthlyAssessment.class, uses = {ConversionUtils.class})
public class VWjRyydkh {
    @TableField(value = "KPTBPZMC")
    @AutoMapping(target = "assessmentNotice")
    private String kptbpzmc;

    @TableField(value = "ND")
    @AutoMapping(target = "assessmentYear")
    private String nd;

    @TableField(value = "YF")
    @AutoMapping(target = "assessmentMonth")
    private String yf;

    @TableField(value = "DF")
    @AutoMapping(target = "assessmentScore", qualifiedByName = "bigDecimalToString")
    private BigDecimal df;

    @TableField(value = "JJ")
    @AutoMapping(target = "assessmentBonus", qualifiedByName = "bigDecimalToString")
    private BigDecimal jj;

    @TableField(value = "PM")
    @AutoMapping(target = "assessmentRanking", qualifiedByName = "bigDecimalToString")
    private BigDecimal pm;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;
}