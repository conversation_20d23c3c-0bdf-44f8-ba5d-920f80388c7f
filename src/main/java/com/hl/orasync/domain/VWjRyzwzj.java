package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

/**
 * 职务职级
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYZWZJ")
@AutoMapper(target = PolicePositionRank.class,uses = {ConversionUtils.class})
public class VWjRyzwzj {

    @TableField(value = "ZWMC")
    @AutoMapping(target = "positionName")
    private String zwmc;

    @TableField(value = "GAZWJB")
    @AutoMapping(target = "policePositionLevel")
    private String gazwjb;

    @TableField(value = "ZWSXSJ")
    @AutoMapping(target = "currentPositionDate", qualifiedByName = "strToDate")
    private String zwsxsj;

    @TableField(value = "XZJSJ")
    @AutoMapping(target = "currentRankDate", qualifiedByName = "strToDate")
    private String xzjsj;

    @TableField(value = "RZWH")
    @AutoMapping(target = "appointmentDocument")
    private String rzwh;

    @TableField(value = "RKBM")
    private String rkbm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;


}