package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceViolationSummary;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_WGWJDJB")
@AutoMapper(target = PoliceViolationSummary.class,uses = {ConversionUtils.class})
public class VWjWgwjdjb {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "xxzjbh")
    private String xxzjbh;

    /**
     * 填报单位
     */
    @TableField(value = "DJDWMC")
    @AutoMapping(target = "reportOrg")
    private String djdwmc;

    /**
     * 问题线索来源
     */
    @TableField(value = "WTXSLYMC")
    @AutoMapping(target = "clueSource")
    private String wtxslymc;

    /**
     * 问题线索内容
     */
    @TableField(value = "WTXSNR")
    @AutoMapping(target = "clueContent")
    private String wtxsnr;

    /**
     * 发现时间
     */
    @TableField(value = "FXSJ")
    @AutoMapping(target = "foundDate",qualifiedByName = "strToLocalDate")
    private String fxsj;

    /**
     * 发现单位
     */
    @TableField(value = "FXDW")
    @AutoMapping(target = "foundOrg")
    private String fxdw;

    /**
     * 受理时间
     */
    @TableField(value = "SLSJ")
    @AutoMapping(target = "acceptDate",qualifiedByName = "strToLocalDate")
    private String slsj;

    /**
     * 受理单位
     */
    @TableField(value = "SLDW")
    @AutoMapping(target = "acceptOrg")
    private String sldw;

    /**
     * 问题线索移交(交办)时间
     */
    @TableField(value = "YJSJ")
    @AutoMapping(target = "transferDate",qualifiedByName = "strToLocalDate")
    private String yjsj;

    /**
     * 问题线索移交(交办)接收单位
     */
    @TableField(value = "YJDW")
    @AutoMapping(target = "transferOrg")
    private String yjdw;

    /**
     * 启动调查时间
     */
    @TableField(value = "DCSJ")
    @AutoMapping(target = "investigationStart",qualifiedByName = "strToLocalDate")
    private String dcsj;

    /**
     * 调查单位
     */
    @TableField(value = "DCDW")
    @AutoMapping(target = "investigationOrg")
    private String dcdw;

    /**
     * 调查情况
     */
    @TableField(value = "DCQK")
    @AutoMapping(target = "investigationResult")
    private String dcqk;

    /**
     * 启动初核时间
     */
    @TableField(value = "CHSJ")
    @AutoMapping(target = "preliminaryStart",qualifiedByName = "strToLocalDate")
    private String chsj;

    /**
     * 初核单位
     */
    @TableField(value = "CHDW")
    @AutoMapping(target = "preliminaryOrg")
    private String chdw;

    /**
     * 初核情况
     */
    @TableField(value = "CHQK")
    @AutoMapping(target = "preliminaryResult")
    private String chqk;

    /**
     * 会商时间
     */
    @TableField(value = "HSSJ")
    @AutoMapping(target = "meetingDate",qualifiedByName = "strToLocalDate")
    private String hssj;

    /**
     * 会商单位
     */
    @TableField(value = "HSDW")
    @AutoMapping(target = "meetingOrg")
    private String hsdw;

    /**
     * 会商结果
     */
    @TableField(value = "HSJG")
    @AutoMapping(target = "meetingResult")
    private String hsjg;

    /**
     * 立案时间
     */
    @TableField(value = "LASJ")
    @AutoMapping(target = "caseDate",qualifiedByName = "strToLocalDate")
    private String lasj;

    /**
     * 立案单位
     */
    @TableField(value = "LADW")
    @AutoMapping(target = "caseOrg")
    private String ladw;

    /**
     * 留置审查时间
     */
    @TableField(value = "LZSCSJ")
    @AutoMapping(target = "detentionDate",qualifiedByName = "strToLocalDate")
    private String lzscsj;

    /**
     * 留置审查单位
     */
    @TableField(value = "LZSCDW")
    @AutoMapping(target = "detentionOrg")
    private String lzscdw;

    /**
     * 主要违法违纪事实
     */
    @TableField(value = "WJWFSS")
    @AutoMapping(target = "violationFact")
    private String wjwfss;

    /**
     * 违规违纪类型
     */
    @TableField(value = "WJWFLXMC")
    @AutoMapping(target = "violationType")
    private String wjwflxmc;

    /**
     * 案件编号
     */
    @TableField(value = "AJBH")
    @AutoMapping(target = "caseNo")
    private String ajbh;
}