package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceViolationResult;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_WGWJDJB_RYCLJG")
@AutoMapper(target = PoliceViolationResult.class,uses = {ConversionUtils.class})
public class VWjWgwjdjbRycljg {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "xxzjbh")
    private String xxzjbh;

    /**
     * 问题表主键编号
     */
    @TableField(value = "WT_XXZJBH")
    @AutoMapping(target = "wtXxzjbh")
    private String wtXxzjbh;

    /**
     * 人员表主键编号
     */
    @TableField(value = "RY_XXZJBH")
    @AutoMapping(target = "ryXxzjbh")
    private String ryXxzjbh;

    /**
     * 处理类别
     */
    @TableField(value = "LBMC")
    @AutoMapping(target = "lbmc")
    private String lbmc;

    /**
     * 处理时间
     */
    @TableField(value = "CLSJ")
    @AutoMapping(target = "clsj",qualifiedByName = "strToLocalDate")
    private String clsj;

    /**
     * 处理单位
     */
    @TableField(value = "CLDW")
    @AutoMapping(target = "cldw")
    private String cldw;

    /**
     * 处理结果
     */
    @TableField(value = "CLJG")
    @AutoMapping(target = "cljg")
    private String cljg;
}