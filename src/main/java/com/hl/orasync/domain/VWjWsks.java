package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceOnlineExam;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName(value = "ZTJC.V_WJ_WSKS")
@AutoMapper(target = PoliceOnlineExam.class, uses = {ConversionUtils.class})
public class VWjWsks {
    @TableField(value = "SJMC")
    @AutoMapping(target = "examPaperName")
    private String sjmc;

    @TableField(value = "XXZJBH")
    private String xxzjbh;

    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "KSSJ")
    @AutoMapping(target = "startTime", qualifiedByName = "strToDate")
    private String kssj;

    @TableField(value = "JSSJ")
    @AutoMapping(target = "endTime", qualifiedByName = "strToDate")
    private String jssj;

    @TableField(value = "KSCS")
    private BigDecimal kscs;

    @TableField(value = "KSSC")
    @AutoMapping(target = "examDuration", qualifiedByName = "bigDecimalToInteger")
    private BigDecimal kssc;

    @TableField(value = "TMGS")
    @AutoMapping(target = "questionCount", qualifiedByName = "bigDecimalToInteger")
    private BigDecimal tmgs;

    @TableField(value = "SFJJ")
    @AutoMapping(target = "submitStatus")
    private String sfjj;

    @TableField(value = "DF")
    @AutoMapping(target = "score")
    private BigDecimal df;
}