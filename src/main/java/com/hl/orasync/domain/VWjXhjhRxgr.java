package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_XHJH_RXGR")
@AutoMapper(target = PoliceProjectEntryPerson.class,
        uses = {ConversionUtils.class})
public class VWjXhjhRxgr {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "XM")
    @AutoMapping(target = "name")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "JH")
    @AutoMapping(target = "policeNumber")
    private String jh;

    @TableField(value = "DWMC")
    @AutoMapping(target = "unit")
    private String dwmc;

    @TableField(value = "RYJBDM")
    private String ryjbdm;

    @TableField(value = "RYJBMC")
    @AutoMapping(target = "honorLevel")
    private String ryjbmc;

    @TableField(value = "ZGRYMC")
    @AutoMapping(target = "honorName")
    private String zgrymc;

    @TableField(value = "DJSJ")
    @AutoMapping(target = "entryTime",qualifiedByName = "strToDate")
    private String djsj;

    @TableField(value = "GXSJ")
    private String gxsj;

    @TableField(value = "PYLXR_XM")
    @AutoMapping(target = "contactPerson")
    private String pylxrXm;

    @TableField(value = "SHZT")
    private String shzt;

    @TableField(value = "SHZTMC")
    @AutoMapping(target = "auditStatus")
    private String shztmc;
}