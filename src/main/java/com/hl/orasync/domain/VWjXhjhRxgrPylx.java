package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceProjectContact;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_XHJH_RXGR_PYLX")
@AutoMapper(target = PoliceProjectContact.class, uses = {ConversionUtils.class})
public class VWjXhjhRxgrPylx {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "JL_XXZJBH")
    @AutoMapping(target = "xhjhZjbh")
    private String jlXxzjbh;

    @TableField(value = "DJSJ")
    @AutoMapping(target = "registerTime", qualifiedByName = "strToDate")
    private String djsj;

    @TableField(value = "DJRXM")
    @AutoMapping(target = "registeredBy")
    private String djrxm;

    @TableField(value = "PYLXR_XM")
    @AutoMapping(target = "contactName")
    private String pylxrXm;

    @TableField(value = "PYLXR_JH")
    private String pylxrJh;

    @TableField(value = "PYLXR_ZW")
    @AutoMapping(target = "contactPosition")
    private String pylxrZw;

    @TableField(value = "TZLD")
    @AutoMapping(target = "traits")
    private String tzld;

    @TableField(value = "PYPJ")
    @AutoMapping(target = "evaluation")
    private String pypj;
}