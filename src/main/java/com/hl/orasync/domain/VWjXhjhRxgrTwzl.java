package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceProjectMaterial;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_XHJH_RXGR_TWZL")
@AutoMapper(target = PoliceProjectMaterial.class, uses = {ConversionUtils.class})
public class VWjXhjhRxgrTwzl {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "JL_XXZJBH")
    @AutoMapping(target = "xhjhZjbh")
    private String jlXxzjbh;

    @TableField(value = "DJSJ")
    private String djsj;

    @TableField(value = "DJRXM")
    private String djrxm;

    @TableField(value = "TPZL_BT")
    @AutoMapping(target = "imageName")
    private String tpzlBt;

    @TableField(value = "TPZL_NR")
    @AutoMapping(target = "imageUrl")
    private String tpzlNr;

    @TableField(value = "TPZL_WJMC")
    private String tpzlWjmc;
}