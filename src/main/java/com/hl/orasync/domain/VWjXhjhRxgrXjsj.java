package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceProjectStory;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_XHJH_RXGR_XJSJ")
@AutoMapper(target = PoliceProjectStory.class, uses = {ConversionUtils.class})
public class VWjXhjhRxgrXjsj {
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "zjbh")
    private String xxzjbh;

    @TableField(value = "JL_XXZJBH")
    @AutoMapping(target = "xhjhZjbh")
    private String jlXxzjbh;

    @TableField(value = "DJSJ")
    @AutoMapping(target = "registerTime", qualifiedByName = "strToDate")
    private String djsj;

    @TableField(value = "DJRXM")
    @AutoMapping(target = "registeredBy")
    private String djrxm;

    @TableField(value = "XJSJ_BT")
    @AutoMapping(target = "title")
    private String xjsjBt;

    @TableField(value = "XJSJ_NR")
    @AutoMapping(target = "content")
    private String xjsjNr;

    @TableField(value = "XJSJ_FJ")
    private String xjsjFj;
}