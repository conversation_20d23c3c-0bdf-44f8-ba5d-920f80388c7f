package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceTrainingRecords;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName(value = "ZTJC.V_WJ_XLDA")
@AutoMapper(target = PoliceTrainingRecords.class)
public class VWjXlda {
    @TableField(value = "PXBMC")
    @AutoMapping(target = "trainingName")
    private String pxbmc;

    @TableField(value = "KPXMMC")
    @AutoMapping(target = "examProjectName")
    private String kpxmmc;

    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "JH")
    private String jh;

    @TableField(value = "CJ")
    @AutoMapping(target = "grade")
    private String cj;

    @TableField(value = "PFDF")
    @AutoMapping(target = "score")
    private String pfdf;

    @TableField(value = "PXSJ")
    @AutoMapping(target = "trainingTime")
    private LocalDateTime pxsj;
}