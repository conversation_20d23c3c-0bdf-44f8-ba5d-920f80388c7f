package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_YJBB")
@AutoMapper(target = PoliceDrinkReport.class,uses = {ConversionUtils.class})
public class VWjYjbb {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "xxzj")
    private String xxzjbh;

    /**
     * 姓名
     */
    @TableField(value = "XM")
    private String xm;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    /**
     * 单位名称
     */
    @TableField(value = "DWMC")
    private String dwmc;

    /**
     * 单位编码
     */
    @TableField(value = "DWBM")
    @AutoMapping(target = "organizationId")
    private String dwbm;

    /**
     * 唯一机构码
     */
    @TableField(value = "CZD01")
    private String czd01;

    /**
     * 职务
     */
    @TableField(value = "ZWMC")
    private String zwmc;

    /**
     * 饮酒时间
     */
    @TableField(value = "YJRQ")
    @AutoMapping(target = "drinkTime",qualifiedByName = "strToDate")
    private String yjrq;

    /**
     * 饮酒地点
     */
    @TableField(value = "YJDD")
    @AutoMapping(target = "location")
    private String yjdd;

    /**
     * 饮酒事由
     */
    @TableField(value = "YJSY")
    @AutoMapping(target = "reason")
    private String yjsy;

    /**
     * 邀约人
     */
    @TableField(value = "YYRXM")
    @AutoMapping(target = "inviter")
    private String yyrxm;

    /**
     * 参与人数
     */
    @TableField(value = "CYRS")
    @AutoMapping(target = "participants")
    private String cyrs;

    /**
     * 付款人
     */
    @TableField(value = "FKRXM")
    @AutoMapping(target = "payer")
    private String fkrxm;

    /**
     * 出行方式
     */
    @TableField(value = "CXFS")
    @AutoMapping(target = "travelMode")
    private String cxfs;

    /**
     * 备注
     */
    @TableField(value = "BZ")
    @AutoMapping(target = "remark")
    private String bz;

    /**
     * 审核结果
     */
    @TableField(value = "SHJG")
    @AutoMapping(target = "approveResult")
    private String shjg;
}