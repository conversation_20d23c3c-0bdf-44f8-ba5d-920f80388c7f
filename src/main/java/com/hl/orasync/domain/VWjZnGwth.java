package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceChildrenForeignMarriage;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_ZN_GWTH")
@AutoMapper(target = PoliceChildrenForeignMarriage.class, uses = {ConversionUtils.class})
public class VWjZnGwth {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_ZN")
    @AutoMapping(target = "childName")
    private String xmZn;

    @TableField(value = "XM_ZNPO")
    @AutoMapping(target = "spouseName")
    private String xmZnpo;

    @TableField(value = "GJ_ZNPO")
    @AutoMapping(target = "spouseCountry")
    private String gjZnpo;

    @TableField(value = "GZDW_ZNPO")
    @AutoMapping(target = "workStudyUnit")
    private String gzdwZnpo;

    @TableField(value = "ZW_ZNPO")
    @AutoMapping(target = "position")
    private String zwZnpo;

    @TableField(value = "DJRQ")
    @AutoMapping(target = "registrationDate",qualifiedByName = "strToDate")
    private String djrq;
}