package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyBusiness;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

@Data
@TableName(value = "ZTJC.V_WJ_ZN_JSBQY")
@AutoMapper(target = PoliceFamilyBusiness.class, uses = {ConversionUtils.class})
public class VWjZnJsbqy {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_FR")
    @AutoMapping(target = "name")
    private String xmFr;

    @TableField(value = "ZCH")
    @AutoMapping(target = "socialCreditCode")
    private String zch;

    @TableField(value = "QYMC")
    @AutoMapping(target = "enterpriseName")
    private String qymc;

    @TableField(value = "CLSJ")
    @AutoMapping(target = "establishmentDate", qualifiedByName = "strToDate")
    private String clsj;

    @TableField(value = "JYFW")
    @AutoMapping(target = "businessScope")
    private String jyfw;

    @TableField(value = "ZCD")
    @AutoMapping(target = "registrationAddress")
    private String zcd;

    @TableField(value = "JYD")
    @AutoMapping(target = "businessAddress")
    private String jyd;

    @TableField(value = "QYLX")
    private String qylx;

    @TableField(value = "QYLXMC")
    @AutoMapping(target = "enterpriseType")
    private String qylxmc;

    @TableField(value = "ZCZB")
    @AutoMapping(target = "registeredCapital",qualifiedByName = "strToBigDecimal")
    private String zczb;

    @TableField(value = "GRCZE")
    @AutoMapping(target = "personalContribution",qualifiedByName = "strToBigDecimal")
    private String grcze;

    @TableField(value = "GRCZBL")
    @AutoMapping(target = "personalContributionRatio",qualifiedByName = "strToBigDecimal")
    private String grczbl;

    @TableField(value = "QYZT")
    private String qyzt;

    @TableField(value = "QYZTMC")
    @AutoMapping(target = "enterpriseStatus")
    private String qyztmc;

    @TableField(value = "BZ")
    private String bz;

    @TableField(value = "SFGD")
    @AutoMapping(target = "isShareholder")
    private String sfgd;

    @TableField(value = "SFGDMC")
    private String sfgdmc;

    @TableField(value = "SFDRGJZW")
    @AutoMapping(target = "isSeniorPosition")
    private String sfdrgjzw;

    @TableField(value = "SFDRGJZWMC")
    private String sfdrgjzwmc;

    @TableField(value = "SFFSJJGX")
    private String sffsjjgx;

    @TableField(value = "SFFSJJGXMC")
    private String sffsjjgxmc;

    @TableField(value = "TZSJ")
    @AutoMapping(target = "investmentDate",qualifiedByName = "strToDate")
    private String tzsj;

    @TableField(value = "GJZWMC")
    @AutoMapping(target = "seniorPositionName")
    private String gjzwmc;

    @TableField(value = "GJZWSJ")
    @AutoMapping(target = "seniorPositionDate",qualifiedByName = "strToDate")
    private String gjzwsj;
}