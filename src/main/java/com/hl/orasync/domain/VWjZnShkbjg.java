package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import com.hl.archive.domain.entity.PoliceFamilyPaidInstitutions;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_ZN_SHKBJG")
@AutoMapper(target = PoliceFamilyPaidInstitutions.class, uses = {ConversionUtils.class})
public class VWjZnShkbjg {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_FR")
    @AutoMapping(target = "name")
    private String xmFr;

    @TableField(value = "ZCH")
    @AutoMapping(target = "socialCreditCode")
    private String zch;

    @TableField(value = "QYMC")
    @AutoMapping(target = "institutionName")
    private String qymc;

    @TableField(value = "CLSJ")
    @AutoMapping(target = "establishmentDate",qualifiedByName = "strToDate")
    private String clsj;

    @TableField(value = "JYFW")
    @AutoMapping(target = "businessScope")
    private String jyfw;

    @TableField(value = "ZCD")
    @AutoMapping(target = "registrationAddress")
    private String zcd;

    @TableField(value = "JYD")
    @AutoMapping(target = "businessAddress")
    private String jyd;

    @TableField(value = "QYLX")
    private String qylx;

    @TableField(value = "QYLXMC")
    @AutoMapping(target = "institutionType")
    private String qylxmc;

    @TableField(value = "ZCZB")
    @AutoMapping(target = "registeredCapital",qualifiedByName = "strToBigDecimal")
    private String zczb;

    @TableField(value = "GRCZE")
    private String grcze;

    @TableField(value = "GRCZBL")
    private String grczbl;

    @TableField(value = "QYZT")
    private String qyzt;

    @TableField(value = "QYZTMC")
    private String qyztmc;

    @TableField(value = "BZ")
    private String bz;

    @TableField(value = "SFGD")
    private String sfgd;

    @TableField(value = "SFGDMC")
    private String sfgdmc;

    @TableField(value = "SFDRGJZW")
    private String sfdrgjzw;

    @TableField(value = "SFDRGJZWMC")
    private String sfdrgjzwmc;

    @TableField(value = "SFFSJJGX")
    private String sffsjjgx;

    @TableField(value = "SFFSJJGXMC")
    private String sffsjjgxmc;

    @TableField(value = "TZSJ")
    private String tzsj;

    @TableField(value = "GJZWMC")
    private String gjzwmc;

    @TableField(value = "GJZWSJ")
    private String gjzwsj;

    @TableField(value = "ZYZGMC")
    private String zyzgmc;

    @TableField(value = "ZYZH")
    private String zyzh;

    @TableField(value = "XG_FLAG")
    private BigDecimal xgFlag;
}