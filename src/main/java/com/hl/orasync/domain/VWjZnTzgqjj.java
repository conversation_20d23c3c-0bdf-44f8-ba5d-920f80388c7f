package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyPrivateEquityFund;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_ZN_TZGQJJ")
@AutoMapper(target = PoliceFamilyPrivateEquityFund.class,uses = {ConversionUtils.class})
public class VWjZnTzgqjj {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_FR")
    @AutoMapping(target = "name")
    private String xmFr;

    @TableField(value = "ZJE")
    @AutoMapping(target = "totalPaidAmount")
    private String zje;

    @TableField(value = "MC")
    @AutoMapping(target = "fundNameCode")
    private String mc;

    @TableField(value = "GRJE")
    @AutoMapping(target = "personalPaidAmount",qualifiedByName = "strToBigDecimal")
    private String grje;

    @TableField(value = "JJTX")
    @AutoMapping(target = "fundInvestmentDirection")
    private String jjtx;

    @TableField(value = "QSRQ")
    @AutoMapping(target = "contractSigningDate",qualifiedByName = "strToDate")
    private String qsrq;

    @TableField(value = "JZRQ")
    @AutoMapping(target = "contractExpiryDate",qualifiedByName = "strToDate")
    private String jzrq;

    @TableField(value = "JYFW")
    private String jyfw;

    @TableField(value = "MC2")
    private String mc2;

    @TableField(value = "RJJE")
    @AutoMapping(target = "subscriptionAmount",qualifiedByName = "strToBigDecimal")
    private String rjje;

    @TableField(value = "RJBL")
    private String rjbl;

    @TableField(value = "RJSJ")
    private String rjsj;

    @TableField(value = "SFKZR")
    private String sfkzr;

    @TableField(value = "SFKZRMC")
    private String sfkzrmc;

    @TableField(value = "SFGD")
    private String sfgd;

    @TableField(value = "SFGDMC")
    private String sfgdmc;

    @TableField(value = "SFDRGJZW")
    private String sfdrgjzw;

    @TableField(value = "SFDRGJZWMC")
    private String sfdrgjzwmc;

    @TableField(value = "SFFSJJGX")
    private String sffsjjgx;

    @TableField(value = "SFFSJJGXMC")
    private String sffsjjgxmc;

    @TableField(value = "TZSJ")
    private String tzsj;

    @TableField(value = "GJZWMC")
    private String gjzwmc;

    @TableField(value = "GJZWSJ")
    private String gjzwsj;

    @TableField(value = "BZ")
    private String bz;
}