package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyCriminalLiability;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_ZN_XSZJ")
@AutoMapper(target = PoliceFamilyCriminalLiability.class, uses = {ConversionUtils.class})
public class VWjZnXszj {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_DSR")
    @AutoMapping(target = "name")
    private String xmDsr;

    @TableField(value = "BZJSJ")
    @AutoMapping(target = "prosecutionDate", qualifiedByName = "strToDate")
    private String bzjsj;

    @TableField(value = "BZJYY")
    @AutoMapping(target = "prosecutionReason")
    private String bzjyy;

    @TableField(value = "CLJG")
    @AutoMapping(target = "handlingResult")
    private String cljg;

    @TableField(value = "CLJD")
    @AutoMapping(target = "handlingStage")
    private String cljd;
}