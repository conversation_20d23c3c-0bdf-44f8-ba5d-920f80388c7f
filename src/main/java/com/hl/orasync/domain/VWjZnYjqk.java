package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceFamilyOverseasMigration;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_ZN_YJQK")
@AutoMapper(target = PoliceFamilyOverseasMigration.class, uses = {ConversionUtils.class})
public class VWjZnYjqk {
    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    @AutoMapping(target = "idCard")
    private String gmsfhm;

    @TableField(value = "ND")
    private String nd;

    @TableField(value = "XM_POZN")
    @AutoMapping(target = "familyMemberName")
    private String xmPozn;

    @TableField(value = "YJGJ")
    @AutoMapping(target = "migrationCountry")
    private String yjgj;

    @TableField(value = "XJZCS")
    @AutoMapping(target = "currentCity")
    private String xjzcs;

    @TableField(value = "YJZJHM")
    @AutoMapping(target = "migrationDocumentNumber")
    private String yjzjhm;

    @TableField(value = "YJLX")
    @AutoMapping(target = "migrationCategory")
    private String yjlx;

    @TableField(value = "BZ")
    @AutoMapping(target = "remarks")
    private String bz;

    @TableField(value = "YJSJ")
    @AutoMapping(target = "basisDate",qualifiedByName = "strToDate")
    private String yjsj;
}