package com.hl.orasync.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.service.PoliceBasicInfoService;
import com.hl.common.config.datasource.DataSource;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.convert.PoliceBasicInfoConvert;
import com.hl.orasync.domain.VWjRyjbxx;
import com.hl.orasync.mapper.VWjRyjbxxMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
@DataSource(DataSourceType.DATASOURCE0)
public class VWjRyjbxxService extends ServiceImpl<VWjRyjbxxMapper, VWjRyjbxx> {

    private final PoliceBasicInfoService policeBasicInfoService;

//    @EventListener(ApplicationReadyEvent.class)
    @DataSource(value = DataSourceType.DATASOURCE0)
    public void refreshJbxx(){
        List<VWjRyjbxx> list = list();
        for (VWjRyjbxx vWjRyjbxx : list) {
            PoliceBasicInfo policeBasicInfo = PoliceBasicInfoConvert.INSTANCE.convertPoliceBasicInfo(vWjRyjbxx);
            savePoliceBasicInfo(policeBasicInfo);
        }
    }


    public void savePoliceBasicInfo(PoliceBasicInfo policeBasicInfo){
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.MASTER));
        policeBasicInfoService.save(policeBasicInfo);
        DynamicDataSourceContextHolder.clearDataSourceType();
    }
}
