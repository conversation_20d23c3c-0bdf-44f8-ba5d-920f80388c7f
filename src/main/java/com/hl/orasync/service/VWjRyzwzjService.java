package com.hl.orasync.service;

import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.common.config.datasource.DataSource;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.orasync.sync.DataSyncService;
import com.hl.orasync.sync.SyncConfig;
import com.hl.orasync.sync.SyncResult;
import com.hl.orasync.sync.impl.PolicePositionRankSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.orasync.mapper.VWjRyzwzjMapper;
import com.hl.orasync.domain.VWjRyzwzj;

import javax.annotation.Resource;

@Service
@Slf4j
@RequiredArgsConstructor
public class VWjRyzwzjService extends ServiceImpl<VWjRyzwzjMapper, VWjRyzwzj> {



    private final Converter converter;


    @DataSource(value = DataSourceType.DATASOURCE0)
//    @EventListener(ApplicationReadyEvent.class)
    public void syncZwzj() {
        List<VWjRyzwzj> list = list();
        for (VWjRyzwzj vWjRyzwzj : list) {
            PolicePositionRank convert = converter.convert(vWjRyzwzj, PolicePositionRank.class);
            log.info(convert.toString());
        }
    }

//    @EventListener(ApplicationReadyEvent.class)
//    public void syncPolicePositionRank() {
//        log.info("开始执行用户数据同步任务");
//
//        SyncConfig config = SyncConfig.builder()
//                .batchSize(1000)
//                .pageSize(5000)
//                .enableParallelProcessing(true)
//                .threadPoolSize(4)
//                .build();
//
//        SyncResult result = dataSyncService.syncData(policePositionRankSyncProcessor, config);
//
//        if (result.isSuccess()) {
//            log.info("用户数据同步成功: 新增={}, 更新={}, 删除={}",
//                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
//        } else {
//            log.error("用户数据同步失败: {}", result.getErrorMessage());
//        }
//    }


}
