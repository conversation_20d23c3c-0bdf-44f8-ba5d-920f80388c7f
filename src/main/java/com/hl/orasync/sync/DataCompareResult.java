package com.hl.orasync.sync;

import lombok.Builder;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
@Builder
public class DataCompareResult<T> {
    private List<T> toInsert;
    private List<T> toUpdate;
    private List<String> toDelete;

    public boolean hasChanges() {
        return !CollectionUtils.isEmpty(toInsert) ||
                !CollectionUtils.isEmpty(toUpdate) ||
                !CollectionUtils.isEmpty(toDelete);
    }
}