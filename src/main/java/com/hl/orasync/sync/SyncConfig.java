package com.hl.orasync.sync;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SyncConfig {
    @Builder.Default
    private int batchSize = 200000;

    @Builder.Default
    private int pageSize = 5000;

    @Builder.Default
    private boolean enableParallelProcessing = true;

    @Builder.Default
    private int threadPoolSize = 4;

    @Builder.Default
    private boolean enableRedisCache = true;

    @Builder.Default
    private int cacheExpireMinutes = 30;
}