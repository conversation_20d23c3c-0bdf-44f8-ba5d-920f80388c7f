package com.hl.orasync.sync;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SyncResult {
    private boolean success;
    private String errorMessage;
    private int insertCount;
    private int updateCount;
    private int deleteCount;
    private long executionTime;

    public String getSummary() {
        if (success) {
            return String.format("同步成功 - 新增: %d, 更新: %d, 删除: %d, 耗时: %dms",
                    insertCount, updateCount, deleteCount, executionTime);
        } else {
            return String.format("同步失败 - %s, 耗时: %dms", errorMessage, executionTime);
        }
    }
}