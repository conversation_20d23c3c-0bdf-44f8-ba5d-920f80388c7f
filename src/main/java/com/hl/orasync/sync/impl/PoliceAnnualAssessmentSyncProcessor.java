package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceAnnualAssessment;
import com.hl.archive.service.PoliceAnnualAssessmentService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjyxl;
import com.hl.orasync.domain.VWjRyndkh;
import com.hl.orasync.service.VWjRyndkhService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceAnnualAssessmentSyncProcessor implements DataSyncProcessor<VWjRyndkh, PoliceAnnualAssessment> {

    private final Converter converter;

    private final VWjRyndkhService vwjRyndkhService;

    private final PoliceAnnualAssessmentService policeAnnualAssessmentService;

    @Override
    public List<VWjRyndkh> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));

        Page<VWjRyndkh> page = vwjRyndkhService.page(Page.of(offset, limit), Wrappers.<VWjRyndkh>lambdaQuery()
                .orderByDesc(VWjRyndkh::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();

        return page.getRecords();
    }

    @Override
    public List<PoliceAnnualAssessment> getTargetData(int offset, int limit) {
        Page<PoliceAnnualAssessment> page = policeAnnualAssessmentService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceAnnualAssessment convert(VWjRyndkh source) {
        return converter.convert(source, PoliceAnnualAssessment.class);
    }

    @Override
    public Function<PoliceAnnualAssessment, String> getBusinessKeyGenerator() {
        return policeAnnualAssessment ->
                policeAnnualAssessment.getIdCard() + '_' + policeAnnualAssessment.getAssessmentYear()
                        + "_" + policeAnnualAssessment.getAssessmentCategory()
                        + "_" + policeAnnualAssessment.getAssessmentResult();
    }

    @Override
    public void batchInsert(List<PoliceAnnualAssessment> records) {
        policeAnnualAssessmentService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceAnnualAssessment> records) {
        policeAnnualAssessmentService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeAnnualAssessmentService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyndkhService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return  policeAnnualAssessmentService.count();
    }
}
