package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.service.PoliceBasicInfoService;
import com.hl.orasync.convert.PoliceBasicInfoConvert;
import com.hl.orasync.domain.VWjRyjbxx;
import com.hl.orasync.service.VWjRyjbxxService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
public class PoliceBasicInfoSyncProcessor extends AbstractDataSyncProcessor<VWjRyjbxx, PoliceBasicInfo> {

    private final PoliceBasicInfoService policeBasicInfoService;

    private final VWjRyjbxxService vwjRyjbxxService;

    public PoliceBasicInfoSyncProcessor(Converter converter,
                                      PoliceBasicInfoService policeBasicInfoService,
                                      VWjRyjbxxService vwjRyjbxxService) {
        super(converter);
        this.policeBasicInfoService = policeBasicInfoService;
        this.vwjRyjbxxService = vwjRyjbxxService;
    }


    @Override
    protected IService<VWjRyjbxx> getSourceService() {
        return vwjRyjbxxService;
    }

    @Override
    protected IService<PoliceBasicInfo> getTargetService() {
        return policeBasicInfoService;
    }

    @Override
    protected Class<PoliceBasicInfo> getTargetClass() {
        return PoliceBasicInfo.class;
    }

    @Override
    protected LambdaQueryWrapper<VWjRyjbxx> buildSourceQueryWrapper() {
        return Wrappers.<VWjRyjbxx>lambdaQuery()
                .orderByDesc(VWjRyjbxx::getGmsfhm);
    }

    @Override
    public PoliceBasicInfo convert(VWjRyjbxx source) {
        // 使用自定义转换器而不是通用转换器
        return PoliceBasicInfoConvert.INSTANCE.convertPoliceBasicInfo(source);
    }

    @Override
    public Function<PoliceBasicInfo, String> getBusinessKeyGenerator() {
        return PoliceBasicInfo::getIdCard;
    }
}
