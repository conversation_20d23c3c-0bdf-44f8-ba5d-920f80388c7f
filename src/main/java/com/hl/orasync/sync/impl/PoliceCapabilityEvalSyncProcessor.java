package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import com.hl.archive.service.PoliceCapabilityEvalService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjNlcp;
import com.hl.orasync.service.VWjNlcpService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceCapabilityEvalSyncProcessor implements DataSyncProcessor<VWjNlcp, PoliceCapabilityEval> {

    private final VWjNlcpService vwjNlcpService;

    private final PoliceCapabilityEvalService policeCapabilityEvalService;

    private final Converter converter;


    @Override
    public List<VWjNlcp> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjNlcp> page = vwjNlcpService.page(new Page<>(offset, limit), Wrappers.<VWjNlcp>lambdaQuery()
                .orderByDesc(VWjNlcp::getLcid));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceCapabilityEval> getTargetData(int offset, int limit) {
        Page<PoliceCapabilityEval> page = policeCapabilityEvalService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceCapabilityEval convert(VWjNlcp source) {
        return converter.convert(source, PoliceCapabilityEval.class);
    }

    @Override
    public Function<PoliceCapabilityEval, String> getBusinessKeyGenerator() {
        return PoliceCapabilityEval::getLcid;
    }

    @Override
    public void batchInsert(List<PoliceCapabilityEval> records) {
        policeCapabilityEvalService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceCapabilityEval> records) {
        policeCapabilityEvalService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeCapabilityEvalService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjNlcpService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeCapabilityEvalService.count();
    }
}
