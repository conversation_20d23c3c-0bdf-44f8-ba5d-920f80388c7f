package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceChildrenForeignMarriage;
import com.hl.archive.service.PoliceChildrenForeignMarriageService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjZnGwth;
import com.hl.orasync.service.VWjZnGwthService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceChildrenForeignMarriageSyncProcessor implements DataSyncProcessor<VWjZnGwth, PoliceChildrenForeignMarriage> {

    private final VWjZnGwthService vWjZnGwthService;

    private final PoliceChildrenForeignMarriageService policeChildrenForeignMarriageService;

    private final Converter converter;

    @Override
    public List<VWjZnGwth> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjZnGwth> page = vWjZnGwthService.page(Page.of(offset, limit), Wrappers.<VWjZnGwth>lambdaQuery()
                .orderByDesc(VWjZnGwth::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceChildrenForeignMarriage> getTargetData(int offset, int limit) {
        Page<PoliceChildrenForeignMarriage> page = policeChildrenForeignMarriageService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceChildrenForeignMarriage convert(VWjZnGwth source) {
        return converter.convert(source, PoliceChildrenForeignMarriage.class);
    }

    @Override
    public Function<PoliceChildrenForeignMarriage, String> getBusinessKeyGenerator() {
        return policeChildrenForeignMarriage ->
                policeChildrenForeignMarriage.getIdCard() + "_" +
                        policeChildrenForeignMarriage.getPosition() + "_" +
                        policeChildrenForeignMarriage.getChildName() + "_" +
                        policeChildrenForeignMarriage.getSpouseCountry() + "_" +
                        policeChildrenForeignMarriage.getSpouseName();
    }

    @Override
    public void batchInsert(List<PoliceChildrenForeignMarriage> records) {
        policeChildrenForeignMarriageService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceChildrenForeignMarriage> records) {
        policeChildrenForeignMarriageService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeChildrenForeignMarriageService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjZnGwthService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeChildrenForeignMarriageService.count();
    }
}
