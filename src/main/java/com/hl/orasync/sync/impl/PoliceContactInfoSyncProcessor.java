package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.entity.PoliceContactInfo;
import com.hl.archive.service.PoliceContactInfoService;
import com.hl.orasync.domain.VWjRyjtzz;
import com.hl.orasync.service.VWjRyjtzzService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;

import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
public class PoliceContactInfoSyncProcessor extends AbstractDataSyncProcessor<VWjRyjtzz, PoliceContactInfo> {

    private final VWjRyjtzzService vwjRyjtzzService;

    private final PoliceContactInfoService policeContactInfoService;

    public PoliceContactInfoSyncProcessor(Converter converter,
                                        VWjRyjtzzService vwjRyjtzzService,
                                        PoliceContactInfoService policeContactInfoService) {
        super(converter);
        this.vwjRyjtzzService = vwjRyjtzzService;
        this.policeContactInfoService = policeContactInfoService;
    }

    @Override
    protected IService<VWjRyjtzz> getSourceService() {
        return vwjRyjtzzService;
    }

    @Override
    protected IService<PoliceContactInfo> getTargetService() {
        return policeContactInfoService;
    }

    @Override
    protected Class<PoliceContactInfo> getTargetClass() {
        return PoliceContactInfo.class;
    }

    @Override
    protected LambdaQueryWrapper<VWjRyjtzz> buildSourceQueryWrapper() {
        return Wrappers.<VWjRyjtzz>lambdaQuery()
                .orderByDesc(VWjRyjtzz::getGmsfhm);
    }

    @Override
    public Function<PoliceContactInfo, String> getBusinessKeyGenerator() {
        return policeContactInfo -> policeContactInfo.getIdCard() + "_" + policeContactInfo.getMobilePhone() + "_" + policeContactInfo.getHomeAddress();
    }
}
