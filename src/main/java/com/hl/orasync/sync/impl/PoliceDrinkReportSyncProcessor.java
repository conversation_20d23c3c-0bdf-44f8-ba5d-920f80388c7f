package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.entity.PoliceDrinkReport;
import com.hl.archive.service.PoliceDrinkReportService;
import com.hl.orasync.domain.VWjYjbb;
import com.hl.orasync.service.VWjYjbbService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
public class PoliceDrinkReportSyncProcessor extends AbstractDataSyncProcessor <VWjYjbb, PoliceDrinkReport>{


    private final VWjYjbbService vwjYjbbService;

    private final PoliceDrinkReportService policeDrinkReportService;

    public PoliceDrinkReportSyncProcessor(Converter converter,
                                          VWjYjbbService vwjYjbbService,
                                          PoliceDrinkReportService policeDrinkReportService) {
        super(converter);
        this.vwjYjbbService = vwjYjbbService;
        this.policeDrinkReportService = policeDrinkReportService;
    }

    @Override
    protected IService<VWjYjbb> getSourceService() {
        return vwjYjbbService;
    }

    @Override
    protected IService<PoliceDrinkReport> getTargetService() {
        return policeDrinkReportService;
    }

    @Override
    protected Class<PoliceDrinkReport> getTargetClass() {
        return PoliceDrinkReport.class;
    }

    @Override
    public Function<PoliceDrinkReport, String> getBusinessKeyGenerator() {
        return PoliceDrinkReport::getXxzj;
    }

    @Override
    protected LambdaQueryWrapper<PoliceDrinkReport> buildTargetQueryWrapper() {
        return Wrappers.<PoliceDrinkReport>lambdaQuery()
                .isNotNull(PoliceDrinkReport::getXxzj);
    }
}
