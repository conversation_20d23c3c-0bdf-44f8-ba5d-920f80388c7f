package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceEducation;
import com.hl.archive.service.PoliceEducationService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyxlxw;
import com.hl.orasync.service.VWjRyxlxwService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceEducationSyncProcessor implements DataSyncProcessor<VWjRyxlxw, PoliceEducation> {

    private final Converter converter;

    private final VWjRyxlxwService vwjRyxlxwService;

    private final PoliceEducationService policeEducationService;

    @Override
    public List<VWjRyxlxw> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyxlxw> page = vwjRyxlxwService.page(Page.of(offset, limit), Wrappers.<VWjRyxlxw>lambdaQuery()
                .orderByDesc(VWjRyxlxw::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceEducation> getTargetData(int offset, int limit) {
        Page<PoliceEducation> page = policeEducationService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceEducation convert(VWjRyxlxw source) {
        return converter.convert(source, PoliceEducation.class);
    }

    @Override
    public Function<PoliceEducation, String> getBusinessKeyGenerator() {
        return policeEducation ->
                policeEducation.getIdCard() + "_" + policeEducation.getSchoolName() + "_" + policeEducation.getEnrollmentDate()+"_"+policeEducation.getEducationLevel();
    }

    @Override
    public void batchInsert(List<PoliceEducation> records) {
        policeEducationService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceEducation> records) {
        policeEducationService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeEducationService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyxlxwService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeEducationService.count();
    }
}
