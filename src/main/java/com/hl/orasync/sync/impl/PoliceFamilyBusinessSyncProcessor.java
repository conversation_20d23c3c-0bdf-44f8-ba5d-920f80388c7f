package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyBusiness;
import com.hl.archive.service.PoliceFamilyBusinessService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjZnJsbqy;
import com.hl.orasync.service.VWjZnJsbqyService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyBusinessSyncProcessor implements DataSyncProcessor<VWjZnJsbqy, PoliceFamilyBusiness> {

    private final VWjZnJsbqyService vWjZnJsbqyService;

    private final PoliceFamilyBusinessService policeFamilyBusinessService;

    private final Converter converter;

    @Override
    public List<VWjZnJsbqy> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjZnJsbqy> page = vWjZnJsbqyService.page(Page.of(offset, limit), Wrappers.<VWjZnJsbqy>lambdaQuery()
                .orderByDesc(VWjZnJsbqy::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyBusiness> getTargetData(int offset, int limit) {
        Page<PoliceFamilyBusiness> page = policeFamilyBusinessService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyBusiness convert(VWjZnJsbqy source) {
        return converter.convert(source, PoliceFamilyBusiness.class);
    }

    @Override
    public Function<PoliceFamilyBusiness, String> getBusinessKeyGenerator() {
        return policeFamilyBusiness ->
                policeFamilyBusiness.getIdCard() + "_" +
                        policeFamilyBusiness.getBusinessScope() + "_" +
                        policeFamilyBusiness.getBusinessAddress() + "_" +
                        policeFamilyBusiness.getEnterpriseType() + "_" +
                        policeFamilyBusiness.getPersonalContribution() + "_" +
                        policeFamilyBusiness.getPersonalContributionRatio();
    }

    @Override
    public void batchInsert(List<PoliceFamilyBusiness> records) {
        policeFamilyBusinessService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyBusiness> records) {
        policeFamilyBusinessService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyBusinessService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjZnJsbqyService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyBusinessService.count();
    }
}
