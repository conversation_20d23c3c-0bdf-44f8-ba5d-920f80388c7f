package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyCriminalLiability;
import com.hl.archive.service.PoliceFamilyCriminalLiabilityService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjZnXszj;
import com.hl.orasync.service.VWjZnXszjService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyCriminalLiabilitySyncProcessor implements DataSyncProcessor<VWjZnXszj, PoliceFamilyCriminalLiability> {

    private final VWjZnXszjService vWjZnXszjService;

    private final PoliceFamilyCriminalLiabilityService policeFamilyCriminalLiabilityService;

    private final Converter converter;

    @Override
    public List<VWjZnXszj> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjZnXszj> page = vWjZnXszjService.page(Page.of(offset, limit), Wrappers.<VWjZnXszj>lambdaQuery()
                .orderByDesc(VWjZnXszj::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyCriminalLiability> getTargetData(int offset, int limit) {
        Page<PoliceFamilyCriminalLiability> page = policeFamilyCriminalLiabilityService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyCriminalLiability convert(VWjZnXszj source) {
        return converter.convert(source, PoliceFamilyCriminalLiability.class);
    }

    @Override
    public Function<PoliceFamilyCriminalLiability, String> getBusinessKeyGenerator() {
        return policeFamilyCriminalLiability ->
                policeFamilyCriminalLiability.getIdCard() + "_" +
                        policeFamilyCriminalLiability.getName() + "_" +
                        policeFamilyCriminalLiability.getHandlingStage() + "_" +
                        policeFamilyCriminalLiability.getProsecutionReason() + "_" +
                        policeFamilyCriminalLiability.getProsecutionDate();
    }

    @Override
    public void batchInsert(List<PoliceFamilyCriminalLiability> records) {
        policeFamilyCriminalLiabilityService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyCriminalLiability> records) {
        policeFamilyCriminalLiabilityService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyCriminalLiabilityService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjZnXszjService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyCriminalLiabilityService.count();
    }
}
