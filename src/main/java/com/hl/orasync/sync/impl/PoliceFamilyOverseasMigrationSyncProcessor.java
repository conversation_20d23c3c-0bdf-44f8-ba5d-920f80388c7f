package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyOverseasMigration;
import com.hl.archive.service.PoliceFamilyOverseasMigrationService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjZnYjqk;
import com.hl.orasync.service.VWjZnYjqkService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyOverseasMigrationSyncProcessor implements DataSyncProcessor<VWjZnYjqk, PoliceFamilyOverseasMigration> {

    private final VWjZnYjqkService vWjZnYjqkService;

    private final PoliceFamilyOverseasMigrationService policeFamilyOverseasMigrationService;

    private final Converter converter;

    @Override
    public List<VWjZnYjqk> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjZnYjqk> page = vWjZnYjqkService.page(Page.of(offset, limit), Wrappers.<VWjZnYjqk>lambdaQuery()
                .orderByDesc(VWjZnYjqk::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyOverseasMigration> getTargetData(int offset, int limit) {
        Page<PoliceFamilyOverseasMigration> page = policeFamilyOverseasMigrationService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyOverseasMigration convert(VWjZnYjqk source) {
        return converter.convert(source, PoliceFamilyOverseasMigration.class);
    }

    @Override
    public Function<PoliceFamilyOverseasMigration, String> getBusinessKeyGenerator() {
        return policeFamilyOverseasMigration ->
                policeFamilyOverseasMigration.getIdCard() + "_" +
                        policeFamilyOverseasMigration.getMigrationCategory() + "_" +
                        policeFamilyOverseasMigration.getMigrationCountry() +
                        policeFamilyOverseasMigration.getFamilyMemberName() +
                        policeFamilyOverseasMigration.getMigrationDocumentNumber();
    }

    @Override
    public void batchInsert(List<PoliceFamilyOverseasMigration> records) {
        policeFamilyOverseasMigrationService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyOverseasMigration> records) {
        policeFamilyOverseasMigrationService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyOverseasMigrationService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjZnYjqkService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyOverseasMigrationService.count();
    }
}
