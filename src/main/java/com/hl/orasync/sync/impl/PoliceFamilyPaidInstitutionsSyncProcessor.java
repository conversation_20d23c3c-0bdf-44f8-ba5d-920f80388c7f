package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyPaidInstitutions;
import com.hl.archive.service.PoliceFamilyPaidInstitutionsService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjZnShkbjg;
import com.hl.orasync.service.VWjZnShkbjgService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyPaidInstitutionsSyncProcessor implements DataSyncProcessor<VWjZnShkbjg, PoliceFamilyPaidInstitutions> {

    private final VWjZnShkbjgService vWjZnShkbjgService;

    private final PoliceFamilyPaidInstitutionsService policeFamilyPaidInstitutionsService;

    private final Converter converter;

    @Override
    public List<VWjZnShkbjg> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjZnShkbjg> page = vWjZnShkbjgService.page(Page.of(offset, limit), Wrappers.<VWjZnShkbjg>lambdaQuery()
                .orderByDesc(VWjZnShkbjg::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyPaidInstitutions> getTargetData(int offset, int limit) {
        Page<PoliceFamilyPaidInstitutions> page = policeFamilyPaidInstitutionsService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyPaidInstitutions convert(VWjZnShkbjg source) {
        return converter.convert(source, PoliceFamilyPaidInstitutions.class);
    }

    @Override
    public Function<PoliceFamilyPaidInstitutions, String> getBusinessKeyGenerator() {
        return policeFamilyPaidInstitutions ->
                policeFamilyPaidInstitutions.getIdCard() + "_" +
                        policeFamilyPaidInstitutions.getBusinessScope() + "_" +
                        policeFamilyPaidInstitutions.getInstitutionName() + "_" +
                        policeFamilyPaidInstitutions.getPersonalContributionRatio() + "_" +
                        policeFamilyPaidInstitutions.getBusinessAddress();
    }

    @Override
    public void batchInsert(List<PoliceFamilyPaidInstitutions> records) {
        policeFamilyPaidInstitutionsService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyPaidInstitutions> records) {
        policeFamilyPaidInstitutionsService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyPaidInstitutionsService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjZnShkbjgService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyPaidInstitutionsService.count();
    }
}
