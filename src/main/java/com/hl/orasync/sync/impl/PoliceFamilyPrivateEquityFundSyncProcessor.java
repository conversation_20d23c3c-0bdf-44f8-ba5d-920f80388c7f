package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyPrivateEquityFund;
import com.hl.archive.service.PoliceFamilyPrivateEquityFundService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjZnTzgqjj;
import com.hl.orasync.service.VWjZnTzgqjjService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyPrivateEquityFundSyncProcessor implements DataSyncProcessor<VWjZnTzgqjj, PoliceFamilyPrivateEquityFund> {

    private final VWjZnTzgqjjService vWjZnTzgqjjService;

    private final PoliceFamilyPrivateEquityFundService policeFamilyPrivateEquityFundService;

    private final Converter converter;

    @Override
    public List<VWjZnTzgqjj> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjZnTzgqjj> page = vWjZnTzgqjjService.page(Page.of(offset, limit), Wrappers.<VWjZnTzgqjj>lambdaQuery()
                .orderByDesc(VWjZnTzgqjj::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyPrivateEquityFund> getTargetData(int offset, int limit) {
        Page<PoliceFamilyPrivateEquityFund> page = policeFamilyPrivateEquityFundService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyPrivateEquityFund convert(VWjZnTzgqjj source) {
        return converter.convert(source, PoliceFamilyPrivateEquityFund.class);
    }

    @Override
    public Function<PoliceFamilyPrivateEquityFund, String> getBusinessKeyGenerator() {
        return policeFamilyPrivateEquityFund ->
                policeFamilyPrivateEquityFund.getIdCard() + "_" +
                        policeFamilyPrivateEquityFund.getFundNameCode() + "_" +
                        policeFamilyPrivateEquityFund.getFundManagerNameCode();
    }

    @Override
    public void batchInsert(List<PoliceFamilyPrivateEquityFund> records) {
        policeFamilyPrivateEquityFundService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyPrivateEquityFund> records) {
        policeFamilyPrivateEquityFundService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyPrivateEquityFundService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjZnTzgqjjService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyPrivateEquityFundService.count();
    }
}
