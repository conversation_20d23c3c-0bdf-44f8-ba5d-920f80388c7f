package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyRealEstate;
import com.hl.archive.service.PoliceFamilyRealEstateService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjQtFcqk;
import com.hl.orasync.service.VWjQtFcqkService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyRealEstateSyncProcessor implements DataSyncProcessor<VWjQtFcqk, PoliceFamilyRealEstate> {

    private final VWjQtFcqkService vWjQtFcqkService;

    private final PoliceFamilyRealEstateService policeFamilyRealEstateService;

    private final Converter converter;

    @Override
    public List<VWjQtFcqk> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjQtFcqk> page = vWjQtFcqkService.page(Page.of(offset, limit), Wrappers.<VWjQtFcqk>lambdaQuery()
                .orderByDesc(VWjQtFcqk::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyRealEstate> getTargetData(int offset, int limit) {
        Page<PoliceFamilyRealEstate> page = policeFamilyRealEstateService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyRealEstate convert(VWjQtFcqk source) {
        return converter.convert(source, PoliceFamilyRealEstate.class);
    }

    @Override
    public Function<PoliceFamilyRealEstate, String> getBusinessKeyGenerator() {
        return policeFamilyRealEstate ->
                policeFamilyRealEstate.getIdCard() + "_" +
                        policeFamilyRealEstate.getPropertyOwnerName() + "_" +
                        policeFamilyRealEstate.getPropertyAddress() + "_" +
                        policeFamilyRealEstate.getTransactionDate();
    }

    @Override
    public void batchInsert(List<PoliceFamilyRealEstate> records) {
        policeFamilyRealEstateService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyRealEstate> records) {
        policeFamilyRealEstateService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyRealEstateService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjQtFcqkService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyRealEstateService.count();
    }
}
