package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceFamilyVehicles;
import com.hl.archive.service.PoliceFamilyVehiclesService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjQtClxx;
import com.hl.orasync.service.VWjQtClxxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceFamilyVehiclesSyncProcessor implements DataSyncProcessor<VWjQtClxx, PoliceFamilyVehicles> {

    private final VWjQtClxxService vWjQtClxxService;

    private final PoliceFamilyVehiclesService policeFamilyVehiclesService;

    private final Converter converter;

    @Override
    public List<VWjQtClxx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjQtClxx> page = vWjQtClxxService.page(Page.of(offset, limit), Wrappers.<VWjQtClxx>lambdaQuery()
                .orderByDesc(VWjQtClxx::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceFamilyVehicles> getTargetData(int offset, int limit) {
        Page<PoliceFamilyVehicles> page = policeFamilyVehiclesService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceFamilyVehicles convert(VWjQtClxx source) {
        return converter.convert(source, PoliceFamilyVehicles.class);
    }

    @Override
    public Function<PoliceFamilyVehicles, String> getBusinessKeyGenerator() {
        return policeFamilyVehicles ->
                policeFamilyVehicles.getIdCard() + "_" +
                        policeFamilyVehicles.getOwnerName() + "_" +
                        policeFamilyVehicles.getLicensePlate() + "_" +
                        policeFamilyVehicles.getTransactionDate();
    }

    @Override
    public void batchInsert(List<PoliceFamilyVehicles> records) {
        policeFamilyVehiclesService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceFamilyVehicles> records) {
        policeFamilyVehiclesService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeFamilyVehiclesService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjQtClxxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeFamilyVehiclesService.count();
    }
}
