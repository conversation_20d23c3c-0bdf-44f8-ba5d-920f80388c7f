package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceHealthStatus;
import com.hl.archive.service.PoliceHealthStatusService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrJkzk;
import com.hl.orasync.service.VWjBrJkzkService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceHealthStatusSyncProcessor implements DataSyncProcessor<VWjBrJkzk, PoliceHealthStatus> {

    private final VWjBrJkzkService vWjBrJkzkService;

    private final PoliceHealthStatusService policeHealthStatusService;

    private final Converter converter;

    @Override
    public List<VWjBrJkzk> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrJkzk> page = vWjBrJkzkService.page(Page.of(offset, limit), Wrappers.<VWjBrJkzk>lambdaQuery()
                .orderByDesc(VWjBrJkzk::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceHealthStatus> getTargetData(int offset, int limit) {
        Page<PoliceHealthStatus> page = policeHealthStatusService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceHealthStatus convert(VWjBrJkzk source) {
        return converter.convert(source, PoliceHealthStatus.class);
    }

    @Override
    public Function<PoliceHealthStatus, String> getBusinessKeyGenerator() {
        return policeHealthStatus ->
                policeHealthStatus.getIdCard() + "_" +
                        policeHealthStatus.getIllnessName() + "_" +
                        policeHealthStatus.getDiagnosisInstitution() + "_" +
                        policeHealthStatus.getDiagnosisDate();
    }

    @Override
    public void batchInsert(List<PoliceHealthStatus> records) {
        policeHealthStatusService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceHealthStatus> records) {
        policeHealthStatusService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeHealthStatusService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrJkzkService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeHealthStatusService.count();
    }
}
