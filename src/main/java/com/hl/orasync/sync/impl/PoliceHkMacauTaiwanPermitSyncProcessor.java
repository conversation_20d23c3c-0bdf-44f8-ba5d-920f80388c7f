package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceHkMacauTaiwanPermit;
import com.hl.archive.service.PoliceHkMacauTaiwanPermitService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrGattxz;
import com.hl.orasync.service.VWjBrGattxzService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceHkMacauTaiwanPermitSyncProcessor implements DataSyncProcessor<VWjBrGattxz, PoliceHkMacauTaiwanPermit> {

    private final VWjBrGattxzService vWjBrGattxzService;

    private final PoliceHkMacauTaiwanPermitService policeHkMacauTaiwanPermitService;

    private final Converter converter;

    @Override
    public List<VWjBrGattxz> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrGattxz> page = vWjBrGattxzService.page(Page.of(offset, limit), Wrappers.<VWjBrGattxz>lambdaQuery()
                .orderByDesc(VWjBrGattxz::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceHkMacauTaiwanPermit> getTargetData(int offset, int limit) {
        Page<PoliceHkMacauTaiwanPermit> page = policeHkMacauTaiwanPermitService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceHkMacauTaiwanPermit convert(VWjBrGattxz source) {
        return converter.convert(source, PoliceHkMacauTaiwanPermit.class);
    }

    @Override
    public Function<PoliceHkMacauTaiwanPermit, String> getBusinessKeyGenerator() {
        return policeHkMacauTaiwanPermit ->
                policeHkMacauTaiwanPermit.getIdCard() + "_" +
                        policeHkMacauTaiwanPermit.getDocumentNumber() + "_" +
                        policeHkMacauTaiwanPermit.getIssueDate() + "_" +
                        policeHkMacauTaiwanPermit.getCustodyOrganization() + "_" +
                        policeHkMacauTaiwanPermit.getRemarks();
    }

    @Override
    public void batchInsert(List<PoliceHkMacauTaiwanPermit> records) {
        policeHkMacauTaiwanPermitService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceHkMacauTaiwanPermit> records) {
        policeHkMacauTaiwanPermitService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeHkMacauTaiwanPermitService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrGattxzService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeHkMacauTaiwanPermitService.count();
    }
}
