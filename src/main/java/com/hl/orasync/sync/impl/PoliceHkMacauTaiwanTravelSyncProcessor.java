package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceHkMacauTaiwanTravel;
import com.hl.archive.service.PoliceHkMacauTaiwanTravelService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrGatwlqk;
import com.hl.orasync.service.VWjBrGatwlqkService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceHkMacauTaiwanTravelSyncProcessor implements DataSyncProcessor<VWjBrGatwlqk, PoliceHkMacauTaiwanTravel> {

    private final VWjBrGatwlqkService vWjBrGatwlqkService;

    private final PoliceHkMacauTaiwanTravelService policeHkMacauTaiwanTravelService;

    private final Converter converter;

    @Override
    public List<VWjBrGatwlqk> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrGatwlqk> page = vWjBrGatwlqkService.page(Page.of(offset, limit), Wrappers.<VWjBrGatwlqk>lambdaQuery()
                .orderByDesc(VWjBrGatwlqk::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceHkMacauTaiwanTravel> getTargetData(int offset, int limit) {
        Page<PoliceHkMacauTaiwanTravel> page = policeHkMacauTaiwanTravelService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceHkMacauTaiwanTravel convert(VWjBrGatwlqk source) {
        return converter.convert(source, PoliceHkMacauTaiwanTravel.class);
    }

    @Override
    public Function<PoliceHkMacauTaiwanTravel, String> getBusinessKeyGenerator() {
        return policeHkMacauTaiwanTravel ->
                policeHkMacauTaiwanTravel.getIdCard() + "_" +
                        policeHkMacauTaiwanTravel.getDocumentNumber() + "_" +
                        policeHkMacauTaiwanTravel.getApprovalAuthority() + "_" +
                        policeHkMacauTaiwanTravel.getDestinationRegion() + "_" +
                        policeHkMacauTaiwanTravel.getStartDate() + "_" +
                        policeHkMacauTaiwanTravel.getEndDate();
    }

    @Override
    public void batchInsert(List<PoliceHkMacauTaiwanTravel> records) {
        policeHkMacauTaiwanTravelService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceHkMacauTaiwanTravel> records) {
        policeHkMacauTaiwanTravelService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeHkMacauTaiwanTravelService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrGatwlqkService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeHkMacauTaiwanTravelService.count();
    }
}
