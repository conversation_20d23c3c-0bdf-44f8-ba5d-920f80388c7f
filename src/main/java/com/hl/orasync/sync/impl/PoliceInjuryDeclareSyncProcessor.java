package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceInjuryDeclare;
import com.hl.archive.service.PoliceInjuryDeclareService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjMjqyxxsb;
import com.hl.orasync.service.VWjMjqyxxsbService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceInjuryDeclareSyncProcessor implements DataSyncProcessor<VWjMjqyxxsb, PoliceInjuryDeclare> {

    private final VWjMjqyxxsbService vWjMjqyxxsbService;

    private final PoliceInjuryDeclareService policeInjuryDeclareService;

    private final Converter converter;

    @Override
    public List<VWjMjqyxxsb> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjMjqyxxsb> page = vWjMjqyxxsbService.page(new Page<>(offset, limit), Wrappers.<VWjMjqyxxsb>lambdaQuery()
                .orderByDesc(VWjMjqyxxsb::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceInjuryDeclare> getTargetData(int offset, int limit) {
        Page<PoliceInjuryDeclare> page = policeInjuryDeclareService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceInjuryDeclare convert(VWjMjqyxxsb source) {
        return converter.convert(source, PoliceInjuryDeclare.class);
    }

    @Override
    public Function<PoliceInjuryDeclare, String> getBusinessKeyGenerator() {
        return policeInjuryDeclare -> policeInjuryDeclare.getIdCard() + "_" + policeInjuryDeclare.getDeclareType();
    }

    @Override
    public void batchInsert(List<PoliceInjuryDeclare> records) {
        policeInjuryDeclareService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceInjuryDeclare> records) {
        policeInjuryDeclareService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeInjuryDeclareService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjMjqyxxsbService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeInjuryDeclareService.count();
    }
}
