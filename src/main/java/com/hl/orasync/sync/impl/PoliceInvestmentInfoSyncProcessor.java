package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceInvestmentInfo;
import com.hl.archive.service.PoliceInvestmentInfoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrTzqk;
import com.hl.orasync.service.VWjBrTzqkService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceInvestmentInfoSyncProcessor implements DataSyncProcessor<VWjBrTzqk, PoliceInvestmentInfo> {

    private final VWjBrTzqkService vWjBrTzqkService;

    private final PoliceInvestmentInfoService policeInvestmentInfoService;

    private final Converter converter;

    @Override
    public List<VWjBrTzqk> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrTzqk> page = vWjBrTzqkService.page(Page.of(offset, limit), Wrappers.<VWjBrTzqk>lambdaQuery()
                .orderByDesc(VWjBrTzqk::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceInvestmentInfo> getTargetData(int offset, int limit) {
        Page<PoliceInvestmentInfo> page = policeInvestmentInfoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceInvestmentInfo convert(VWjBrTzqk source) {
        return converter.convert(source, PoliceInvestmentInfo.class);
    }

    @Override
    public Function<PoliceInvestmentInfo, String> getBusinessKeyGenerator() {
        return policeInvestmentInfo ->
                policeInvestmentInfo.getIdCard() + "_" +
                        policeInvestmentInfo.getInvestmentEntity() + "_" +
                        policeInvestmentInfo.getInvestmentSource() + "_" +
                        policeInvestmentInfo.getTransactionDate();
    }

    @Override
    public void batchInsert(List<PoliceInvestmentInfo> records) {
        policeInvestmentInfoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceInvestmentInfo> records) {
        policeInvestmentInfoService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeInvestmentInfoService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrTzqkService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeInvestmentInfoService.count();
    }
}
