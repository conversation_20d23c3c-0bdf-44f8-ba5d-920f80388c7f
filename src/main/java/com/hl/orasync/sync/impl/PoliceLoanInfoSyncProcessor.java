package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceLoanInfo;
import com.hl.archive.service.PoliceLoanInfoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrGrjd;
import com.hl.orasync.service.VWjBrGrjdService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceLoanInfoSyncProcessor implements DataSyncProcessor<VWjBrGrjd, PoliceLoanInfo> {

    private final VWjBrGrjdService vWjBrGrjdService;

    private final PoliceLoanInfoService policeLoanInfoService;

    private final Converter converter;

    @Override
    public List<VWjBrGrjd> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrGrjd> page = vWjBrGrjdService.page(Page.of(offset, limit), Wrappers.<VWjBrGrjd>lambdaQuery()
                .orderByDesc(VWjBrGrjd::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceLoanInfo> getTargetData(int offset, int limit) {
        Page<PoliceLoanInfo> page = policeLoanInfoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceLoanInfo convert(VWjBrGrjd source) {
        return converter.convert(source, PoliceLoanInfo.class);
    }

    @Override
    public Function<PoliceLoanInfo, String> getBusinessKeyGenerator() {
        return policeLoanInfo ->
                policeLoanInfo.getIdCard() + "_" +
                        policeLoanInfo.getLoanInfo() + "_" +
                        policeLoanInfo.getLoanPurpose() + "_" +
                        policeLoanInfo.getLoanAmount() + "_" +
                        policeLoanInfo.getLenderName();
    }

    @Override
    public void batchInsert(List<PoliceLoanInfo> records) {
        policeLoanInfoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceLoanInfo> records) {
        policeLoanInfoService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeLoanInfoService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrGrjdService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeLoanInfoService.count();
    }
}
