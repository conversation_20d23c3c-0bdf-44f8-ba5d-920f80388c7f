package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceMarriageStatus;
import com.hl.archive.service.PoliceMarriageStatusService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrHyzk;
import com.hl.orasync.service.VWjBrHyzkService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceMarriageStatusSyncProcessor implements DataSyncProcessor<VWjBrHyzk, PoliceMarriageStatus> {

    private final PoliceMarriageStatusService policeMarriageStatusService;

    private final VWjBrHyzkService vWjBrHyzkService;

    private final Converter converter;
    @Override
    public List<VWjBrHyzk> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrHyzk> page = vWjBrHyzkService.page(Page.of(offset, limit), Wrappers.<VWjBrHyzk>lambdaQuery()
                .orderByDesc(VWjBrHyzk::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceMarriageStatus> getTargetData(int offset, int limit) {
        Page<PoliceMarriageStatus> page = policeMarriageStatusService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceMarriageStatus convert(VWjBrHyzk source) {
        return converter.convert(source, PoliceMarriageStatus.class);
    }

    @Override
    public Function<PoliceMarriageStatus, String> getBusinessKeyGenerator() {
        return policeMarriageStatus -> 
            policeMarriageStatus.getIdCard() + "_" + 
            policeMarriageStatus.getMarriageStatus() + "_" + 
            policeMarriageStatus.getChangeDate();
    }

    @Override
    public void batchInsert(List<PoliceMarriageStatus> records) {
        policeMarriageStatusService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceMarriageStatus> records) {
        policeMarriageStatusService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeMarriageStatusService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrHyzkService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeMarriageStatusService.count();
    }
}
