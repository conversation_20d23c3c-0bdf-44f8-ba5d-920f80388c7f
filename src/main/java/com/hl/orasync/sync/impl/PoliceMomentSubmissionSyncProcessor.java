package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.archive.service.PoliceMomentSubmissionService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjJcsjScsb;
import com.hl.orasync.service.VWjJcsjScsbService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceMomentSubmissionSyncProcessor implements DataSyncProcessor<VWjJcsjScsb, PoliceMomentSubmission> {

    private final VWjJcsjScsbService vWjJcsjScsbService;

    private final PoliceMomentSubmissionService policeMomentSubmissionService;

    private final Converter converter;

    @Override
    public List<VWjJcsjScsb> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjJcsjScsb> page = vWjJcsjScsbService.page(Page.of(offset, limit),
                Wrappers.<VWjJcsjScsb>lambdaQuery()
                        .orderByDesc(VWjJcsjScsb::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceMomentSubmission> getTargetData(int offset, int limit) {
        Page<PoliceMomentSubmission> page = policeMomentSubmissionService.page(Page.of(offset, limit),Wrappers.<PoliceMomentSubmission>lambdaQuery()
                .eq(PoliceMomentSubmission::getDataType,0));
        return page.getRecords();
    }

    @Override
    public PoliceMomentSubmission convert(VWjJcsjScsb source) {
        return converter.convert(source, PoliceMomentSubmission.class);
    }

    @Override
    public Function<PoliceMomentSubmission, String> getBusinessKeyGenerator() {
        return PoliceMomentSubmission::getZjbh;
    }

    @Override
    public void batchInsert(List<PoliceMomentSubmission> records) {
        policeMomentSubmissionService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceMomentSubmission> records) {
        policeMomentSubmissionService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeMomentSubmissionService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjJcsjScsbService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeMomentSubmissionService.count();
    }
}
