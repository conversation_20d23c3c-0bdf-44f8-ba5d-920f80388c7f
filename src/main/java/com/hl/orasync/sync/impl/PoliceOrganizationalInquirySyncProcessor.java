package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceOrganizationalInquiry;
import com.hl.archive.service.PoliceOrganizationalInquiryService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrWjdc;
import com.hl.orasync.service.VWjBrWjdcService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceOrganizationalInquirySyncProcessor implements DataSyncProcessor<VWjBrWjdc, PoliceOrganizationalInquiry> {

    private final VWjBrWjdcService vWjBrWjdcService;

    private final PoliceOrganizationalInquiryService policeOrganizationalInquiryService;

    private final Converter converter;

    @Override
    public List<VWjBrWjdc> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrWjdc> page = vWjBrWjdcService.page(Page.of(offset, limit), Wrappers.<VWjBrWjdc>lambdaQuery()
                .orderByDesc(VWjBrWjdc::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceOrganizationalInquiry> getTargetData(int offset, int limit) {
        Page<PoliceOrganizationalInquiry> page = policeOrganizationalInquiryService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceOrganizationalInquiry convert(VWjBrWjdc source) {
        return converter.convert(source, PoliceOrganizationalInquiry.class);
    }

    @Override
    public Function<PoliceOrganizationalInquiry, String> getBusinessKeyGenerator() {
        return policeOrganizationalInquiry ->
                policeOrganizationalInquiry.getIdCard() + "_" +
                        policeOrganizationalInquiry.getHandlingAuthority() + "_" +
                        policeOrganizationalInquiry.getSuspectedViolations() + "_" +
                        policeOrganizationalInquiry.getHandlingDate();
    }

    @Override
    public void batchInsert(List<PoliceOrganizationalInquiry> records) {
        policeOrganizationalInquiryService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceOrganizationalInquiry> records) {
        policeOrganizationalInquiryService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeOrganizationalInquiryService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrWjdcService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeOrganizationalInquiryService.count();
    }
}
