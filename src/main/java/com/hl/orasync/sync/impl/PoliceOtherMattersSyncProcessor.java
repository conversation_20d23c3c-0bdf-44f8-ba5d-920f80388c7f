package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceOtherMatters;
import com.hl.archive.service.PoliceOtherMattersService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjQtQtsx;
import com.hl.orasync.service.VWjQtQtsxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceOtherMattersSyncProcessor implements DataSyncProcessor<VWjQtQtsx, PoliceOtherMatters> {

    private final VWjQtQtsxService vWjQtQtsxService;

    private final PoliceOtherMattersService policeOtherMattersService;

    private final Converter converter;

    @Override
    public List<VWjQtQtsx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjQtQtsx> page = vWjQtQtsxService.page(Page.of(offset, limit), Wrappers.<VWjQtQtsx>lambdaQuery()
                .orderByDesc(VWjQtQtsx::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceOtherMatters> getTargetData(int offset, int limit) {
        Page<PoliceOtherMatters> page = policeOtherMattersService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceOtherMatters convert(VWjQtQtsx source) {
        return converter.convert(source, PoliceOtherMatters.class);
    }

    @Override
    public Function<PoliceOtherMatters, String> getBusinessKeyGenerator() {
        return policeOtherMatters ->
                policeOtherMatters.getIdCard() + "_" +
                        policeOtherMatters.getReportContent() + "_" +
                        policeOtherMatters.getRegistrationDate();
    }

    @Override
    public void batchInsert(List<PoliceOtherMatters> records) {
        policeOtherMattersService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceOtherMatters> records) {
        policeOtherMattersService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeOtherMattersService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjQtQtsxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeOtherMattersService.count();
    }
}
