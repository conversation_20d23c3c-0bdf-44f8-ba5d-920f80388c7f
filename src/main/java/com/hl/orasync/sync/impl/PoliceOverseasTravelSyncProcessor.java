package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceOverseasTravel;
import com.hl.archive.service.PoliceOverseasTravelService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrYscg;
import com.hl.orasync.service.VWjBrYscgService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceOverseasTravelSyncProcessor implements DataSyncProcessor<VWjBrYscg, PoliceOverseasTravel> {

    private final VWjBrYscgService vWjBrYscgService;

    private final PoliceOverseasTravelService policeOverseasTravelService;

    private final Converter converter;

    @Override
    public List<VWjBrYscg> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrYscg> page = vWjBrYscgService.page(Page.of(offset, limit), Wrappers.<VWjBrYscg>lambdaQuery()
                .orderByDesc(VWjBrYscg::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceOverseasTravel> getTargetData(int offset, int limit) {
        Page<PoliceOverseasTravel> page = policeOverseasTravelService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceOverseasTravel convert(VWjBrYscg source) {
        return converter.convert(source, PoliceOverseasTravel.class);
    }

    @Override
    public Function<PoliceOverseasTravel, String> getBusinessKeyGenerator() {
        return po -> po.getIdCard() + "_" + po.getPassportNumber() + "_" + po.getStartDate() + "_" + po.getEndDate() + "_" + po.getDestinationCountry();
    }

    @Override
    public void batchInsert(List<PoliceOverseasTravel> records) {
        policeOverseasTravelService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceOverseasTravel> records) {
        policeOverseasTravelService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeOverseasTravelService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrYscgService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeOverseasTravelService.count();
    }
}
