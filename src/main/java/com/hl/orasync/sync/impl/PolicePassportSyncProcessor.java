package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PolicePassport;
import com.hl.orasync.domain.VWjBrPthz;
import com.hl.orasync.service.VWjBrPthzService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.archive.service.PolicePassportService;

@Component
@RequiredArgsConstructor
public class PolicePassportSyncProcessor implements DataSyncProcessor<VWjBrPthz, PolicePassport> {

    private final VWjBrPthzService vWjBrPthzService;

    private final PolicePassportService policePassportService;

    private final Converter converter;

    @Override
    public List<VWjBrPthz> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrPthz> page = vWjBrPthzService.page(Page.of(offset, limit), Wrappers.<VWjBrPthz>lambdaQuery()
                .orderByDesc(VWjBrPthz::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PolicePassport> getTargetData(int offset, int limit) {
        Page<PolicePassport> page = policePassportService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PolicePassport convert(VWjBrPthz source) {
        return converter.convert(source, PolicePassport.class);
    }

    @Override
    public Function<PolicePassport, String> getBusinessKeyGenerator() {
        return PolicePassport::getIdCard;
    }

    @Override
    public void batchInsert(List<PolicePassport> records) {
        policePassportService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PolicePassport> records) {
        policePassportService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policePassportService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrPthzService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policePassportService.count();
    }
}
