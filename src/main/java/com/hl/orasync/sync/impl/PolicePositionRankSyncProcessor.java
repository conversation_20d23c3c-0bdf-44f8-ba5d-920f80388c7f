package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.archive.service.PolicePositionRankService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyzwzj;
import com.hl.orasync.service.VWjRyzwzjService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PolicePositionRankSyncProcessor implements DataSyncProcessor<VWjRyzwzj, PolicePositionRank> {


    private final VWjRyzwzjService vwjRyzwzjService;

    private final PolicePositionRankService policePositionRankService;

    private final Converter converter;

    @Override
    public List<VWjRyzwzj> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyzwzj> page = vwjRyzwzjService.page(Page.of(offset, limit), Wrappers.<VWjRyzwzj>lambdaQuery()
                .orderByDesc(VWjRyzwzj::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PolicePositionRank> getTargetData(int offset, int limit) {
        Page<PolicePositionRank> page = policePositionRankService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PolicePositionRank convert(VWjRyzwzj source) {
        return converter.convert(source, PolicePositionRank.class);
    }

    @Override
    public Function<PolicePositionRank, String> getBusinessKeyGenerator() {
        return policePositionRank -> policePositionRank.getIdCard()
                + "_" + policePositionRank.getPositionName()
                + "_" + policePositionRank.getCurrentPositionDate()
                +"_"+policePositionRank.getPolicePositionLevel()
                +"_"+policePositionRank.getAppointmentDocument()
                +"_"+policePositionRank.getCurrentRankDate();
    }

    @Override
    public void batchInsert(List<PolicePositionRank> records) {
        policePositionRankService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PolicePositionRank> records) {
        policePositionRankService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policePositionRankService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyzwzjService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policePositionRankService.count();
    }
}
