package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceProjectContact;
import com.hl.archive.service.PoliceProjectContactService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjXhjhRxgrPylx;
import com.hl.orasync.service.VWjXhjhRxgrPylxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceProjectContactSyncProcessor implements DataSyncProcessor<VWjXhjhRxgrPylx, PoliceProjectContact> {

    private final VWjXhjhRxgrPylxService vWjXhjhRxgrPylxService;

    private final PoliceProjectContactService policeProjectContactService;

    private final Converter converter;

    @Override
    public List<VWjXhjhRxgrPylx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjXhjhRxgrPylx> page = vWjXhjhRxgrPylxService.page(Page.of(offset, limit),
                Wrappers.<VWjXhjhRxgrPylx>lambdaQuery()
                        .orderByDesc(VWjXhjhRxgrPylx::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceProjectContact> getTargetData(int offset, int limit) {
        Page<PoliceProjectContact> page = policeProjectContactService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceProjectContact convert(VWjXhjhRxgrPylx source) {
        return converter.convert(source, PoliceProjectContact.class);
    }

    @Override
    public Function<PoliceProjectContact, String> getBusinessKeyGenerator() {
        return PoliceProjectContact::getZjbh;
    }

    @Override
    public void batchInsert(List<PoliceProjectContact> records) {
        policeProjectContactService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceProjectContact> records) {
        policeProjectContactService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeProjectContactService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjXhjhRxgrPylxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeProjectContactService.count();
    }
}
