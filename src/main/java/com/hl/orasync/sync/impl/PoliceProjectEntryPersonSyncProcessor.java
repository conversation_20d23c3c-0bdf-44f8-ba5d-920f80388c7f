package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.archive.service.PoliceProjectEntryPersonService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjXhjhRxgr;
import com.hl.orasync.service.VWjXhjhRxgrService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceProjectEntryPersonSyncProcessor implements DataSyncProcessor<VWjXhjhRxgr, PoliceProjectEntryPerson> {

    private final VWjXhjhRxgrService vWjXhjhRxgrService;

    private final PoliceProjectEntryPersonService policeProjectEntryPersonService;

    private final Converter converter;

    @Override
    public List<VWjXhjhRxgr> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjXhjhRxgr> page = vWjXhjhRxgrService.page(Page.of(offset, limit),
                Wrappers.<VWjXhjhRxgr>lambdaQuery()
                        .orderByDesc(VWjXhjhRxgr::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceProjectEntryPerson> getTargetData(int offset, int limit) {

        Page<PoliceProjectEntryPerson> page = policeProjectEntryPersonService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceProjectEntryPerson convert(VWjXhjhRxgr source) {
        return converter.convert(source, PoliceProjectEntryPerson.class);
    }

    @Override
    public Function<PoliceProjectEntryPerson, String> getBusinessKeyGenerator() {
        return policeProjectEntryPerson ->
                policeProjectEntryPerson.getZjbh() + "_" +
                        policeProjectEntryPerson.getIdCard();
    }

    @Override
    public void batchInsert(List<PoliceProjectEntryPerson> records) {
        policeProjectEntryPersonService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceProjectEntryPerson> records) {
        policeProjectEntryPersonService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeProjectEntryPersonService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjXhjhRxgrService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeProjectEntryPersonService.count();
    }
}
