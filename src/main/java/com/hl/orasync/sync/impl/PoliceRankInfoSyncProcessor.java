package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceRankInfo;
import com.hl.archive.service.PoliceRankInfoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjxxx;
import com.hl.orasync.service.VWjRyjxxxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;


@Component
@RequiredArgsConstructor
public class PoliceRankInfoSyncProcessor implements DataSyncProcessor<VWjRyjxxx, PoliceRankInfo> {

    private final Converter converter;

    private final PoliceRankInfoService policeRankInfoService;

    private final VWjRyjxxxService vwjRyjxxxService;

    @Override
    public List<VWjRyjxxx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyjxxx> page = vwjRyjxxxService.page(Page.of(offset, limit), Wrappers.<VWjRyjxxx>lambdaQuery()
                .orderByDesc(VWjRyjxxx::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceRankInfo> getTargetData(int offset, int limit) {
        Page<PoliceRankInfo> page = policeRankInfoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceRankInfo convert(VWjRyjxxx source) {
        return converter.convert(source, PoliceRankInfo.class);
    }

    @Override
    public Function<PoliceRankInfo, String> getBusinessKeyGenerator() {
        return policeRankInfo -> policeRankInfo.getIdCard() + "_" +
                policeRankInfo.getRankTitle() + "_" +
                policeRankInfo.getPromotionDate();
    }

    @Override
    public void batchInsert(List<PoliceRankInfo> records) {
        policeRankInfoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceRankInfo> records) {
        policeRankInfoService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeRankInfoService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyjxxxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeRankInfoService.count();
    }
}
