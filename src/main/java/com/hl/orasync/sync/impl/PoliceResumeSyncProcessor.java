package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceResume;
import com.hl.archive.service.PoliceResumeService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjlxx;
import com.hl.orasync.service.VWjRyjlxxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceResumeSyncProcessor implements DataSyncProcessor<VWjRyjlxx, PoliceResume> {

    private final Converter converter;

    private final PoliceResumeService policeResumeService;

    private final VWjRyjlxxService vwjRyjlxxService;

    @Override
    public List<VWjRyjlxx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyjlxx> page = vwjRyjlxxService.page(Page.of(offset, limit), Wrappers.<VWjRyjlxx>lambdaQuery()
                .orderByDesc(VWjRyjlxx::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return  page.getRecords();
    }

    @Override
    public List<PoliceResume> getTargetData(int offset, int limit) {
        Page<PoliceResume> page = policeResumeService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceResume convert(VWjRyjlxx source) {
        return converter.convert(source, PoliceResume.class);
    }

    @Override
    public Function<PoliceResume, String> getBusinessKeyGenerator() {
        return policeResume ->
                policeResume.getIdCard()+"_"+policeResume.getStartDate()+"_"+policeResume.getWorkUnit();
    }

    @Override
    public void batchInsert(List<PoliceResume> records) {
        policeResumeService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceResume> records) {
        policeResumeService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeResumeService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyjlxxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeResumeService.count();
    }
}
