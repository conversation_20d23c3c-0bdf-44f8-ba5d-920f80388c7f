package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceTrainingRecords;
import com.hl.archive.service.PoliceTrainingRecordsService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjXlda;
import com.hl.orasync.service.VWjXldaService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceTrainingRecordsSyncProcessor implements DataSyncProcessor<VWjXlda, PoliceTrainingRecords> {

    private final VWjXldaService vWjXldaService;

    private final PoliceTrainingRecordsService policeTrainingRecordsService;

    private final Converter converter;

    @Override
    public List<VWjXlda> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjXlda> page = vWjXldaService.page(Page.of(offset, limit), Wrappers.<VWjXlda>lambdaQuery()
                .orderByDesc(VWjXlda::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceTrainingRecords> getTargetData(int offset, int limit) {
        Page<PoliceTrainingRecords> page = policeTrainingRecordsService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceTrainingRecords convert(VWjXlda source) {
        return converter.convert(source, PoliceTrainingRecords.class);
    }

    @Override
    public Function<PoliceTrainingRecords, String> getBusinessKeyGenerator() {
        return policeTrainingRecords ->
                policeTrainingRecords.getIdCard() + "_" +
                        policeTrainingRecords.getTrainingName() + "_" +
                        policeTrainingRecords.getExamProjectName() + "_" +
                        policeTrainingRecords.getTrainingTime() + "_" +
                        policeTrainingRecords.getScore() + "_" +
                        policeTrainingRecords.getGrade();
    }

    @Override
    public void batchInsert(List<PoliceTrainingRecords> records) {
        policeTrainingRecordsService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceTrainingRecords> records) {
        policeTrainingRecordsService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeTrainingRecordsService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjXldaService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeTrainingRecordsService.count();
    }
}
