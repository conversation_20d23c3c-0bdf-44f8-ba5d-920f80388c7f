package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceUnitAward;
import com.hl.archive.service.PoliceUnitAwardService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBzjlDwry;
import com.hl.orasync.service.VWjBzjlDwryService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceUnitAwardSyncProcessor implements DataSyncProcessor<VWjBzjlDwry, PoliceUnitAward> {

    private final VWjBzjlDwryService vWjBzjlDwryService;

    private final PoliceUnitAwardService policeUnitAwardService;

    private final Converter converter;

    @Override
    public List<VWjBzjlDwry> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBzjlDwry> page = vWjBzjlDwryService.page(Page.of(offset, limit),
                Wrappers.<VWjBzjlDwry>lambdaQuery()
                        .orderByDesc(VWjBzjlDwry::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceUnitAward> getTargetData(int offset, int limit) {
        Page<PoliceUnitAward> page = policeUnitAwardService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceUnitAward convert(VWjBzjlDwry source) {
        return converter.convert(source, PoliceUnitAward.class);
    }

    @Override
    public Function<PoliceUnitAward, String> getBusinessKeyGenerator() {
        return policeUnitAward ->
                policeUnitAward.getAwardName() + "_" +
                policeUnitAward.getAwardTime() + "_" +
                policeUnitAward.getUnit();
    }

    @Override
    public void batchInsert(List<PoliceUnitAward> records) {
        policeUnitAwardService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceUnitAward> records) {
        policeUnitAwardService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeUnitAwardService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBzjlDwryService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeUnitAwardService.count();
    }
}
