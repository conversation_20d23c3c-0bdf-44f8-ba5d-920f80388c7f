package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceViolationPerson;
import com.hl.archive.service.PoliceViolationPersonService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjWgwjdjbRycl;
import com.hl.orasync.service.VWjWgwjdjbRyclService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceViolationPersonSyncProcessor implements DataSyncProcessor<VWjWgwjdjbRycl, PoliceViolationPerson> {

    private final VWjWgwjdjbRyclService vWjWgwjdjbRyclService;

    private final PoliceViolationPersonService policeViolationPersonService;

    private final Converter converter;

    @Override
    public List<VWjWgwjdjbRycl> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjWgwjdjbRycl> page = vWjWgwjdjbRyclService.page(new Page<>(offset, limit), Wrappers.<VWjWgwjdjbRycl>lambdaQuery()
                .orderByDesc(VWjWgwjdjbRycl::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceViolationPerson> getTargetData(int offset, int limit) {
        Page<PoliceViolationPerson> page = policeViolationPersonService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceViolationPerson convert(VWjWgwjdjbRycl source) {
        return converter.convert(source, PoliceViolationPerson.class);
    }

    @Override
    public Function<PoliceViolationPerson, String> getBusinessKeyGenerator() {
        return PoliceViolationPerson::getXxzjbh;
    }

    @Override
    public void batchInsert(List<PoliceViolationPerson> records) {
        policeViolationPersonService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceViolationPerson> records) {
        policeViolationPersonService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeViolationPersonService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjWgwjdjbRyclService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeViolationPersonService.count();
    }
}
