package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceViolationResult;
import com.hl.archive.service.PoliceViolationResultService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjWgwjdjbRycljg;
import com.hl.orasync.service.VWjWgwjdjbRycljgService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceViolationResultSyncProcessor implements DataSyncProcessor<VWjWgwjdjbRycljg, PoliceViolationResult> {

    private final VWjWgwjdjbRycljgService vWjWgwjdjbRycljgService;

    private final PoliceViolationResultService policeViolationResultService;

    private final Converter converter;

    @Override
    public List<VWjWgwjdjbRycljg> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjWgwjdjbRycljg> page = vWjWgwjdjbRycljgService.page(new Page<>(offset, limit), Wrappers.<VWjWgwjdjbRycljg>lambdaQuery()
                .orderByDesc(VWjWgwjdjbRycljg::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceViolationResult> getTargetData(int offset, int limit) {
        Page<PoliceViolationResult> page = policeViolationResultService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceViolationResult convert(VWjWgwjdjbRycljg source) {
        return converter.convert(source, PoliceViolationResult.class);
    }

    @Override
    public Function<PoliceViolationResult, String> getBusinessKeyGenerator() {
        return PoliceViolationResult::getXxzjbh;
    }

    @Override
    public void batchInsert(List<PoliceViolationResult> records) {
        policeViolationResultService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceViolationResult> records) {
        policeViolationResultService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeViolationResultService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjWgwjdjbRycljgService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeViolationResultService.count();
    }
}
