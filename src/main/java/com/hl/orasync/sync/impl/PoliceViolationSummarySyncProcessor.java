package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceViolationSummary;
import com.hl.archive.service.PoliceViolationSummaryService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjWgwjdjb;
import com.hl.orasync.service.VWjWgwjdjbService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceViolationSummarySyncProcessor implements DataSyncProcessor<VWjWgwjdjb, PoliceViolationSummary> {

    private final VWjWgwjdjbService vWjWgwjdjbService;

    private final PoliceViolationSummaryService policeViolationSummaryService;

    private final Converter converter;

    @Override
    public List<VWjWgwjdjb> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjWgwjdjb> page = vWjWgwjdjbService.page(new Page<>(offset, limit), Wrappers.<VWjWgwjdjb>lambdaQuery()
                .orderByDesc(VWjWgwjdjb::getXxzjbh));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceViolationSummary> getTargetData(int offset, int limit) {
        Page<PoliceViolationSummary> page = policeViolationSummaryService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceViolationSummary convert(VWjWgwjdjb source) {
        return converter.convert(source, PoliceViolationSummary.class);
    }

    @Override
    public Function<PoliceViolationSummary, String> getBusinessKeyGenerator() {
        return PoliceViolationSummary::getXxzjbh;
    }

    @Override
    public void batchInsert(List<PoliceViolationSummary> records) {
        policeViolationSummaryService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceViolationSummary> records) {
        policeViolationSummaryService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeViolationSummaryService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjWgwjdjbService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeViolationSummaryService.count();
    }
}
