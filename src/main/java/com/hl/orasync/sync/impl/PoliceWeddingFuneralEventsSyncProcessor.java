package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceWeddingFuneralEvents;
import com.hl.archive.service.PoliceWeddingFuneralEventsService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBrHsjq;
import com.hl.orasync.service.VWjBrHsjqService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceWeddingFuneralEventsSyncProcessor implements DataSyncProcessor<VWjBrHsjq, PoliceWeddingFuneralEvents> {

    private final VWjBrHsjqService vWjBrHsjqService;

    private final PoliceWeddingFuneralEventsService policeWeddingFuneralEventsService;

    private final Converter converter;

    @Override
    public List<VWjBrHsjq> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjBrHsjq> page = vWjBrHsjqService.page(Page.of(offset, limit), Wrappers.<VWjBrHsjq>lambdaQuery()
                .orderByDesc(VWjBrHsjq::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceWeddingFuneralEvents> getTargetData(int offset, int limit) {
        Page<PoliceWeddingFuneralEvents> page = policeWeddingFuneralEventsService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceWeddingFuneralEvents convert(VWjBrHsjq source) {
        return converter.convert(source, PoliceWeddingFuneralEvents.class);
    }

    @Override
    public Function<PoliceWeddingFuneralEvents, String> getBusinessKeyGenerator() {
        return policeWeddingFuneralEvents ->
                policeWeddingFuneralEvents.getIdCard() + "_" + policeWeddingFuneralEvents.getEventType()
                        + "_" + policeWeddingFuneralEvents.getEventDate() + "_" + policeWeddingFuneralEvents.getPartyName();
    }

    @Override
    public void batchInsert(List<PoliceWeddingFuneralEvents> records) {
        policeWeddingFuneralEventsService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceWeddingFuneralEvents> records) {
        policeWeddingFuneralEventsService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeWeddingFuneralEventsService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjBrHsjqService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeWeddingFuneralEventsService.count();
    }
}
