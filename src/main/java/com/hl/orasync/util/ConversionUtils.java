package com.hl.orasync.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
public class ConversionUtils {


    @Named("mapGender")
    public static Integer mapGender(String xb) {
        if ("男".equals(xb)) return 1;
        if ("女".equals(xb)) return 2;
        return null;
    }

    @Named("strToDate")
    public static LocalDateTime strToDate(String date) {
        if (StrUtil.isBlank(date)) return null;
        try {
            DateTime parse = DateUtil.parse(date);
            return parse.toLocalDateTime();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Named("strToLocalDate")
    public static LocalDate strToLocalDate(String date) {
        if (StrUtil.isBlank(date)) return null;
        try {
            DateTime parse = DateUtil.parse(date);
            return parse.toLocalDateTime().toLocalDate();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    @Named("strToBigDecimal")
    public static BigDecimal strToBigDecimal(String value) {
        if (value == null  || StrUtil.isBlank(value)) return null;
        try {
            return new BigDecimal(value.trim());
        }catch (Exception e) {
            log.error("strToBigDecimal error:{}",value);
            return null;
        }
    }

    @Named("bigDecimalToInteger")
    public static Integer bigDecimalToInteger(BigDecimal value) {
        if (value == null  ) return null;
        try {
            return value.intValue();
        }catch (Exception e) {
            log.error("bigDecimalToInteger error:{}",value);
            return null;
        }
    }


    @Named("bigDecimalToString")
    public static String bigDecimalToString(BigDecimal value) {
        if (value == null  ) return null;
        try {
            return value.toString();
        }catch (Exception e) {
            log.error("bigDecimalToString error:{}",value);
            return null;
        }
    }

}
