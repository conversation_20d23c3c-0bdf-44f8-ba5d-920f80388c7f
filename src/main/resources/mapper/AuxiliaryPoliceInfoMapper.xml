<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.AuxiliaryPoliceInfoMapper">

<resultMap id="AuxiliaryPoliceStatisticsDTOMap" type="com.hl.archive.domain.dto.AuxiliaryPoliceStatisticsDTO">
    <result column="department" property="department"/>
    <result column="total_count" property="totalCount"/>
    <result column="age_20_30" property="age20To30"/>
    <result column="age_30_40" property="age30To40"/>
    <result column="age_40_50" property="age40To50"/>
    <result column="age_50_up" property="age50Up"/>
    <result column="edu_junior_and_below" property="eduJuniorAndBelow"/>
    <result column="edu_technical" property="eduTechnical"/>
    <result column="edu_high_school" property="eduHighSchool"/>
    <result column="edu_college" property="eduCollege"/>
    <result column="edu_bachelor_and_above" property="eduBachelorAndAbove"/>
    <result column="avg_age" property="avgAge"/>
    <result column="male_count" property="maleCount"/>
    <result column="female_count" property="femaleCount"/>
    <result column="on_duty_count" property="onDutyCount"/>
    <result column="off_duty_count" property="offDutyCount"/>
</resultMap>

<select id="getAuxiliaryPoliceStatisticsByDepartment" resultMap="AuxiliaryPoliceStatisticsDTOMap">
    SELECT
      CONCAT( SUBSTR(organization_id,1,8),'0000') department,
      COUNT(*) AS total_count,
      SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30 THEN 1 ELSE 0 END) AS age_20_30,
      SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40 THEN 1 ELSE 0 END) AS age_30_40,
      SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50 THEN 1 ELSE 0 END) AS age_40_50,
      SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50 THEN 1 ELSE 0 END) AS age_50_up,
      -- 新的学历分类统计
      SUM(CASE WHEN education_level LIKE '%初中%' OR education_level LIKE '%小学%' OR education_level LIKE '%文盲%' OR education_level IS NULL OR education_level = '' THEN 1 ELSE 0 END) AS edu_junior_and_below,
      SUM(CASE WHEN education_level LIKE '%中专%' OR education_level LIKE '%技校%' THEN 1 ELSE 0 END) AS edu_technical,
      SUM(CASE WHEN education_level LIKE '%高中%' THEN 1 ELSE 0 END) AS edu_high_school,
      SUM(CASE WHEN education_level LIKE '%专科%' OR education_level LIKE '%大专%' THEN 1 ELSE 0 END) AS edu_college,
      SUM(CASE WHEN education_level LIKE '%本科%' OR education_level LIKE '%大学%' OR education_level LIKE '%硕士%' OR education_level LIKE '%博士%' THEN 1 ELSE 0 END) AS edu_bachelor_and_above,
      ROUND(AVG(TIMESTAMPDIFF(YEAR, birth_date, CURDATE())), 2) AS avg_age,
      SUM(CASE WHEN gender = '男' THEN 1 ELSE 0 END) AS male_count,
      SUM(CASE WHEN gender = '女' THEN 1 ELSE 0 END) AS female_count,
      SUM(CASE WHEN employment_status = '在职' THEN 1 ELSE 0 END) AS on_duty_count,
      SUM(CASE WHEN employment_status != '在职' OR employment_status IS NULL THEN 1 ELSE 0 END) AS off_duty_count
    FROM auxiliary_police_info
    WHERE is_deleted = 0
    GROUP BY CONCAT(SUBSTR(organization_id,1,8),'0000')
</select>

<select id="getAuxiliaryPoliceStatisticsByOrgId" resultMap="AuxiliaryPoliceStatisticsDTOMap">
    <choose>
      <when test="organizationId != null and (organizationId == '320412000000' or organizationId == '' )">
        <!-- 当organizationId为320412000000时，统计全部数据 -->
        SELECT
          '320412000000' as department,
          COUNT(*) AS total_count,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30 THEN 1 ELSE 0 END) AS age_20_30,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40 THEN 1 ELSE 0 END) AS age_30_40,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50 THEN 1 ELSE 0 END) AS age_40_50,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50 THEN 1 ELSE 0 END) AS age_50_up,
          -- 新的学历分类统计
          SUM(CASE WHEN education_level LIKE '%初中%' OR education_level LIKE '%小学%' OR education_level LIKE '%文盲%' OR education_level IS NULL OR education_level = '' THEN 1 ELSE 0 END) AS edu_junior_and_below,
          SUM(CASE WHEN education_level LIKE '%中专%' OR education_level LIKE '%技校%' THEN 1 ELSE 0 END) AS edu_technical,
          SUM(CASE WHEN education_level LIKE '%高中%' THEN 1 ELSE 0 END) AS edu_high_school,
          SUM(CASE WHEN education_level LIKE '%专科%' OR education_level LIKE '%大专%' THEN 1 ELSE 0 END) AS edu_college,
          SUM(CASE WHEN education_level LIKE '%本科%' OR education_level LIKE '%大学%' OR education_level LIKE '%硕士%' OR education_level LIKE '%博士%' THEN 1 ELSE 0 END) AS edu_bachelor_and_above,
          ROUND(AVG(TIMESTAMPDIFF(YEAR, birth_date, CURDATE())), 2) AS avg_age,
          SUM(CASE WHEN gender = '男' THEN 1 ELSE 0 END) AS male_count,
          SUM(CASE WHEN gender = '女' THEN 1 ELSE 0 END) AS female_count,
          SUM(CASE WHEN employment_status = '在职' THEN 1 ELSE 0 END) AS on_duty_count,
          SUM(CASE WHEN employment_status != '在职' OR employment_status IS NULL THEN 1 ELSE 0 END) AS off_duty_count

        FROM auxiliary_police_info
        WHERE is_deleted = 0
          <if test="organizationId != null and organizationId != ''">
            AND organization_id = #{organizationId}
          </if>
      </when>
      <otherwise>
        <!-- 其他情况按组织查询 -->
        SELECT
          CONCAT( SUBSTR(organization_id,1,8),'0000') department,
          COUNT(*) AS total_count,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30 THEN 1 ELSE 0 END) AS age_20_30,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40 THEN 1 ELSE 0 END) AS age_30_40,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50 THEN 1 ELSE 0 END) AS age_40_50,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50 THEN 1 ELSE 0 END) AS age_50_up,
          -- 新的学历分类统计
          SUM(CASE WHEN education_level LIKE '%初中%' OR education_level LIKE '%小学%' OR education_level LIKE '%文盲%' OR education_level IS NULL OR education_level = '' THEN 1 ELSE 0 END) AS edu_junior_and_below,
          SUM(CASE WHEN education_level LIKE '%中专%' OR education_level LIKE '%技校%' THEN 1 ELSE 0 END) AS edu_technical,
          SUM(CASE WHEN education_level LIKE '%高中%' THEN 1 ELSE 0 END) AS edu_high_school,
          SUM(CASE WHEN education_level LIKE '%专科%' OR education_level LIKE '%大专%' THEN 1 ELSE 0 END) AS edu_college,
          SUM(CASE WHEN education_level LIKE '%本科%' OR education_level LIKE '%大学%' OR education_level LIKE '%硕士%' OR education_level LIKE '%博士%' THEN 1 ELSE 0 END) AS edu_bachelor_and_above,
          ROUND(AVG(TIMESTAMPDIFF(YEAR, birth_date, CURDATE())), 2) AS avg_age,
          SUM(CASE WHEN gender = '男' THEN 1 ELSE 0 END) AS male_count,
          SUM(CASE WHEN gender = '女' THEN 1 ELSE 0 END) AS female_count,
          SUM(CASE WHEN employment_status = '在职' THEN 1 ELSE 0 END) AS on_duty_count,
          SUM(CASE WHEN employment_status != '在职' OR employment_status IS NULL THEN 1 ELSE 0 END) AS off_duty_count
        FROM auxiliary_police_info
        WHERE is_deleted = 0
          <if test="organizationId != null and organizationId != ''">
            AND organization_id LIKE CONCAT(#{organizationId}, '%')
          </if>
        GROUP BY CONCAT(SUBSTR(organization_id,1,8),'0000') limit 1
      </otherwise>
    </choose>
</select>

<!-- 辅警统计数字穿透查询 - 分页查询辅警基本信息 -->
<select id="getAuxiliaryPoliceListByStatisticsType" resultType="com.hl.archive.domain.entity.AuxiliaryPoliceInfo">
    SELECT *
    FROM auxiliary_police_info
    WHERE is_deleted = 0
      <!-- 组织ID处理 -->
      <choose>
        <when test="request.organizationId != null and request.organizationId == '320412000000'">
          and organization_id = '320412000000'
        </when>
        <when test="request.organizationId != null and request.organizationId != ''">
          AND organization_id LIKE CONCAT(#{request.organizationId}, '%')
        </when>
      </choose>

    <if test="request.name != null and request.name != ''">
      AND name LIKE CONCAT('%', #{request.name}, '%')
    </if>

      <!-- 优先使用自由组合条件，如果没有则使用statisticsType -->
      <choose>
          <when test="request.educationLevel != null or request.gender != null or request.minAge != null or request.maxAge != null or request.unitName != null or request.department != null or request.dutyStatus != null">
              <!-- 自由组合查询条件 -->
              <if test="request.educationLevel != null and request.educationLevel != ''">
                  AND education_level LIKE CONCAT('%', #{request.educationLevel}, '%')
              </if>
              <if test="request.gender != null">
                  <choose>
                      <when test="request.gender == 1">
                          AND gender = '男'
                      </when>
                      <when test="request.gender == 2">
                          AND gender = '女'
                      </when>
                  </choose>
              </if>
              <if test="request.minAge != null and request.maxAge != null">
                  AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN #{request.minAge} AND #{request.maxAge}
              </if>
              <if test="request.minAge != null and request.maxAge == null">
                  AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &gt;= #{request.minAge}
              </if>
              <if test="request.minAge == null and request.maxAge != null">
                  AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= #{request.maxAge}
              </if>
              <if test="request.unitName != null and request.unitName != ''">
                  AND unit_name LIKE CONCAT('%', #{request.unitName}, '%')
              </if>
              <if test="request.department != null and request.department != ''">
                  AND department LIKE CONCAT('%', #{request.department}, '%')
              </if>
              <if test="request.dutyStatus != null">
                  <choose>
                      <when test="request.dutyStatus == 0">
                          AND employment_status = '在职'
                      </when>
                      <otherwise>
                          AND employment_status != '在职'
                      </otherwise>
                  </choose>
              </if>
          </when>
        <otherwise>
          <!-- 使用statisticsType进行单一条件查询 -->
            <choose>
                <when test="request.statisticsType == 'age_20_30' or request.statisticsType == 'age20To30'">
                    AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30
                </when>
                <when test="request.statisticsType == 'age_30_40' or request.statisticsType == 'age30To40'">
                    AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40
                </when>
                <when test="request.statisticsType == 'age_40_50' or request.statisticsType == 'age40To50'">
                    AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50
                </when>
                <when test="request.statisticsType == 'age_50_up' or request.statisticsType == 'age50Up'">
                    AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50
                </when>
                <!-- 民警学历分类 -->
                <when test="request.statisticsType == 'edu_zhuan' or request.statisticsType == 'eduZhuan'">
                    AND (education_level LIKE '%专科%' OR education_level LIKE '%大专%')
                </when>
                <when test="request.statisticsType == 'edu_ben' or request.statisticsType == 'eduBen'">
                    AND education_level LIKE '%大学%'
                </when>
                <when test="request.statisticsType == 'edu_shuo' or request.statisticsType == 'eduShuo'">
                    AND education_level LIKE '%硕士%'
                </when>
                <when test="request.statisticsType == 'edu_boshi' or request.statisticsType == 'eduBoshi'">
                    AND education_level LIKE '%博士%'
                </when>
                <!-- 辅警学历分类 -->
                <when test="request.statisticsType == 'edu_junior_and_below' or request.statisticsType == 'eduJuniorAndBelow'">
                    AND (education_level LIKE '%初中%' OR education_level LIKE '%小学%' OR education_level LIKE '%文盲%' OR education_level IS NULL OR education_level = '')
                </when>
                <when test="request.statisticsType == 'edu_technical' or request.statisticsType == 'eduTechnical'">
                    AND (education_level LIKE '%中专%' OR education_level LIKE '%技校%')
                </when>
                <when test="request.statisticsType == 'edu_high_school' or request.statisticsType == 'eduHighSchool'">
                    AND education_level LIKE '%高中%'
                </when>
                <when test="request.statisticsType == 'edu_college' or request.statisticsType == 'eduCollege'">
                    AND (education_level LIKE '%专科%' OR education_level LIKE '%大专%')
                </when>
                <when test="request.statisticsType == 'edu_bachelor_and_above' or request.statisticsType == 'eduBachelorAndAbove'">
                    AND (education_level LIKE '%本科%' OR education_level LIKE '%大学%' OR education_level LIKE '%硕士%' OR education_level LIKE '%博士%')
                </when>
                <when test="request.statisticsType == 'maleCount'">
                    AND gender = '男'
                </when>
                <when test="request.statisticsType == 'femaleCount'">
                    AND gender = '女'
                </when>
                <when test="request.statisticsType == 'onDutyCount'">
                    AND employment_status = '在职'
                </when>
                <when test="request.statisticsType == 'offDutyCount'">
                    AND (employment_status != '在职')
                </when>
            </choose>
        </otherwise>
      </choose>
    ORDER BY created_at DESC
</select>

</mapper>