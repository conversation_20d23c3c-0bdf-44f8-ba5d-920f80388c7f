<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceCapabilityEvalMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceCapabilityEval">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_capability_eval-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="participant_name" jdbcType="VARCHAR" property="participantName" />
    <result column="police_number" jdbcType="VARCHAR" property="policeNumber" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="feature_name" jdbcType="VARCHAR" property="featureName" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="eval_status" jdbcType="VARCHAR" property="evalStatus" />
    <result column="eval_level" jdbcType="VARCHAR" property="evalLevel" />
    <result column="review_result" jdbcType="VARCHAR" property="reviewResult" />
    <result column="lcid" jdbcType="VARCHAR" property="lcid" />
    <result column="xxzjbh" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, participant_name, police_number, `position`, org_name, plan_name, category_name, 
    feature_name, reviewer, eval_status, eval_level, review_result, lcid, xxzjbh, is_deleted, 
    created_by, updated_by, created_at, updated_at
  </sql>
</mapper>