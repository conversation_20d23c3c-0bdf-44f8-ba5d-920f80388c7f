<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceChildrenForeignMarriageMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceChildrenForeignMarriage">
    <!--@mbg.generated-->
    <!--@Table police_children_foreign_marriage-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="child_name" jdbcType="VARCHAR" property="childName" />
    <result column="spouse_name" jdbcType="VARCHAR" property="spouseName" />
    <result column="spouse_country" jdbcType="VARCHAR" property="spouseCountry" />
    <result column="work_study_unit" jdbcType="VARCHAR" property="workStudyUnit" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="registration_date" jdbcType="DATE" property="registrationDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, child_name, spouse_name, spouse_country, work_study_unit, `position`, 
    registration_date, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>