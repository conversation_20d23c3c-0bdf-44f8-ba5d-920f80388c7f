<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceClubActivityEnrollMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceClubActivityEnroll">
    <!--@mbg.generated-->
    <!--@Table police_club_activity_enroll-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="acticity_id" jdbcType="BIGINT" property="acticityId" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="enroll_time" jdbcType="TIMESTAMP" property="enrollTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="approve_person" jdbcType="VARCHAR" property="approvePerson" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, acticity_id, id_card, enroll_time, `status`, remark, approve_person, create_at, 
    update_at, is_deleted, created_by, updated_by
  </sql>
</mapper>