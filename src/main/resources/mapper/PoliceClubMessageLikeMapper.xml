<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceClubMessageLikeMapper">

    <!-- 查询用户对指定留言的点赞记录 -->
    <select id="selectByMessageIdAndUserId" resultType="com.hl.archive.domain.entity.PoliceClubMessageLike">
        SELECT *
        FROM police_club_message_like
        WHERE message_id = #{messageId}
            AND user_id_card = #{userIdCard}
    </select>

    <!-- 删除用户对指定留言的点赞记录 -->
    <delete id="deleteByMessageIdAndUserId">
        DELETE FROM police_club_message_like
        WHERE message_id = #{messageId}
            AND user_id_card = #{userIdCard}
    </delete>

    <!-- 查询留言的点赞用户列表 -->
    <select id="selectLikeUsersByMessageId" resultType="com.hl.archive.domain.entity.PoliceClubMessageLike">
        SELECT *
        FROM police_club_message_like
        WHERE message_id = #{messageId}
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计留言的点赞数量 -->
    <select id="countLikesByMessageId" resultType="int">
        SELECT COUNT(*)
        FROM police_club_message_like
        WHERE message_id = #{messageId}
    </select>

    <!-- 批量查询用户的点赞状态 -->
    <select id="selectUserLikedMessageIds" resultType="java.lang.Long">
        SELECT message_id
        FROM police_club_message_like
        WHERE message_id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        AND user_id_card = #{userIdCard}
    </select>

</mapper>
