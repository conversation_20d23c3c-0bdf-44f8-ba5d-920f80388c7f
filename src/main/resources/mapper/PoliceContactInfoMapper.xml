<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceContactInfoMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceContactInfo">
    <!--@mbg.generated-->
    <!--@Table police_contact_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="home_address" jdbcType="VARCHAR" property="homeAddress" />
    <result column="home_phone" jdbcType="VARCHAR" property="homePhone" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="mobile_short_number" jdbcType="VARCHAR" property="mobileShortNumber" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, home_address, home_phone, mobile_phone, mobile_short_number, is_deleted, 
    created_at, updated_at, created_by, updated_by
  </sql>
</mapper>