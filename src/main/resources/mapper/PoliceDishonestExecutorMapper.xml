<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceDishonestExecutorMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceDishonestExecutor">
    <!--@mbg.generated-->
    <!--@Table police_dishonest_executor-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="party_name" jdbcType="VARCHAR" property="partyName" />
    <result column="relationship" jdbcType="VARCHAR" property="relationship" />
    <result column="dishonest_reason" jdbcType="LONGVARCHAR" property="dishonestReason" />
    <result column="execution_unit" jdbcType="VARCHAR" property="executionUnit" />
    <result column="dishonest_date" jdbcType="DATE" property="dishonestDate" />
    <result column="cancellation_date" jdbcType="DATE" property="cancellationDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, party_name, relationship, dishonest_reason, execution_unit, dishonest_date, 
    cancellation_date, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>