<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceEducationMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceEducation">
    <!--@mbg.generated-->
    <!--@Table police_education-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="education_level" jdbcType="VARCHAR" property="educationLevel" />
    <result column="school_name" jdbcType="VARCHAR" property="schoolName" />
    <result column="major_name" jdbcType="VARCHAR" property="majorName" />
    <result column="enrollment_date" jdbcType="DATE" property="enrollmentDate" />
    <result column="graduation_date" jdbcType="DATE" property="graduationDate" />
    <result column="degree" jdbcType="VARCHAR" property="degree" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, education_level, school_name, major_name, enrollment_date, graduation_date, 
    `degree`, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>