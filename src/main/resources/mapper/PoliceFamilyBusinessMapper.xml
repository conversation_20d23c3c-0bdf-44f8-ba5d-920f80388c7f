<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyBusinessMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyBusiness">
    <!--@mbg.generated-->
    <!--@Table police_family_business-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="social_credit_code" jdbcType="VARCHAR" property="socialCreditCode" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="establishment_date" jdbcType="DATE" property="establishmentDate" />
    <result column="business_scope" jdbcType="LONGVARCHAR" property="businessScope" />
    <result column="registration_address" jdbcType="VARCHAR" property="registrationAddress" />
    <result column="business_address" jdbcType="VARCHAR" property="businessAddress" />
    <result column="enterprise_type" jdbcType="VARCHAR" property="enterpriseType" />
    <result column="registered_capital" jdbcType="DECIMAL" property="registeredCapital" />
    <result column="enterprise_status" jdbcType="VARCHAR" property="enterpriseStatus" />
    <result column="is_shareholder" jdbcType="TINYINT" property="isShareholder" />
    <result column="personal_contribution" jdbcType="DECIMAL" property="personalContribution" />
    <result column="personal_contribution_ratio" jdbcType="DECIMAL" property="personalContributionRatio" />
    <result column="investment_date" jdbcType="DATE" property="investmentDate" />
    <result column="is_senior_position" jdbcType="TINYINT" property="isSeniorPosition" />
    <result column="senior_position_name" jdbcType="VARCHAR" property="seniorPositionName" />
    <result column="senior_position_date" jdbcType="DATE" property="seniorPositionDate" />
    <result column="has_business_relation" jdbcType="TINYINT" property="hasBusinessRelation" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, social_credit_code, enterprise_name, establishment_date, business_scope, 
    registration_address, business_address, enterprise_type, registered_capital, enterprise_status, 
    is_shareholder, personal_contribution, personal_contribution_ratio, investment_date, 
    is_senior_position, senior_position_name, senior_position_date, has_business_relation, 
    remarks, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>