<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyCriminalLiabilityMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyCriminalLiability">
    <!--@mbg.generated-->
    <!--@Table police_family_criminal_liability-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="prosecution_date" jdbcType="DATE" property="prosecutionDate" />
    <result column="prosecution_reason" jdbcType="LONGVARCHAR" property="prosecutionReason" />
    <result column="handling_stage" jdbcType="VARCHAR" property="handlingStage" />
    <result column="handling_result" jdbcType="LONGVARCHAR" property="handlingResult" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, prosecution_date, prosecution_reason, handling_stage, handling_result, 
    is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>