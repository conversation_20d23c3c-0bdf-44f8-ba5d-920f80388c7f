<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyMembersMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyMembers">
    <!--@mbg.generated-->
    <!--@Table police_family_members-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="relationship" jdbcType="VARCHAR" property="relationship" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="birth_date" jdbcType="DATE" property="birthDate" />
    <result column="work_unit_position" jdbcType="VARCHAR" property="workUnitPosition" />
    <result column="political_status" jdbcType="VARCHAR" property="politicalStatus" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, relationship, member_name, birth_date, work_unit_position, political_status, 
    mobile_phone, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>