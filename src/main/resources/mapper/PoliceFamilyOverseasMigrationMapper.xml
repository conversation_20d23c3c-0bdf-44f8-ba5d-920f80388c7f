<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyOverseasMigrationMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyOverseasMigration">
    <!--@mbg.generated-->
    <!--@Table police_family_overseas_migration-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="family_member_name" jdbcType="VARCHAR" property="familyMemberName" />
    <result column="migration_country" jdbcType="VARCHAR" property="migrationCountry" />
    <result column="current_city" jdbcType="VARCHAR" property="currentCity" />
    <result column="migration_document_number" jdbcType="VARCHAR" property="migrationDocumentNumber" />
    <result column="migration_category" jdbcType="VARCHAR" property="migrationCategory" />
    <result column="basis_date" jdbcType="DATE" property="basisDate" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, family_member_name, migration_country, current_city, migration_document_number, 
    migration_category, basis_date, remarks, is_deleted, created_at, updated_at, created_by, 
    updated_by
  </sql>
</mapper>