<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyPaidInstitutionsMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyPaidInstitutions">
    <!--@mbg.generated-->
    <!--@Table police_family_paid_institutions-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="professional_qualification" jdbcType="VARCHAR" property="professionalQualification" />
    <result column="license_number" jdbcType="VARCHAR" property="licenseNumber" />
    <result column="institution_name" jdbcType="VARCHAR" property="institutionName" />
    <result column="social_credit_code" jdbcType="VARCHAR" property="socialCreditCode" />
    <result column="establishment_date" jdbcType="DATE" property="establishmentDate" />
    <result column="business_scope" jdbcType="LONGVARCHAR" property="businessScope" />
    <result column="registration_address" jdbcType="VARCHAR" property="registrationAddress" />
    <result column="business_address" jdbcType="VARCHAR" property="businessAddress" />
    <result column="institution_type" jdbcType="VARCHAR" property="institutionType" />
    <result column="registered_capital" jdbcType="DECIMAL" property="registeredCapital" />
    <result column="operation_status" jdbcType="VARCHAR" property="operationStatus" />
    <result column="is_shareholder" jdbcType="TINYINT" property="isShareholder" />
    <result column="personal_contribution" jdbcType="DECIMAL" property="personalContribution" />
    <result column="personal_contribution_ratio" jdbcType="DECIMAL" property="personalContributionRatio" />
    <result column="partnership_date" jdbcType="DATE" property="partnershipDate" />
    <result column="is_working_in_institution" jdbcType="TINYINT" property="isWorkingInInstitution" />
    <result column="position_name" jdbcType="VARCHAR" property="positionName" />
    <result column="employment_date" jdbcType="DATE" property="employmentDate" />
    <result column="has_business_relation" jdbcType="TINYINT" property="hasBusinessRelation" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, professional_qualification, license_number, institution_name, 
    social_credit_code, establishment_date, business_scope, registration_address, business_address, 
    institution_type, registered_capital, operation_status, is_shareholder, personal_contribution, 
    personal_contribution_ratio, partnership_date, is_working_in_institution, position_name, 
    employment_date, has_business_relation, remarks, is_deleted, created_at, updated_at, 
    created_by, updated_by
  </sql>
</mapper>