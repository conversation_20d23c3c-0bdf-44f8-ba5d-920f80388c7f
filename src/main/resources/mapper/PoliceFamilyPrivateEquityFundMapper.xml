<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyPrivateEquityFundMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyPrivateEquityFund">
    <!--@mbg.generated-->
    <!--@Table police_family_private_equity_fund-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="fund_name_code" jdbcType="VARCHAR" property="fundNameCode" />
    <result column="total_paid_amount" jdbcType="DECIMAL" property="totalPaidAmount" />
    <result column="personal_paid_amount" jdbcType="DECIMAL" property="personalPaidAmount" />
    <result column="fund_investment_direction" jdbcType="LONGVARCHAR" property="fundInvestmentDirection" />
    <result column="contract_signing_date" jdbcType="DATE" property="contractSigningDate" />
    <result column="contract_expiry_date" jdbcType="DATE" property="contractExpiryDate" />
    <result column="fund_manager_name_code" jdbcType="VARCHAR" property="fundManagerNameCode" />
    <result column="is_actual_controller" jdbcType="TINYINT" property="isActualController" />
    <result column="is_shareholder" jdbcType="TINYINT" property="isShareholder" />
    <result column="subscription_amount" jdbcType="DECIMAL" property="subscriptionAmount" />
    <result column="subscription_ratio" jdbcType="DECIMAL" property="subscriptionRatio" />
    <result column="subscription_date" jdbcType="DATE" property="subscriptionDate" />
    <result column="is_senior_position" jdbcType="TINYINT" property="isSeniorPosition" />
    <result column="senior_position_name" jdbcType="VARCHAR" property="seniorPositionName" />
    <result column="senior_position_date" jdbcType="DATE" property="seniorPositionDate" />
    <result column="manager_business_scope" jdbcType="LONGVARCHAR" property="managerBusinessScope" />
    <result column="has_business_relation" jdbcType="TINYINT" property="hasBusinessRelation" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, fund_name_code, total_paid_amount, personal_paid_amount, fund_investment_direction, 
    contract_signing_date, contract_expiry_date, fund_manager_name_code, is_actual_controller, 
    is_shareholder, subscription_amount, subscription_ratio, subscription_date, is_senior_position, 
    senior_position_name, senior_position_date, manager_business_scope, has_business_relation, 
    remarks, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>