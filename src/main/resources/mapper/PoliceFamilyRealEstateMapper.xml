<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyRealEstateMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyRealEstate">
    <!--@mbg.generated-->
    <!--@Table police_family_real_estate-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="property_owner_name" jdbcType="VARCHAR" property="propertyOwnerName" />
    <result column="property_source" jdbcType="VARCHAR" property="propertySource" />
    <result column="property_address" jdbcType="VARCHAR" property="propertyAddress" />
    <result column="building_area" jdbcType="DECIMAL" property="buildingArea" />
    <result column="property_type" jdbcType="VARCHAR" property="propertyType" />
    <result column="transaction_date" jdbcType="DATE" property="transactionDate" />
    <result column="transaction_price" jdbcType="DECIMAL" property="transactionPrice" />
    <result column="property_disposition" jdbcType="VARCHAR" property="propertyDisposition" />
    <result column="sale_date" jdbcType="DATE" property="saleDate" />
    <result column="sale_price" jdbcType="DECIMAL" property="salePrice" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, property_owner_name, property_source, property_address, building_area, 
    property_type, transaction_date, transaction_price, property_disposition, sale_date, 
    sale_price, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>