<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilySecuritiesInsuranceMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilySecuritiesInsurance">
    <!--@mbg.generated-->
    <!--@Table police_family_securities_insurance-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="holder_name" jdbcType="VARCHAR" property="holderName" />
    <result column="security_name_code" jdbcType="VARCHAR" property="securityNameCode" />
    <result column="holding_quantity" jdbcType="DECIMAL" property="holdingQuantity" />
    <result column="net_value_premium" jdbcType="DECIMAL" property="netValuePremium" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, holder_name, security_name_code, holding_quantity, net_value_premium, 
    is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>