<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceFamilyVehiclesMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceFamilyVehicles">
    <!--@mbg.generated-->
    <!--@Table police_family_vehicles-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="vehicle_source" jdbcType="VARCHAR" property="vehicleSource" />
    <result column="vehicle_brand" jdbcType="VARCHAR" property="vehicleBrand" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
    <result column="transaction_date" jdbcType="DATE" property="transactionDate" />
    <result column="vehicle_disposition" jdbcType="VARCHAR" property="vehicleDisposition" />
    <result column="sale_amount" jdbcType="DECIMAL" property="saleAmount" />
    <result column="sale_date" jdbcType="DATE" property="saleDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, owner_name, vehicle_source, vehicle_brand, license_plate, transaction_amount, 
    transaction_date, vehicle_disposition, sale_amount, sale_date, is_deleted, created_at, 
    updated_at, created_by, updated_by
  </sql>
</mapper>