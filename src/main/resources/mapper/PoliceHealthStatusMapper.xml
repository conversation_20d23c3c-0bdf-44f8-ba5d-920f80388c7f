<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceHealthStatusMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceHealthStatus">
    <!--@mbg.generated-->
    <!--@Table police_health_status-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="has_serious_illness" jdbcType="TINYINT" property="hasSeriousIllness" />
    <result column="illness_name" jdbcType="VARCHAR" property="illnessName" />
    <result column="diagnosis_date" jdbcType="DATE" property="diagnosisDate" />
    <result column="diagnosis_institution" jdbcType="VARCHAR" property="diagnosisInstitution" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, has_serious_illness, illness_name, diagnosis_date, diagnosis_institution, 
    remarks, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>