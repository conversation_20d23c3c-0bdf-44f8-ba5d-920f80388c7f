<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceInjuryDeclareMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceInjuryDeclare">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_injury_declare-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="police_number" jdbcType="VARCHAR" property="policeNumber" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="declare_type" jdbcType="VARCHAR" property="declareType" />
    <result column="current_status" jdbcType="VARCHAR" property="currentStatus" />
    <result column="injury_event" jdbcType="VARCHAR" property="injuryEvent" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, police_number, id_card, org_name, `position`, declare_type, current_status, 
    injury_event, is_deleted, created_by, updated_by, created_at, updated_at
  </sql>
</mapper>