<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceLeaveRecordMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceLeaveRecord">
    <!--@mbg.generated-->
    <!--@Table police_leave_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="leave_type" jdbcType="VARCHAR" property="leaveType" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, start_date, end_date, id_card, leave_type, is_deleted, create_at, update_at
  </sql>
</mapper>