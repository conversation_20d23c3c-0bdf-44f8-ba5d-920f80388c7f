<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceLoanInfoMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceLoanInfo">
    <!--@mbg.generated-->
    <!--@Table police_loan_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="loan_info" jdbcType="VARCHAR" property="loanInfo" />
    <result column="lender_name" jdbcType="VARCHAR" property="lenderName" />
    <result column="loan_purpose" jdbcType="VARCHAR" property="loanPurpose" />
    <result column="loan_amount" jdbcType="DECIMAL" property="loanAmount" />
    <result column="loan_date" jdbcType="DATE" property="loanDate" />
    <result column="repayment_deadline" jdbcType="DATE" property="repaymentDeadline" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, loan_info, lender_name, loan_purpose, loan_amount, loan_date, repayment_deadline, 
    is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>