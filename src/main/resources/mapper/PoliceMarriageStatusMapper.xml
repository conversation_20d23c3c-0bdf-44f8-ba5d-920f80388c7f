<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceMarriageStatusMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceMarriageStatus">
    <!--@mbg.generated-->
    <!--@Table police_marriage_status-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="marriage_status" jdbcType="VARCHAR" property="marriageStatus" />
    <result column="change_status" jdbcType="VARCHAR" property="changeStatus" />
    <result column="change_date" jdbcType="DATE" property="changeDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, marriage_status, change_status, change_date, is_deleted, created_at, 
    updated_at, created_by, updated_by
  </sql>
</mapper>