<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceMomentSubmissionMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceMomentSubmission">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_moment_submission-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="submit_unit" jdbcType="VARCHAR" property="submitUnit" />
    <result column="submitter" jdbcType="VARCHAR" property="submitter" />
    <result column="officer_name" jdbcType="VARCHAR" property="officerName" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="submission_type" jdbcType="VARCHAR" property="submissionType" />
    <result column="submission_time" jdbcType="TIMESTAMP" property="submissionTime" />
    <result column="material_intro" jdbcType="LONGVARCHAR" property="materialIntro" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="video_available" jdbcType="BOOLEAN" property="videoAvailable" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, submit_unit, submitter, officer_name, material_type, submission_type, 
    submission_time, material_intro, audit_result, video_available, created_at, create_by, 
    updated_at, update_by, is_deleted
  </sql>
</mapper>