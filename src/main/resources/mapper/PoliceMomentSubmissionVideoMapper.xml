<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceMomentSubmissionVideoMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceMomentSubmissionVideo">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_moment_submission_video-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
    <result column="zjbh" jdbcType="VARCHAR" property="zjbh" />
    <result column="sb_zjbh" jdbcType="VARCHAR" property="sbZjbh" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="LONGVARCHAR" property="fileUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, created_at, create_by, updated_at, update_by, is_deleted, zjbh, sb_zjbh, file_type, 
    file_name, file_url
  </sql>
</mapper>