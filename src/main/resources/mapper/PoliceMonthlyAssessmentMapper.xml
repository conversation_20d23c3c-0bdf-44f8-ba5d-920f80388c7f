<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceMonthlyAssessmentMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceMonthlyAssessment">
    <!--@mbg.generated-->
    <!--@Table police_monthly_assessment-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="assessment_notice" jdbcType="VARCHAR" property="assessmentNotice" />
    <result column="assessment_year" jdbcType="OTHER" property="assessmentYear" />
    <result column="assessment_month" jdbcType="TINYINT" property="assessmentMonth" />
    <result column="assessment_score" jdbcType="DECIMAL" property="assessmentScore" />
    <result column="assessment_bonus" jdbcType="DECIMAL" property="assessmentBonus" />
    <result column="assessment_ranking" jdbcType="INTEGER" property="assessmentRanking" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, assessment_notice, assessment_year, assessment_month, assessment_score, 
    assessment_bonus, assessment_ranking, is_deleted, created_at, updated_at, created_by, 
    updated_by
  </sql>
</mapper>