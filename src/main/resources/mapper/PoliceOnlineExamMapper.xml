<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceOnlineExamMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceOnlineExam">
    <!--@mbg.generated-->
    <!--@Table police_online_exam-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="exam_paper_name" jdbcType="VARCHAR" property="examPaperName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="question_count" jdbcType="INTEGER" property="questionCount" />
    <result column="exam_duration" jdbcType="INTEGER" property="examDuration" />
    <result column="interruption_count" jdbcType="INTEGER" property="interruptionCount" />
    <result column="retake_count" jdbcType="INTEGER" property="retakeCount" />
    <result column="submit_status" jdbcType="VARCHAR" property="submitStatus" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, exam_paper_name, start_time, end_time, question_count, exam_duration, 
    interruption_count, retake_count, submit_status, score, is_deleted, created_at, updated_at, 
    created_by, updated_by
  </sql>
</mapper>