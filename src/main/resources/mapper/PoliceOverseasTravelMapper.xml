<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceOverseasTravelMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceOverseasTravel">
    <!--@mbg.generated-->
    <!--@Table police_overseas_travel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="destination_country" jdbcType="VARCHAR" property="destinationCountry" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="travel_reason" jdbcType="VARCHAR" property="travelReason" />
    <result column="approval_authority" jdbcType="VARCHAR" property="approvalAuthority" />
    <result column="passport_number" jdbcType="VARCHAR" property="passportNumber" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, destination_country, start_date, end_date, travel_reason, approval_authority, 
    passport_number, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>