<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PolicePassportMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PolicePassport">
    <!--@mbg.generated-->
    <!--@Table police_passport-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="passport_number" jdbcType="VARCHAR" property="passportNumber" />
    <result column="issue_date" jdbcType="DATE" property="issueDate" />
    <result column="expiry_date" jdbcType="DATE" property="expiryDate" />
    <result column="custody_organization" jdbcType="VARCHAR" property="custodyOrganization" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, passport_number, issue_date, expiry_date, custody_organization, remarks, 
    is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>