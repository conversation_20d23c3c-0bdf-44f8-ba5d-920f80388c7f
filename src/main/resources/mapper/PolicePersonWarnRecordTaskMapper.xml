<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PolicePersonWarnRecordTaskMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PolicePersonWarnRecordTask">
    <!--@mbg.generated-->
    <!--@Table police_person_warn_record_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="warn_id" jdbcType="BIGINT" property="warnId" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, warn_id, task_type, task_id, create_by, create_at, update_by, update_at, is_deleted
  </sql>
</mapper>