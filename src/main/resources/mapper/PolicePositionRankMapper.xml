<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PolicePositionRankMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PolicePositionRank">
    <!--@mbg.generated-->
    <!--@Table police_position_rank-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="position_name" jdbcType="VARCHAR" property="positionName" />
    <result column="police_position_level" jdbcType="VARCHAR" property="policePositionLevel" />
    <result column="current_position_date" jdbcType="DATE" property="currentPositionDate" />
    <result column="current_rank_date" jdbcType="DATE" property="currentRankDate" />
    <result column="appointment_document" jdbcType="VARCHAR" property="appointmentDocument" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, position_name, police_position_level, current_position_date, current_rank_date, 
    appointment_document, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>