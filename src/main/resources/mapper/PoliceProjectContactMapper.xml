<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceProjectContactMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceProjectContact">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_project_contact-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_position" jdbcType="VARCHAR" property="contactPosition" />
    <result column="traits" jdbcType="VARCHAR" property="traits" />
    <result column="evaluation" jdbcType="LONGVARCHAR" property="evaluation" />
    <result column="registered_by" jdbcType="VARCHAR" property="registeredBy" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, contact_name, contact_position, traits, evaluation, registered_by, register_time, 
    created_at, create_by, updated_at, update_by, is_deleted
  </sql>
</mapper>