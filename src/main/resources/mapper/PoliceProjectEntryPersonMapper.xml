<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceProjectEntryPersonMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceProjectEntryPerson">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_project_entry_person-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="police_number" jdbcType="VARCHAR" property="policeNumber" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="honor_level" jdbcType="VARCHAR" property="honorLevel" />
    <result column="honor_name" jdbcType="VARCHAR" property="honorName" />
    <result column="entry_time" jdbcType="TIMESTAMP" property="entryTime" />
    <result column="contact_person" jdbcType="VARCHAR" property="contactPerson" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, `name`, police_number, unit, honor_level, honor_name, entry_time, contact_person, 
    audit_status, created_at, create_by, updated_at, update_by, is_deleted
  </sql>
</mapper>