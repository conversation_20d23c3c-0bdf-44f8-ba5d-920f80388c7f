<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceProjectMaterialMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceProjectMaterial">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_project_material-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="image_name" jdbcType="VARCHAR" property="imageName" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, image_name, image_url, created_at, create_by, updated_at, update_by, 
    is_deleted
  </sql>
</mapper>