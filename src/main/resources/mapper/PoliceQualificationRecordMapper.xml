<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceQualificationRecordMapper">

    <select id="queryLatestRecord" resultType="com.hl.archive.domain.entity.PoliceQualificationRecord">
        select t.*
        from police_qualification_record t
                 join (select id_card, project, max(issue_date) as issue_date from police_qualification_record where is_deleted = 0 group by id_card, project ) as m
                      on t.id_card = m.id_card and t.project = m.project and t.issue_date = m.issue_date where t.is_deleted = 0
        <if test="idCard != null and idCard != ''">
            and t.id_card = #{idCard}
        </if>
    </select>
</mapper>