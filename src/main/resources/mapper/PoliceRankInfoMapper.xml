<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceRankInfoMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceRankInfo">
    <!--@mbg.generated-->
    <!--@Table police_rank_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="rank_title" jdbcType="VARCHAR" property="rankTitle" />
    <result column="promotion_date" jdbcType="DATE" property="promotionDate" />
    <result column="promotion_reason" jdbcType="VARCHAR" property="promotionReason" />
    <result column="rank_type" jdbcType="VARCHAR" property="rankType" />
    <result column="admin_level_at_promotion" jdbcType="VARCHAR" property="adminLevelAtPromotion" />
    <result column="rank_end_date" jdbcType="DATE" property="rankEndDate" />
    <result column="rank_start_date" jdbcType="DATE" property="rankStartDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, rank_title, promotion_date, promotion_reason, rank_type, admin_level_at_promotion, 
    rank_end_date, rank_start_date, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>