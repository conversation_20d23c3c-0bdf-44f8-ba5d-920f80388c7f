<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceRiskSummaryMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceRiskSummary">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_risk_summary-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="annual" jdbcType="VARCHAR" property="annual" />
    <result column="month" jdbcType="VARCHAR" property="month" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="birth_date" jdbcType="DATE" property="birthDate" />
    <result column="work_date" jdbcType="DATE" property="workDate" />
    <result column="political_status" jdbcType="VARCHAR" property="politicalStatus" />
    <result column="join_party_date" jdbcType="DATE" property="joinPartyDate" />
    <result column="duty_level1" jdbcType="VARCHAR" property="dutyLevel1" />
    <result column="duty_level2" jdbcType="VARCHAR" property="dutyLevel2" />
    <result column="rank" jdbcType="VARCHAR" property="rank" />
    <result column="police_post" jdbcType="VARCHAR" property="policePost" />
    <result column="risk_status" jdbcType="VARCHAR" property="riskStatus" />
    <result column="risk_type" jdbcType="VARCHAR" property="riskType" />
    <result column="risk_level" jdbcType="VARCHAR" property="riskLevel" />
    <result column="care_measures" jdbcType="VARCHAR" property="careMeasures" />
    <result column="care_record" jdbcType="LONGVARCHAR" property="careRecord" />
    <result column="monthly_change" jdbcType="VARCHAR" property="monthlyChange" />
    <result column="disposal_date" jdbcType="DATE" property="disposalDate" />
    <result column="disposal_category" jdbcType="VARCHAR" property="disposalCategory" />
    <result column="disposal_type" jdbcType="VARCHAR" property="disposalType" />
    <result column="responsible_leader" jdbcType="VARCHAR" property="responsibleLeader" />
    <result column="political_commissar" jdbcType="VARCHAR" property="politicalCommissar" />
    <result column="charge_leader" jdbcType="VARCHAR" property="chargeLeader" />
    <result column="home_address" jdbcType="VARCHAR" property="homeAddress" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="family_members" jdbcType="LONGVARCHAR" property="familyMembers" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, annual, `month`, `name`, org_name, id_card, `position`, gender, birth_date, work_date, 
    political_status, join_party_date, duty_level1, duty_level2, `rank`, police_post, 
    risk_status, risk_type, risk_level, care_measures, care_record, monthly_change, disposal_date, 
    disposal_category, disposal_type, responsible_leader, political_commissar, charge_leader, 
    home_address, phone, family_members, is_deleted, created_by, updated_by, created_at, 
    updated_at
  </sql>
</mapper>