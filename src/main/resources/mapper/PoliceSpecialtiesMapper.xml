<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceSpecialtiesMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceSpecialties">
    <!--@mbg.generated-->
    <!--@Table police_specialties-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="specialty_name" jdbcType="VARCHAR" property="specialtyName" />
    <result column="award_date" jdbcType="DATE" property="awardDate" />
    <result column="approve_authority" jdbcType="VARCHAR" property="approveAuthority" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, specialty_name, award_date, approve_authority, is_deleted, created_at, 
    updated_at, created_by, updated_by
  </sql>
</mapper>