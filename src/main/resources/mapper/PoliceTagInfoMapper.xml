<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceTagInfoMapper">

  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceTagInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_cards" jdbcType="VARCHAR" property="idCards" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler" />
    <result column="tag_type" jdbcType="VARCHAR" property="tagType" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler" />
    <result column="award_date" jdbcType="DATE" property="awardDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>

    <resultMap id="policePersonalTagReturnDTO" type="com.hl.archive.domain.dto.PolicePersonalTagReturnDTO">
        <result column="id_card" property="idCard"/>
        <result column="tag_name_list" property="tagNameList" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="tag_type_list" property="tagTypeList" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
    </resultMap>


  <!-- 根据标签类型获取标签信息列表（简单查询） -->
  <select id="getTagInfoByType" resultMap="BaseResultMap">
    SELECT *
    FROM police_tag_info pti
    WHERE pti.tag_type = #{tagType}
      AND pti.is_deleted = 0
      <if test="organizationId != null and organizationId != ''">
        AND EXISTS (
          SELECT 1 FROM police_basic_info pbi
          WHERE JSON_CONTAINS(pti.id_cards, JSON_QUOTE(pbi.id_card))
            AND pbi.organization_id LIKE CONCAT(#{organizationId}, '%')
        )
      </if>
    ORDER BY pti.created_at DESC
  </select>

  <!-- 根据标签类型获取标签信息列表（用于穿透查询） -->
  <select id="getTagInfoForDrillDown" resultMap="BaseResultMap">
    SELECT *
    FROM police_tag_info pti
    WHERE pti.tag_type = #{tagType}
      AND pti.is_deleted = 0
      <if test="tagName != null and tagName != ''">
        AND JSON_CONTAINS(pti.tag_name, JSON_QUOTE(#{tagName}))
      </if>
    ORDER BY pti.created_at DESC
  </select>

  <!-- 根据身份证号列表获取人员基本信息（分页） -->
  <select id="getPoliceByIdCards" resultType="com.hl.archive.domain.entity.PoliceBasicInfo">
    SELECT *
    FROM police_basic_info pbi
    WHERE pbi.is_deleted = 0
      AND pbi.id_card IN
      <foreach collection="idCards" item="idCard" open="(" separator="," close=")">
        #{idCard}
      </foreach>
      <if test="organizationId != null and organizationId != ''">
        AND pbi.organization_id LIKE CONCAT(#{organizationId}, '%')
      </if>
    ORDER BY pbi.name
  </select>

  <select id="pagePolicePersonalTag" resultMap="policePersonalTagReturnDTO">
      select * from view_police_tag_aggregation
      <where>
          <if test="param.tagType != null and param.tagType != ''">
              and tag_type_list like concat('%',#{param.tagType},'%')
          </if>
          <if test="param.idCard != null and param.idCard != ''">
              and id_card = #{param.idCard}
          </if>
          <if test="param.organizationId != null and param.organizationId != ''">
              and organization_id like concat(#{param.organizationId},'%')
          </if>
      </where>
    </select>
</mapper>