<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceTagInfoXjdfMapper">

  <resultMap id="returnMap" type="com.hl.archive.domain.dto.PoliceTagInfoXjdfReturnDTO">
    <result property="idCardList" column="id_card_list" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler" />
    <result property="ids" column="ids" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
  </resultMap>


  <select id="listTag" resultMap="returnMap">
    select award_date,
           tag_name,
           remark,
           JSON_ARRAYAGG(id_card) AS id_card_list,
           JSON_ARRAYAGG(id)      AS ids
    from police_tag_info_xjdf where is_deleted = 0
    <if test="request.tagType != null and request.tagType != ''">
      and tag_type = #{request.tagType}
    </if>
    <if test="request.idCard != null and request.idCard != ''">
      and id_card = #{request.idCard}
    </if>
    <if test="request.awardDate != null">
      and award_date = #{request.awardDate}
    </if>
    <if test="request.organizationId != null and request.organizationId != ''">
      and organization_id like concat(#{request.organizationId}, '%')
    </if>
    <if test="request.tagName != null and request.tagName != ''">
      and  tag_name like concat('%',#{request.tagName},'%')
    </if>
    group by award_date, tag_name, remark
  </select>
</mapper>