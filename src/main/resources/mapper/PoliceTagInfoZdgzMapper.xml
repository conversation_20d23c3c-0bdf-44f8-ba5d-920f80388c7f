<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceTagInfoZdgzMapper">

  <resultMap id="returnMap" type="com.hl.archive.domain.dto.PoliceTagInfoZdgzReturnDTO">
    <result property="ids" column="ids" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
    <result column="id_card" property="idCard"/>
    <result column="remark" property="remark"/>
    <result column="tag_name_list" property="tagNameList" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
  </resultMap>

  <select id="listTag" resultMap="returnMap">
      select id_card,
             remark,
             JSON_ARRAYAGG(tag_name) tag_name_list,
             JSON_ARRAYAGG(id) AS    ids,
             created_by,
             date(created_at)  as    created_at
      from police_tag_info_zdgz  where is_deleted = 0
      <if test="request.tagName != null and request.tagName != ''">
          and tag_name like concat('%', #{request.tagName}, '%')
      </if>
      <if test="request.organizationId != null and request.organizationId != ''">
          and organization_id like concat(#{request.organizationId}, '%')
      </if>
      <if test="request.idCard != null and request.idCard != ''">
          and id_card = #{request.idCard}
      </if>
      group by id_card, remark, created_by, created_at
  </select>
</mapper>