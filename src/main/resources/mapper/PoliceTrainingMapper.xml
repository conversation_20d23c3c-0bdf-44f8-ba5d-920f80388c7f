<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceTrainingMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceTraining">
    <!--@mbg.generated-->
    <!--@Table police_training-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="training_name" jdbcType="VARCHAR" property="trainingName" />
    <result column="training_start_date" jdbcType="DATE" property="trainingStartDate" />
    <result column="training_end_date" jdbcType="DATE" property="trainingEndDate" />
    <result column="organizer_name" jdbcType="VARCHAR" property="organizerName" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, training_name, training_start_date, training_end_date, organizer_name, 
    is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>