<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceUnitAwardMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceUnitAward">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_unit_award-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="award_name" jdbcType="VARCHAR" property="awardName" />
    <result column="award_time" jdbcType="TIMESTAMP" property="awardTime" />
    <result column="award_organ" jdbcType="VARCHAR" property="awardOrgan" />
    <result column="document_number" jdbcType="VARCHAR" property="documentNumber" />
    <result column="supervisor_name" jdbcType="VARCHAR" property="supervisorName" />
    <result column="supervisor_code" jdbcType="VARCHAR" property="supervisorCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, award_name, award_time, award_organ, document_number, supervisor_name, 
    supervisor_code, unit, created_at, create_by, updated_at, update_by, is_deleted
  </sql>
</mapper>