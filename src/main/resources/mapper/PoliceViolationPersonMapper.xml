<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceViolationPersonMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceViolationPerson">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_violation_person-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="birth_date" jdbcType="DATE" property="birthDate" />
    <result column="case_org" jdbcType="VARCHAR" property="caseOrg" />
    <result column="police_dept" jdbcType="VARCHAR" property="policeDept" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="rank" jdbcType="VARCHAR" property="rank" />
    <result column="political_status" jdbcType="VARCHAR" property="politicalStatus" />
    <result column="join_party_date" jdbcType="DATE" property="joinPartyDate" />
    <result column="is_accountability" jdbcType="TINYINT" property="isAccountability" />
    <result column="disposition_category" jdbcType="VARCHAR" property="dispositionCategory" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, gender, birth_date, case_org, police_dept, `position`, `rank`, political_status, 
    join_party_date, is_accountability, disposition_category, remark, is_deleted, created_by, 
    updated_by, created_at, updated_at
  </sql>
</mapper>