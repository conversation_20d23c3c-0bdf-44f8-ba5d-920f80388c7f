<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceViolationResultMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceViolationResult">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_violation_result-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="xxzjbh" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="wt_xxzjbh" jdbcType="VARCHAR" property="wtXxzjbh" />
    <result column="ry_xxzjbh" jdbcType="VARCHAR" property="ryXxzjbh" />
    <result column="lbmc" jdbcType="VARCHAR" property="lbmc" />
    <result column="clsj" jdbcType="DATE" property="clsj" />
    <result column="cldw" jdbcType="VARCHAR" property="cldw" />
    <result column="cljg" jdbcType="VARCHAR" property="cljg" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, xxzjbh, wt_xxzjbh, ry_xxzjbh, lbmc, clsj, cldw, cljg, is_deleted, created_by, 
    updated_by, created_at, updated_at
  </sql>
</mapper>