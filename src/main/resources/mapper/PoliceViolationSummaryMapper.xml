<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceViolationSummaryMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceViolationSummary">
    <!--@mbg.generated-->
    <!--@Table police_archive.police_violation_summary-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_org" jdbcType="VARCHAR" property="reportOrg" />
    <result column="clue_source" jdbcType="VARCHAR" property="clueSource" />
    <result column="clue_content" jdbcType="LONGVARCHAR" property="clueContent" />
    <result column="found_date" jdbcType="DATE" property="foundDate" />
    <result column="found_org" jdbcType="VARCHAR" property="foundOrg" />
    <result column="accept_date" jdbcType="DATE" property="acceptDate" />
    <result column="accept_org" jdbcType="VARCHAR" property="acceptOrg" />
    <result column="transfer_date" jdbcType="DATE" property="transferDate" />
    <result column="transfer_org" jdbcType="VARCHAR" property="transferOrg" />
    <result column="investigation_start" jdbcType="DATE" property="investigationStart" />
    <result column="investigation_org" jdbcType="VARCHAR" property="investigationOrg" />
    <result column="investigation_result" jdbcType="LONGVARCHAR" property="investigationResult" />
    <result column="preliminary_start" jdbcType="DATE" property="preliminaryStart" />
    <result column="preliminary_org" jdbcType="VARCHAR" property="preliminaryOrg" />
    <result column="preliminary_result" jdbcType="LONGVARCHAR" property="preliminaryResult" />
    <result column="meeting_date" jdbcType="DATE" property="meetingDate" />
    <result column="meeting_org" jdbcType="VARCHAR" property="meetingOrg" />
    <result column="meeting_result" jdbcType="LONGVARCHAR" property="meetingResult" />
    <result column="case_date" jdbcType="DATE" property="caseDate" />
    <result column="case_org" jdbcType="VARCHAR" property="caseOrg" />
    <result column="detention_date" jdbcType="DATE" property="detentionDate" />
    <result column="detention_org" jdbcType="VARCHAR" property="detentionOrg" />
    <result column="violation_fact" jdbcType="LONGVARCHAR" property="violationFact" />
    <result column="violation_type" jdbcType="VARCHAR" property="violationType" />
    <result column="case_no" jdbcType="VARCHAR" property="caseNo" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_org, clue_source, clue_content, found_date, found_org, accept_date, accept_org, 
    transfer_date, transfer_org, investigation_start, investigation_org, investigation_result, 
    preliminary_start, preliminary_org, preliminary_result, meeting_date, meeting_org, 
    meeting_result, case_date, case_org, detention_date, detention_org, violation_fact, 
    violation_type, case_no, is_deleted, created_by, updated_by, created_at, updated_at
  </sql>
</mapper>