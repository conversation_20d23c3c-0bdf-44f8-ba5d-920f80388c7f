<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceWeddingFuneralEventsMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceWeddingFuneralEvents">
    <!--@mbg.generated-->
    <!--@Table police_wedding_funeral_events-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="relationship_with_party" jdbcType="VARCHAR" property="relationshipWithParty" />
    <result column="party_name" jdbcType="VARCHAR" property="partyName" />
    <result column="event_date" jdbcType="DATE" property="eventDate" />
    <result column="participant_count" jdbcType="INTEGER" property="participantCount" />
    <result column="expense_amount" jdbcType="DECIMAL" property="expenseAmount" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, event_type, relationship_with_party, party_name, event_date, participant_count, 
    expense_amount, is_deleted, created_at, updated_at, created_by, updated_by
  </sql>
</mapper>