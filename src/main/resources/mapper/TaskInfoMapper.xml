<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.TaskInfoMapper">

  <select id="queryCcTask" resultType="com.alibaba.fastjson2.JSONObject">
    with cc_info as (  SELECT
                         tr.task_id,-- 将 ccdd 数组拼接成字符串（英文逗号分隔）
                         jt.ccdd_item,-- 出差开始时间
                         SUBSTRING_INDEX(JSON_UNQUOTE(JSON_EXTRACT(tr.content, '$.ccsj')), '_', 1) AS ccsj_start,-- 出差结束时间
                         SUBSTRING_INDEX(JSON_UNQUOTE(JSON_EXTRACT(tr.content, '$.ccsj')), '_', - 1) AS ccsj_end
                       FROM
                         task_info tr, JSON_TABLE(
                               tr.content,
                               '$.ccdd[*]' COLUMNS (
                               ccdd_item VARCHAR(50) PATH '$'
                               )
                                       ) AS jt
                       WHERE
                         config_uuid = 'CG9Z9HJ3FJW' and is_delete = 0 )
    select * from cc_info where ccsj_start is not null and ccsj_end is not null and ccdd_item in
    <foreach collection="request.ccdd" item="item" open="(" separator="," close=")">
      #{item}
    </foreach> and ccsj_start &lt;= #{request.endDate} and ccsj_end &gt;= #{request.startDate}
  </select>
</mapper>