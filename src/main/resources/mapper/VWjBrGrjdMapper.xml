<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrGrjdMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrGrjd">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_GRJD-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_JDDX" jdbcType="VARCHAR" property="xmJddx" />
    <result column="JDLXMC" jdbcType="VARCHAR" property="jdlxmc" />
    <result column="JDYT" jdbcType="VARCHAR" property="jdyt" />
    <result column="JE" jdbcType="VARCHAR" property="je" />
    <result column="HKQX" jdbcType="VARCHAR" property="hkqx" />
    <result column="JDRQ" jdbcType="VARCHAR" property="jdrq" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_JDDX, JDLXMC, JDYT, JE, HKQX, JDRQ
  </sql>
</mapper>