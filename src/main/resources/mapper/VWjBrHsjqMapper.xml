<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrHsjqMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrHsjq">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_HSJQ-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_DSR" jdbcType="VARCHAR" property="xmDsr" />
    <result column="GX_DSR" jdbcType="VARCHAR" property="gxDsr" />
    <result column="CBLXMC" jdbcType="VARCHAR" property="cblxmc" />
    <result column="JE" jdbcType="VARCHAR" property="je" />
    <result column="CYRS" jdbcType="DECIMAL" property="cyrs" />
    <result column="CBRQ" jdbcType="VARCHAR" property="cbrq" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_DSR, GX_DSR, CBLXMC, JE, CYRS, CBRQ
  </sql>
</mapper>