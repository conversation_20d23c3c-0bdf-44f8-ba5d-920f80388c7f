<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrHyzkMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrHyzk">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_HYZK-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="BHSJ" jdbcType="VARCHAR" property="bhsj" />
    <result column="DJSJ" jdbcType="VARCHAR" property="djsj" />
    <result column="HYZTMC" jdbcType="VARCHAR" property="hyztmc" />
    <result column="BHHYZTMC" jdbcType="VARCHAR" property="bhhyztmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, BHSJ, DJSJ, HYZTMC, BHHYZTMC
  </sql>
</mapper>