<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrJkzkMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrJkzk">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_JKZK-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="JBMC" jdbcType="VARCHAR" property="jbmc" />
    <result column="JBMS" jdbcType="VARCHAR" property="jbms" />
    <result column="ZCLZ_PDBZMC" jdbcType="VARCHAR" property="zclzPdbzmc" />
    <result column="ZYSJ" jdbcType="VARCHAR" property="zysj" />
    <result column="ZYJSSJ" jdbcType="VARCHAR" property="zyjssj" />
    <result column="ZYDD" jdbcType="VARCHAR" property="zydd" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, JBMC, JBMS, ZCLZ_PDBZMC, ZYSJ, ZYJSSJ, ZYDD
  </sql>
</mapper>