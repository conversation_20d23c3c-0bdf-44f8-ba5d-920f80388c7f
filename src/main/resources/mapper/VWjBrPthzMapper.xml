<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrPthzMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrPthz">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_PTHZ-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="GLBH" jdbcType="VARCHAR" property="glbh" />
    <result column="HZHM" jdbcType="VARCHAR" property="hzhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="QFRQ" jdbcType="VARCHAR" property="qfrq" />
    <result column="YXQZ" jdbcType="VARCHAR" property="yxqz" />
    <result column="BGJGMC" jdbcType="VARCHAR" property="bgjgmc" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, GLBH, HZHM, ND, QFRQ, YXQZ, BGJGMC, BZ
  </sql>
</mapper>