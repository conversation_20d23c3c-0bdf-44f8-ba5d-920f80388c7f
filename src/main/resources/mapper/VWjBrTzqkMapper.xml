<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrTzqkMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrTzqk">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_TZQK-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="TZCPMC" jdbcType="VARCHAR" property="tzcpmc" />
    <result column="JE" jdbcType="VARCHAR" property="je" />
    <result column="TZSJ" jdbcType="VARCHAR" property="tzsj" />
    <result column="TZQX" jdbcType="VARCHAR" property="tzqx" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, TZCPMC, JE, TZSJ, TZQX
  </sql>
</mapper>