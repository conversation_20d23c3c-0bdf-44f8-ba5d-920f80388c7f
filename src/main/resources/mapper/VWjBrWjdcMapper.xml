<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrWjdcMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrWjdc">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_WJDC-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="DCRQ" jdbcType="VARCHAR" property="dcrq" />
    <result column="DCDD" jdbcType="VARCHAR" property="dcdd" />
    <result column="DCJG" jdbcType="VARCHAR" property="dcjg" />
    <result column="WFQK" jdbcType="VARCHAR" property="wfqk" />
    <result column="JTQK" jdbcType="VARCHAR" property="jtqk" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, DCRQ, DCDD, DCJG, WFQK, JTQK
  </sql>
</mapper>