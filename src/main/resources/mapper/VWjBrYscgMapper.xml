<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBrYscgMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBrYscg">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BR_YSCG-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="HZHM" jdbcType="VARCHAR" property="hzhm" />
    <result column="KSSJ" jdbcType="VARCHAR" property="kssj" />
    <result column="JSSJ" jdbcType="VARCHAR" property="jssj" />
    <result column="SDGJ" jdbcType="VARCHAR" property="sdgj" />
    <result column="SY" jdbcType="VARCHAR" property="sy" />
    <result column="SPJGMC" jdbcType="VARCHAR" property="spjgmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, HZHM, KSSJ, JSSJ, SDGJ, SY, SPJGMC
  </sql>
</mapper>