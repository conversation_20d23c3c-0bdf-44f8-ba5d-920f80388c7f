<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBzjlDwryMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBzjlDwry">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BZJL_DWRY-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="JLMC" jdbcType="VARCHAR" property="jlmc" />
    <result column="BZSJ" jdbcType="VARCHAR" property="bzsj" />
    <result column="JLJGMC" jdbcType="VARCHAR" property="jljgmc" />
    <result column="BZWH" jdbcType="VARCHAR" property="bzwh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="DWBM" jdbcType="VARCHAR" property="dwbm" />
    <result column="DWDM" jdbcType="VARCHAR" property="dwdm" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, JLMC, BZSJ, JLJGMC, BZWH, XM, JH, DWBM, DWDM, DWMC
  </sql>
</mapper>