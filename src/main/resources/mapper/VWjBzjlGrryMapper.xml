<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBzjlGrryMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBzjlGrry">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_BZJL_GRRY-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="XBMC" jdbcType="VARCHAR" property="xbmc" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="JLMC" jdbcType="VARCHAR" property="jlmc" />
    <result column="BZSJ" jdbcType="VARCHAR" property="bzsj" />
    <result column="JLJGMC" jdbcType="VARCHAR" property="jljgmc" />
    <result column="BZWH" jdbcType="VARCHAR" property="bzwh" />
    <result column="RYJBMC" jdbcType="VARCHAR" property="ryjbmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, XM, XBMC, JH, GMSFHM, DWMC, JLMC, BZSJ, JLJGMC, BZWH, RYJBMC
  </sql>
</mapper>