<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjBzjlOldMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjBzjlOld">
    <!--@mbg.generated-->
    <!--@Table FJXX.V_WJ_BZJL_OLD-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSONID" jdbcType="VARCHAR" property="personid" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="JCMC" jdbcType="VARCHAR" property="jcmc" />
    <result column="JCLB" jdbcType="VARCHAR" property="jclb" />
    <result column="JCDJ" jdbcType="VARCHAR" property="jcdj" />
    <result column="JCSJ" jdbcType="VARCHAR" property="jcsj" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="JCBH" jdbcType="VARCHAR" property="jcbh" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSONID, GMSFHM, JCMC, JCLB, JCDJ, JCSJ, DWMC, JCBH
  </sql>
</mapper>