<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjFxyhMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjFxyh">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_FXYH-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="YF" jdbcType="VARCHAR" property="yf" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="ZWMC" jdbcType="VARCHAR" property="zwmc" />
    <result column="XB" jdbcType="VARCHAR" property="xb" />
    <result column="CSRQ" jdbcType="VARCHAR" property="csrq" />
    <result column="GZRQ" jdbcType="VARCHAR" property="gzrq" />
    <result column="ZZMMMC" jdbcType="VARCHAR" property="zzmmmc" />
    <result column="CJDPSJ" jdbcType="VARCHAR" property="cjdpsj" />
    <result column="ZWCCMC1" jdbcType="VARCHAR" property="zwccmc1" />
    <result column="ZWCCMC2" jdbcType="VARCHAR" property="zwccmc2" />
    <result column="ZJMC" jdbcType="VARCHAR" property="zjmc" />
    <result column="JZGWMC" jdbcType="VARCHAR" property="jzgwmc" />
    <result column="FXZK_XXQK" jdbcType="VARCHAR" property="fxzkXxqk" />
    <result column="FXLX" jdbcType="VARCHAR" property="fxlx" />
    <result column="FXJBMC" jdbcType="VARCHAR" property="fxjbmc" />
    <result column="GACS" jdbcType="VARCHAR" property="gacs" />
    <result column="GAJL" jdbcType="VARCHAR" property="gajl" />
    <result column="DYBH" jdbcType="VARCHAR" property="dybh" />
    <result column="CFSJ" jdbcType="VARCHAR" property="cfsj" />
    <result column="CFFLMC" jdbcType="VARCHAR" property="cfflmc" />
    <result column="CFLBMC" jdbcType="VARCHAR" property="cflbmc" />
    <result column="ZRLDXM" jdbcType="VARCHAR" property="zrldxm" />
    <result column="ZGZGXM" jdbcType="VARCHAR" property="zgzgxm" />
    <result column="FGLDXM" jdbcType="VARCHAR" property="fgldxm" />
    <result column="DZMC" jdbcType="VARCHAR" property="dzmc" />
    <result column="SJHM" jdbcType="VARCHAR" property="sjhm" />
    <result column="JTCY_XXQK" jdbcType="VARCHAR" property="jtcyXxqk" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, XM, ND, YF, JH, GMSFHM, DWMC, ZWMC, XB, CSRQ, GZRQ, ZZMMMC, CJDPSJ, ZWCCMC1, 
    ZWCCMC2, ZJMC, JZGWMC, FXZK_XXQK, FXLX, FXJBMC, GACS, GAJL, DYBH, CFSJ, CFFLMC, CFLBMC, 
    ZRLDXM, ZGZGXM, FGLDXM, DZMC, SJHM, JTCY_XXQK
  </sql>
</mapper>