<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjJcsjScsbMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjJcsjScsb">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_JCSJ_SCSB-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="BSDW" jdbcType="VARCHAR" property="bsdw" />
    <result column="BSDWMC" jdbcType="VARCHAR" property="bsdwmc" />
    <result column="BSR" jdbcType="VARCHAR" property="bsr" />
    <result column="BSRXM" jdbcType="VARCHAR" property="bsrxm" />
    <result column="CJMJ" jdbcType="VARCHAR" property="cjmj" />
    <result column="CLLX" jdbcType="VARCHAR" property="cllx" />
    <result column="CLLXMC" jdbcType="VARCHAR" property="cllxmc" />
    <result column="BSLX" jdbcType="VARCHAR" property="bslx" />
    <result column="BSLXMC" jdbcType="VARCHAR" property="bslxmc" />
    <result column="DJSJ" jdbcType="VARCHAR" property="djsj" />
    <result column="SCJJ" jdbcType="VARCHAR" property="scjj" />
    <result column="SHJG" jdbcType="VARCHAR" property="shjg" />
    <result column="SHJGMC" jdbcType="VARCHAR" property="shjgmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, BSDW, BSDWMC, BSR, BSRXM, CJMJ, CLLX, CLLXMC, BSLX, BSLXMC, DJSJ, SCJJ, SHJG, 
    SHJGMC
  </sql>
</mapper>