<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjJcsjScsbWjMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjJcsjScsbWj">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_JCSJ_SCSB_WJ-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="SB_XXZJBH" jdbcType="VARCHAR" property="sbXxzjbh" />
    <result column="WJLX" jdbcType="VARCHAR" property="wjlx" />
    <result column="WJLXMC" jdbcType="VARCHAR" property="wjlxmc" />
    <result column="WJMC" jdbcType="VARCHAR" property="wjmc" />
    <result column="FJCL2" jdbcType="VARCHAR" property="fjcl2" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, SB_XXZJBH, WJLX, WJLXMC, WJMC, FJCL2
  </sql>
</mapper>