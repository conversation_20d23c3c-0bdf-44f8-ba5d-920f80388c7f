<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjLzfxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjLzfx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_LZFX-->
    <result column="DWBM" jdbcType="VARCHAR" property="dwbm" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="CZD01" jdbcType="VARCHAR" property="czd01" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="YF" jdbcType="VARCHAR" property="yf" />
    <result column="FXDID" jdbcType="VARCHAR" property="fxdid" />
    <result column="FXDMC" jdbcType="VARCHAR" property="fxdmc" />
    <result column="FXID" jdbcType="VARCHAR" property="fxid" />
    <result column="FXMC" jdbcType="VARCHAR" property="fxmc" />
    <result column="FKCS" jdbcType="VARCHAR" property="fkcs" />
    <result column="JTLSQK" jdbcType="VARCHAR" property="jtlsqk" />
    <result column="FXJB" jdbcType="VARCHAR" property="fxjb" />
    <result column="SJYJ" jdbcType="VARCHAR" property="sjyj" />
    <result column="FXJBMC" jdbcType="VARCHAR" property="fxjbmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DWBM, DWMC, CZD01, ND, YF, FXDID, FXDMC, FXID, FXMC, FKCS, JTLSQK, FXJB, SJYJ, FXJBMC
  </sql>
</mapper>