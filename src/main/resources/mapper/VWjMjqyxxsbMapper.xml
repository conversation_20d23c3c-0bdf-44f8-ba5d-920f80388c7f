<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjMjqyxxsbMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjMjqyxxsb">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_MJQYXXSB-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="GZDW_GAJGMC" jdbcType="VARCHAR" property="gzdwGajgmc" />
    <result column="ZWMC" jdbcType="VARCHAR" property="zwmc" />
    <result column="SQLBMC" jdbcType="VARCHAR" property="sqlbmc" />
    <result column="HCRDZTMC" jdbcType="VARCHAR" property="hcrdztmc" />
    <result column="SQLY" jdbcType="VARCHAR" property="sqly" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, XM, JH, GMSFHM, GZDW_GAJGMC, ZWMC, SQLBMC, HCRDZTMC, SQLY
  </sql>
</mapper>