<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjNlcpMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjNlcp">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_NLCP-->
    <result column="LCID" jdbcType="VARCHAR" property="lcid" />
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="SQRXM" jdbcType="VARCHAR" property="sqrxm" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="ZWMC" jdbcType="VARCHAR" property="zwmc" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="FAMC" jdbcType="VARCHAR" property="famc" />
    <result column="DLMC" jdbcType="VARCHAR" property="dlmc" />
    <result column="BQ_MC" jdbcType="VARCHAR" property="bqMc" />
    <result column="SHRXM" jdbcType="VARCHAR" property="shrxm" />
    <result column="FSZT" jdbcType="VARCHAR" property="fszt" />
    <result column="BQZ_MC" jdbcType="VARCHAR" property="bqzMc" />
    <result column="SHJGMC" jdbcType="VARCHAR" property="shjgmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LCID, XXZJBH, SQRXM, JH, ZWMC, DWMC, FAMC, DLMC, BQ_MC, SHRXM, FSZT, BQZ_MC, SHJGMC
  </sql>
</mapper>