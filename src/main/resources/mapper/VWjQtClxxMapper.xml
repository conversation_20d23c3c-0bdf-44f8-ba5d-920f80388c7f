<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjQtClxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjQtClxx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_QT_CLXX-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_CQR" jdbcType="VARCHAR" property="xmCqr" />
    <result column="CLPP" jdbcType="VARCHAR" property="clpp" />
    <result column="HPHM" jdbcType="VARCHAR" property="hphm" />
    <result column="JE" jdbcType="VARCHAR" property="je" />
    <result column="GMSJ" jdbcType="VARCHAR" property="gmsj" />
    <result column="CLLYMC" jdbcType="VARCHAR" property="cllymc" />
    <result column="CLQXMC" jdbcType="VARCHAR" property="clqxmc" />
    <result column="CSSJ" jdbcType="VARCHAR" property="cssj" />
    <result column="CSJG" jdbcType="VARCHAR" property="csjg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_CQR, CLPP, HPHM, JE, GMSJ, CLLYMC, CLQXMC, CSSJ, CSJG
  </sql>
</mapper>