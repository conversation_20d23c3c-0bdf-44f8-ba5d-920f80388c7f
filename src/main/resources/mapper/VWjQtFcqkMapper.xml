<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjQtFcqkMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjQtFcqk">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_QT_FCQK-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_CQR" jdbcType="VARCHAR" property="xmCqr" />
    <result column="FCLY" jdbcType="VARCHAR" property="fcly" />
    <result column="DZ" jdbcType="VARCHAR" property="dz" />
    <result column="JZMJ" jdbcType="VARCHAR" property="jzmj" />
    <result column="FCLX" jdbcType="VARCHAR" property="fclx" />
    <result column="FCQX" jdbcType="VARCHAR" property="fcqx" />
    <result column="JYSJ" jdbcType="VARCHAR" property="jysj" />
    <result column="JYJG" jdbcType="VARCHAR" property="jyjg" />
    <result column="FCLYMC" jdbcType="VARCHAR" property="fclymc" />
    <result column="FCLXMC" jdbcType="VARCHAR" property="fclxmc" />
    <result column="FCQXMC" jdbcType="VARCHAR" property="fcqxmc" />
    <result column="CSSJ" jdbcType="VARCHAR" property="cssj" />
    <result column="CSJG" jdbcType="VARCHAR" property="csjg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_CQR, FCLY, DZ, JZMJ, FCLX, FCQX, JYSJ, JYJG, FCLYMC, FCLXMC, FCQXMC, 
    CSSJ, CSJG
  </sql>
</mapper>