<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjQtGpjjtzMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjQtGpjjtz">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_QT_GPJJTZ-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="XM_CYR" jdbcType="VARCHAR" property="xmCyr" />
    <result column="MCDM" jdbcType="VARCHAR" property="mcdm" />
    <result column="SL" jdbcType="VARCHAR" property="sl" />
    <result column="RJZ" jdbcType="VARCHAR" property="rjz" />
    <result column="FCQX" jdbcType="VARCHAR" property="fcqx" />
    <result column="CSSJ" jdbcType="VARCHAR" property="cssj" />
    <result column="CSJG" jdbcType="VARCHAR" property="csjg" />
    <result column="FCQXMC" jdbcType="VARCHAR" property="fcqxmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, XM_CYR, MCDM, SL, RJZ, FCQX, CSSJ, CSJG, FCQXMC
  </sql>
</mapper>