<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjQtPosxbzxrMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjQtPosxbzxr">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_QT_POSXBZXR-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="RYGX" jdbcType="VARCHAR" property="rygx" />
    <result column="RYGXMC" jdbcType="VARCHAR" property="rygxmc" />
    <result column="ZXJG" jdbcType="VARCHAR" property="zxjg" />
    <result column="JTQK" jdbcType="VARCHAR" property="jtqk" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, RYGX, RYGXMC, ZXJG, JTQK
  </sql>
</mapper>