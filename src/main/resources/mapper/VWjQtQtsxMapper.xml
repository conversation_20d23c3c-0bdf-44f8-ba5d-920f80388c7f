<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjQtQtsxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjQtQtsx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_QT_QTSX-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
    <result column="DJRQ" jdbcType="VARCHAR" property="djrq" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, BZ, DJRQ
  </sql>
</mapper>