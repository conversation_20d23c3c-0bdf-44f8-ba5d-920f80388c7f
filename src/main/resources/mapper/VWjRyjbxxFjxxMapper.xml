<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjbxxFjxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjbxxFjxx">
    <!--@mbg.generated-->
    <!--@Table FJXX.V_WJ_RYJBXX-->
    <result column="PERSONID" jdbcType="VARCHAR" property="personid" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="CYM" jdbcType="VARCHAR" property="cym" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="XB" jdbcType="VARCHAR" property="xb" />
    <result column="CJMC" jdbcType="VARCHAR" property="cjmc" />
    <result column="CSRQ" jdbcType="VARCHAR" property="csrq" />
    <result column="RZZTMC" jdbcType="VARCHAR" property="rzztmc" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="FJGZRQ" jdbcType="VARCHAR" property="fjgzrq" />
    <result column="NL" jdbcType="DECIMAL" property="nl" />
    <result column="GWMC" jdbcType="VARCHAR" property="gwmc" />
    <result column="XL" jdbcType="VARCHAR" property="xl" />
    <result column="HYZK" jdbcType="VARCHAR" property="hyzk" />
    <result column="ZZMM" jdbcType="VARCHAR" property="zzmm" />
    <result column="RDSJ" jdbcType="VARCHAR" property="rdsj" />
    <result column="SCSJ" jdbcType="VARCHAR" property="scsj" />
    <result column="SCGZSJ" jdbcType="VARCHAR" property="scgzsj" />
    <result column="JGMC" jdbcType="VARCHAR" property="jgmc" />
    <result column="HJDZ" jdbcType="VARCHAR" property="hjdz" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PERSONID, XM, CYM, DWMC, XB, CJMC, CSRQ, RZZTMC, GMSFHM, FJGZRQ, NL, GWMC, XL, HYZK, ZZMM, RDSJ, SCSJ, SCGZSJ, JGMC, HJDZ
  </sql>
</mapper>
