<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjbxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjbxx">
    <!--@mbg.generated-->
    <!--@Table FJXX.V_WJ_RYJBXX-->
    <result column="PERSONID" jdbcType="VARCHAR" property="personid" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="CYM" jdbcType="VARCHAR" property="cym" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="XB" jdbcType="VARCHAR" property="xb" />
    <result column="CJMC" jdbcType="VARCHAR" property="cjmc" />
    <result column="CSRQ" jdbcType="VARCHAR" property="csrq" />
    <result column="RZZTMC" jdbcType="VARCHAR" property="rzztmc" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="FJGZRQ" jdbcType="VARCHAR" property="fjgzrq" />
    <result column="NL" jdbcType="DECIMAL" property="nl" />
    <result column="GWMC" jdbcType="VARCHAR" property="gwmc" />
    <result column="XL" jdbcType="VARCHAR" property="xl" />
    <result column="HYZK" jdbcType="VARCHAR" property="hyzk" />
    <result column="ZZMM" jdbcType="VARCHAR" property="zzmm" />
    <result column="RDSJ" jdbcType="VARCHAR" property="rdsj" />
    <result column="SCSJ" jdbcType="VARCHAR" property="scsj" />
    <result column="SCGZSJ" jdbcType="VARCHAR" property="scgzsj" />
    <result column="JGMC" jdbcType="VARCHAR" property="jgmc" />
    <result column="HJDZ" jdbcType="VARCHAR" property="hjdz" />
    <result column="GH" jdbcType="VARCHAR" property="gh" />
    <result column="JZD" jdbcType="VARCHAR" property="jzd" />
    <result column="LWGS" jdbcType="VARCHAR" property="lwgs" />
    <result column="BZQDMC" jdbcType="VARCHAR" property="bzqdmc" />
    <result column="SJHM" jdbcType="VARCHAR" property="sjhm" />
    <result column="ZC" jdbcType="VARCHAR" property="zc" />
    <result column="ZJ_NX" jdbcType="VARCHAR" property="zjNx" />
    <result column="ZJ_WX" jdbcType="VARCHAR" property="zjWx" />
    <result column="XXMC" jdbcType="VARCHAR" property="xxmc" />
    <result column="FZGL" jdbcType="DECIMAL" property="fzgl" />
    <result column="ZRLDJH" jdbcType="VARCHAR" property="zrldjh" />
    <result column="ZRLDXM" jdbcType="VARCHAR" property="zrldxm" />
    <result column="DFMJJH" jdbcType="VARCHAR" property="dfmjjh" />
    <result column="DFMJXM" jdbcType="VARCHAR" property="dfmjxm" />
    <result column="JKZKMC" jdbcType="VARCHAR" property="jkzkmc" />
    <result column="ZDBS" jdbcType="VARCHAR" property="zdbs" />
    <result column="SFBY" jdbcType="VARCHAR" property="sfby" />
    <result column="JZ" jdbcType="VARCHAR" property="jz" />
    <result column="ZYTC" jdbcType="VARCHAR" property="zytc" />
    <result column="MZ" jdbcType="VARCHAR" property="mz" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PERSONID, XM, CYM, DWMC, XB, CJMC, CSRQ, RZZTMC, GMSFHM, FJGZRQ, NL, GWMC, XL, HYZK, 
    ZZMM, RDSJ, SCSJ, SCGZSJ, JGMC, HJDZ, GH, JZD, LWGS, BZQDMC, SJHM, ZC, ZJ_NX, ZJ_WX, 
    XXMC, FZGL, ZRLDJH, ZRLDXM, DFMJJH, DFMJXM, JKZKMC, ZDBS, SFBY, JZ, ZYTC, MZ
  </sql>
</mapper>