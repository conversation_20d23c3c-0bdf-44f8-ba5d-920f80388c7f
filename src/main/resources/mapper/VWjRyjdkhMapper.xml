<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjdkhMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjdkh">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJDKH-->
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="JD" jdbcType="VARCHAR" property="jd" />
    <result column="JGMC" jdbcType="VARCHAR" property="jgmc" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DWMC, ND, JD, JGMC, RKBM, GMSFHM
  </sql>
</mapper>