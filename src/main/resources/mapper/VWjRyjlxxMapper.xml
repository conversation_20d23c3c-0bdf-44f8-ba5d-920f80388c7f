<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjlxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjlxx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJLXX-->
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="QSSJ" jdbcType="VARCHAR" property="qssj" />
    <result column="JZSJ" jdbcType="VARCHAR" property="jzsj" />
    <result column="SZDW" jdbcType="VARCHAR" property="szdw" />
    <result column="ZW" jdbcType="VARCHAR" property="zw" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RKBM, QSSJ, JZSJ, SZDW, ZW, GMSFHM
  </sql>
</mapper>