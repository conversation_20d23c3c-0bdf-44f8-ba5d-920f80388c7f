<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjtcyMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjtcy">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJTCY-->
    <result column="RYGX" jdbcType="VARCHAR" property="rygx" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="CSRQ" jdbcType="VARCHAR" property="csrq" />
    <result column="GZDW" jdbcType="VARCHAR" property="gzdw" />
    <result column="ZZMM" jdbcType="VARCHAR" property="zzmm" />
    <result column="JTCYBH" jdbcType="VARCHAR" property="jtcybh" />
    <result column="SJHM" jdbcType="VARCHAR" property="sjhm" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RYGX, XM, CSRQ, GZDW, ZZMM, JTCYBH, SJHM, RKBM, GMSFHM
  </sql>
</mapper>