<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjtzzMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjtzz">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJTZZ-->
    <result column="JTDZ" jdbcType="VARCHAR" property="jtdz" />
    <result column="JTDH" jdbcType="VARCHAR" property="jtdh" />
    <result column="SHHM" jdbcType="VARCHAR" property="shhm" />
    <result column="SHHMDH" jdbcType="VARCHAR" property="shhmdh" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    JTDZ, JTDH, SHHM, SHHMDH, RKBM, GMSFHM
  </sql>
</mapper>