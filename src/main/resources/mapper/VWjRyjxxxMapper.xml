<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjxxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjxxx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJXXX-->
    <result column="XC" jdbcType="VARCHAR" property="xc" />
    <result column="SXSJ" jdbcType="VARCHAR" property="sxsj" />
    <result column="SXYY" jdbcType="VARCHAR" property="sxyy" />
    <result column="SXZL" jdbcType="VARCHAR" property="sxzl" />
    <result column="SXSXZZJ" jdbcType="VARCHAR" property="sxsxzzj" />
    <result column="XCZZRQ" jdbcType="VARCHAR" property="xczzrq" />
    <result column="XCQSRQ" jdbcType="VARCHAR" property="xcqsrq" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XC, SXSJ, SXYY, SXZL, SXSXZZJ, XCZZRQ, XCQSRQ, RKBM, GMSFHM
  </sql>
</mapper>