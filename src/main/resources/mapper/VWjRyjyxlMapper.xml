<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjyxlMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjyxl">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJYXL-->
    <result column="PXBMC" jdbcType="VARCHAR" property="pxbmc" />
    <result column="PXQZSJ" jdbcType="VARCHAR" property="pxqzsj" />
    <result column="PXZZSJ" jdbcType="VARCHAR" property="pxzzsj" />
    <result column="PXZBDWMC" jdbcType="VARCHAR" property="pxzbdwmc" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PXBMC, PXQZSJ, PXZZSJ, PXZBDWMC, RKBM, GMSFHM
  </sql>
</mapper>