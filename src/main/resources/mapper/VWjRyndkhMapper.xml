<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyndkhMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyndkh">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYNDKH-->
    <result column="KCLB" jdbcType="VARCHAR" property="kclb" />
    <result column="KHJG" jdbcType="VARCHAR" property="khjg" />
    <result column="KCND" jdbcType="VARCHAR" property="kcnd" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KCLB, KHJG, KCND, RKBM, GMSFHM
  </sql>
</mapper>