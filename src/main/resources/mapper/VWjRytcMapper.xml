<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRytcMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRytc">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYTC-->
    <result column="JCMC" jdbcType="VARCHAR" property="jcmc" />
    <result column="JCSJ" jdbcType="VARCHAR" property="jcsj" />
    <result column="JCPJJGMC" jdbcType="VARCHAR" property="jcpjjgmc" />
    <result column="BH" jdbcType="VARCHAR" property="bh" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    JCMC, JCSJ, JCPJJGMC, BH, RKBM, GMSFHM
  </sql>
</mapper>