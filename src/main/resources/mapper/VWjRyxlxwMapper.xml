<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyxlxwMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyxlxw">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYXLXW-->
    <result column="XL" jdbcType="VARCHAR" property="xl" />
    <result column="XXMC" jdbcType="VARCHAR" property="xxmc" />
    <result column="ZYMC" jdbcType="VARCHAR" property="zymc" />
    <result column="RXSJ" jdbcType="VARCHAR" property="rxsj" />
    <result column="BYSJ" jdbcType="VARCHAR" property="bysj" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XL, XXMC, ZYMC, RXSJ, BYSJ, RKBM, GMSFHM
  </sql>
</mapper>