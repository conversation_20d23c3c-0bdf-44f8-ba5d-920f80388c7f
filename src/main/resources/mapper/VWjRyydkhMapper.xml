<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyydkhMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyydkh">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYYDKH-->
    <result column="KPTBPZMC" jdbcType="VARCHAR" property="kptbpzmc" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="YF" jdbcType="VARCHAR" property="yf" />
    <result column="DF" jdbcType="DECIMAL" property="df" />
    <result column="JJ" jdbcType="DECIMAL" property="jj" />
    <result column="PM" jdbcType="DECIMAL" property="pm" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KPTBPZMC, ND, YF, DF, JJ, PM, RKBM, GMSFHM
  </sql>
</mapper>