<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyzwzjMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyzwzj">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYZWZJ-->
    <result column="ZWMC" jdbcType="VARCHAR" property="zwmc" />
    <result column="GAZWJB" jdbcType="VARCHAR" property="gazwjb" />
    <result column="ZWSXSJ" jdbcType="VARCHAR" property="zwsxsj" />
    <result column="XZJSJ" jdbcType="VARCHAR" property="xzjsj" />
    <result column="RZWH" jdbcType="VARCHAR" property="rzwh" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ZWMC, GAZWJB, ZWSXSJ, XZJSJ, RZWH, RKBM, GMSFHM
  </sql>
</mapper>