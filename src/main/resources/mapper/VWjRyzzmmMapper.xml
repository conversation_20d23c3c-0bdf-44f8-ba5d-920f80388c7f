<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyzzmmMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyzzmm">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYZZMM-->
    <result column="ZZSF" jdbcType="VARCHAR" property="zzsf" />
    <result column="CJDPSJ" jdbcType="VARCHAR" property="cjdpsj" />
    <result column="RKBM" jdbcType="VARCHAR" property="rkbm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ZZSF, CJDPSJ, RKBM, GMSFHM
  </sql>
</mapper>