<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjWgwjdjbMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjWgwjdjb">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_WGWJDJB-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="DJDWMC" jdbcType="VARCHAR" property="djdwmc" />
    <result column="WTXSLYMC" jdbcType="VARCHAR" property="wtxslymc" />
    <result column="WTXSNR" jdbcType="VARCHAR" property="wtxsnr" />
    <result column="FXSJ" jdbcType="VARCHAR" property="fxsj" />
    <result column="FXDW" jdbcType="VARCHAR" property="fxdw" />
    <result column="SLSJ" jdbcType="VARCHAR" property="slsj" />
    <result column="SLDW" jdbcType="VARCHAR" property="sldw" />
    <result column="YJSJ" jdbcType="VARCHAR" property="yjsj" />
    <result column="YJDW" jdbcType="VARCHAR" property="yjdw" />
    <result column="DCSJ" jdbcType="VARCHAR" property="dcsj" />
    <result column="DCDW" jdbcType="VARCHAR" property="dcdw" />
    <result column="DCQK" jdbcType="VARCHAR" property="dcqk" />
    <result column="CHSJ" jdbcType="VARCHAR" property="chsj" />
    <result column="CHDW" jdbcType="VARCHAR" property="chdw" />
    <result column="CHQK" jdbcType="VARCHAR" property="chqk" />
    <result column="HSSJ" jdbcType="VARCHAR" property="hssj" />
    <result column="HSDW" jdbcType="VARCHAR" property="hsdw" />
    <result column="HSJG" jdbcType="VARCHAR" property="hsjg" />
    <result column="LASJ" jdbcType="VARCHAR" property="lasj" />
    <result column="LADW" jdbcType="VARCHAR" property="ladw" />
    <result column="LZSCSJ" jdbcType="VARCHAR" property="lzscsj" />
    <result column="LZSCDW" jdbcType="VARCHAR" property="lzscdw" />
    <result column="WJWFSS" jdbcType="VARCHAR" property="wjwfss" />
    <result column="WJWFLXMC" jdbcType="VARCHAR" property="wjwflxmc" />
    <result column="AJBH" jdbcType="VARCHAR" property="ajbh" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, DJDWMC, WTXSLYMC, WTXSNR, FXSJ, FXDW, SLSJ, SLDW, YJSJ, YJDW, DCSJ, DCDW, 
    DCQK, CHSJ, CHDW, CHQK, HSSJ, HSDW, HSJG, LASJ, LADW, LZSCSJ, LZSCDW, WJWFSS, WJWFLXMC, 
    AJBH
  </sql>
</mapper>