<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjWgwjdjbRyclMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjWgwjdjbRycl">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_WGWJDJB_RYCL-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="WT_XXZJBH" jdbcType="VARCHAR" property="wtXxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="XBMC" jdbcType="VARCHAR" property="xbmc" />
    <result column="CSRQ" jdbcType="VARCHAR" property="csrq" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="JZBMMC" jdbcType="VARCHAR" property="jzbmmc" />
    <result column="ZWMC" jdbcType="VARCHAR" property="zwmc" />
    <result column="ZJMC" jdbcType="VARCHAR" property="zjmc" />
    <result column="ZZMMMC" jdbcType="VARCHAR" property="zzmmmc" />
    <result column="RDRQ" jdbcType="VARCHAR" property="rdrq" />
    <result column="SFDCWZMC" jdbcType="VARCHAR" property="sfdcwzmc" />
    <result column="CLXTMC" jdbcType="VARCHAR" property="clxtmc" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, WT_XXZJBH, XM, XBMC, CSRQ, DWMC, JZBMMC, ZWMC, ZJMC, ZZMMMC, RDRQ, SFDCWZMC, 
    CLXTMC, BZ
  </sql>
</mapper>