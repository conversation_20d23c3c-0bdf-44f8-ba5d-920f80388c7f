<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjWgwjdjbRycljgMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjWgwjdjbRycljg">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_WGWJDJB_RYCLJG-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="WT_XXZJBH" jdbcType="VARCHAR" property="wtXxzjbh" />
    <result column="RY_XXZJBH" jdbcType="VARCHAR" property="ryXxzjbh" />
    <result column="LBMC" jdbcType="VARCHAR" property="lbmc" />
    <result column="CLSJ" jdbcType="VARCHAR" property="clsj" />
    <result column="CLDW" jdbcType="VARCHAR" property="cldw" />
    <result column="CLJG" jdbcType="VARCHAR" property="cljg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, WT_XXZJBH, RY_XXZJBH, LBMC, CLSJ, CLDW, CLJG
  </sql>
</mapper>