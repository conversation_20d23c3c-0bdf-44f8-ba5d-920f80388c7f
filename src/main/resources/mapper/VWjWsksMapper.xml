<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjWsksMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjWsks">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_WSKS-->
    <result column="SJMC" jdbcType="VARCHAR" property="sjmc" />
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="KSSJ" jdbcType="VARCHAR" property="kssj" />
    <result column="JSSJ" jdbcType="VARCHAR" property="jssj" />
    <result column="KSCS" jdbcType="DECIMAL" property="kscs" />
    <result column="KSSC" jdbcType="DECIMAL" property="kssc" />
    <result column="TMGS" jdbcType="DECIMAL" property="tmgs" />
    <result column="SFJJ" jdbcType="VARCHAR" property="sfjj" />
    <result column="DF" jdbcType="DECIMAL" property="df" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SJMC, XXZJBH, XM, GMSFHM, KSSJ, JSSJ, KSCS, KSSC, TMGS, SFJJ, DF
  </sql>
</mapper>