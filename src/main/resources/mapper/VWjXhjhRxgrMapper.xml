<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjXhjhRxgrMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjXhjhRxgr">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_XHJH_RXGR-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="RYJBDM" jdbcType="VARCHAR" property="ryjbdm" />
    <result column="RYJBMC" jdbcType="VARCHAR" property="ryjbmc" />
    <result column="ZGRYMC" jdbcType="VARCHAR" property="zgrymc" />
    <result column="DJSJ" jdbcType="VARCHAR" property="djsj" />
    <result column="GXSJ" jdbcType="VARCHAR" property="gxsj" />
    <result column="PYLXR_XM" jdbcType="VARCHAR" property="pylxrXm" />
    <result column="SHZT" jdbcType="VARCHAR" property="shzt" />
    <result column="SHZTMC" jdbcType="VARCHAR" property="shztmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, XM, GMSFHM, JH, DWMC, RYJBDM, RYJBMC, ZGRYMC, DJSJ, GXSJ, PYLXR_XM, SHZT, 
    SHZTMC
  </sql>
</mapper>