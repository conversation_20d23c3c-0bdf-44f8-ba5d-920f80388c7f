<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjXhjhRxgrPylxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjXhjhRxgrPylx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_XHJH_RXGR_PYLX-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="JL_XXZJBH" jdbcType="VARCHAR" property="jlXxzjbh" />
    <result column="DJSJ" jdbcType="VARCHAR" property="djsj" />
    <result column="DJRXM" jdbcType="VARCHAR" property="djrxm" />
    <result column="PYLXR_XM" jdbcType="VARCHAR" property="pylxrXm" />
    <result column="PYLXR_JH" jdbcType="VARCHAR" property="pylxrJh" />
    <result column="PYLXR_ZW" jdbcType="VARCHAR" property="pylxrZw" />
    <result column="TZLD" jdbcType="VARCHAR" property="tzld" />
    <result column="PYPJ" jdbcType="VARCHAR" property="pypj" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, JL_XXZJBH, DJSJ, DJRXM, PYLXR_XM, PYLXR_JH, PYLXR_ZW, TZLD, PYPJ
  </sql>
</mapper>