<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjXhjhRxgrTwzlMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjXhjhRxgrTwzl">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_XHJH_RXGR_TWZL-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="JL_XXZJBH" jdbcType="VARCHAR" property="jlXxzjbh" />
    <result column="DJSJ" jdbcType="VARCHAR" property="djsj" />
    <result column="DJRXM" jdbcType="VARCHAR" property="djrxm" />
    <result column="TPZL_BT" jdbcType="VARCHAR" property="tpzlBt" />
    <result column="TPZL_NR" jdbcType="VARCHAR" property="tpzlNr" />
    <result column="TPZL_WJMC" jdbcType="VARCHAR" property="tpzlWjmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, JL_XXZJBH, DJSJ, DJRXM, TPZL_BT, TPZL_NR, TPZL_WJMC
  </sql>
</mapper>