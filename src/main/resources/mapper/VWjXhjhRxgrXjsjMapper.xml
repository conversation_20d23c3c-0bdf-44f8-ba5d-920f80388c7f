<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjXhjhRxgrXjsjMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjXhjhRxgrXjsj">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_XHJH_RXGR_XJSJ-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="JL_XXZJBH" jdbcType="VARCHAR" property="jlXxzjbh" />
    <result column="DJSJ" jdbcType="VARCHAR" property="djsj" />
    <result column="DJRXM" jdbcType="VARCHAR" property="djrxm" />
    <result column="XJSJ_BT" jdbcType="VARCHAR" property="xjsjBt" />
    <result column="XJSJ_NR" jdbcType="VARCHAR" property="xjsjNr" />
    <result column="XJSJ_FJ" jdbcType="VARCHAR" property="xjsjFj" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, JL_XXZJBH, DJSJ, DJRXM, XJSJ_BT, XJSJ_NR, XJSJ_FJ
  </sql>
</mapper>