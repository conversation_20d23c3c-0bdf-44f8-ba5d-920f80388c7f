<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjXldaMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjXlda">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_XLDA-->
    <result column="PXBMC" jdbcType="VARCHAR" property="pxbmc" />
    <result column="KPXMMC" jdbcType="VARCHAR" property="kpxmmc" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="CJ" jdbcType="VARCHAR" property="cj" />
    <result column="PFDF" jdbcType="VARCHAR" property="pfdf" />
    <result column="PXSJ" jdbcType="TIMESTAMP" property="pxsj" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PXBMC, KPXMMC, XM, GMSFHM, JH, CJ, PFDF, PXSJ
  </sql>
</mapper>