<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjYjbbMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjYjbb">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_YJBB-->
    <result column="XXZJBH" jdbcType="VARCHAR" property="xxzjbh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="DWBM" jdbcType="VARCHAR" property="dwbm" />
    <result column="CZD01" jdbcType="VARCHAR" property="czd01" />
    <result column="ZWMC" jdbcType="VARCHAR" property="zwmc" />
    <result column="YJRQ" jdbcType="TIMESTAMP" property="yjrq" />
    <result column="YJDD" jdbcType="VARCHAR" property="yjdd" />
    <result column="YJSY" jdbcType="VARCHAR" property="yjsy" />
    <result column="YYRXM" jdbcType="VARCHAR" property="yyrxm" />
    <result column="CYRS" jdbcType="DECIMAL" property="cyrs" />
    <result column="FKRXM" jdbcType="VARCHAR" property="fkrxm" />
    <result column="CXFS" jdbcType="VARCHAR" property="cxfs" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
    <result column="SHJG" jdbcType="VARCHAR" property="shjg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XXZJBH, XM, GMSFHM, DWMC, DWBM, CZD01, ZWMC, YJRQ, YJDD, YJSY, YYRXM, CYRS, FKRXM, 
    CXFS, BZ, SHJG
  </sql>
</mapper>