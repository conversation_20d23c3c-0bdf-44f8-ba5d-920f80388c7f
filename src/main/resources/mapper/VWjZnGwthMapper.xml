<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjZnGwthMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjZnGwth">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_ZN_GWTH-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_ZN" jdbcType="VARCHAR" property="xmZn" />
    <result column="XM_ZNPO" jdbcType="VARCHAR" property="xmZnpo" />
    <result column="GJ_ZNPO" jdbcType="VARCHAR" property="gjZnpo" />
    <result column="GZDW_ZNPO" jdbcType="VARCHAR" property="gzdwZnpo" />
    <result column="ZW_ZNPO" jdbcType="VARCHAR" property="zwZnpo" />
    <result column="DJRQ" jdbcType="VARCHAR" property="djrq" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_ZN, XM_ZNPO, GJ_ZNPO, GZDW_ZNPO, ZW_ZNPO, DJRQ
  </sql>
</mapper>