<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjZnJsbqyMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjZnJsbqy">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_ZN_JSBQY-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_FR" jdbcType="VARCHAR" property="xmFr" />
    <result column="ZCH" jdbcType="VARCHAR" property="zch" />
    <result column="QYMC" jdbcType="VARCHAR" property="qymc" />
    <result column="CLSJ" jdbcType="VARCHAR" property="clsj" />
    <result column="JYFW" jdbcType="VARCHAR" property="jyfw" />
    <result column="ZCD" jdbcType="VARCHAR" property="zcd" />
    <result column="JYD" jdbcType="VARCHAR" property="jyd" />
    <result column="QYLX" jdbcType="VARCHAR" property="qylx" />
    <result column="QYLXMC" jdbcType="VARCHAR" property="qylxmc" />
    <result column="ZCZB" jdbcType="VARCHAR" property="zczb" />
    <result column="GRCZE" jdbcType="VARCHAR" property="grcze" />
    <result column="GRCZBL" jdbcType="VARCHAR" property="grczbl" />
    <result column="QYZT" jdbcType="VARCHAR" property="qyzt" />
    <result column="QYZTMC" jdbcType="VARCHAR" property="qyztmc" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
    <result column="SFGD" jdbcType="VARCHAR" property="sfgd" />
    <result column="SFGDMC" jdbcType="VARCHAR" property="sfgdmc" />
    <result column="SFDRGJZW" jdbcType="VARCHAR" property="sfdrgjzw" />
    <result column="SFDRGJZWMC" jdbcType="VARCHAR" property="sfdrgjzwmc" />
    <result column="SFFSJJGX" jdbcType="VARCHAR" property="sffsjjgx" />
    <result column="SFFSJJGXMC" jdbcType="VARCHAR" property="sffsjjgxmc" />
    <result column="TZSJ" jdbcType="VARCHAR" property="tzsj" />
    <result column="GJZWMC" jdbcType="VARCHAR" property="gjzwmc" />
    <result column="GJZWSJ" jdbcType="VARCHAR" property="gjzwsj" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_FR, ZCH, QYMC, CLSJ, JYFW, ZCD, JYD, QYLX, QYLXMC, ZCZB, GRCZE, 
    GRCZBL, QYZT, QYZTMC, BZ, SFGD, SFGDMC, SFDRGJZW, SFDRGJZWMC, SFFSJJGX, SFFSJJGXMC, 
    TZSJ, GJZWMC, GJZWSJ
  </sql>
</mapper>