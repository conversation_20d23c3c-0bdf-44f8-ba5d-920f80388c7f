<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjZnTzgqjjMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjZnTzgqjj">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_ZN_TZGQJJ-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_FR" jdbcType="VARCHAR" property="xmFr" />
    <result column="ZJE" jdbcType="VARCHAR" property="zje" />
    <result column="MC" jdbcType="VARCHAR" property="mc" />
    <result column="GRJE" jdbcType="VARCHAR" property="grje" />
    <result column="JJTX" jdbcType="VARCHAR" property="jjtx" />
    <result column="QSRQ" jdbcType="VARCHAR" property="qsrq" />
    <result column="JZRQ" jdbcType="VARCHAR" property="jzrq" />
    <result column="JYFW" jdbcType="VARCHAR" property="jyfw" />
    <result column="MC2" jdbcType="VARCHAR" property="mc2" />
    <result column="RJJE" jdbcType="VARCHAR" property="rjje" />
    <result column="RJBL" jdbcType="VARCHAR" property="rjbl" />
    <result column="RJSJ" jdbcType="VARCHAR" property="rjsj" />
    <result column="SFKZR" jdbcType="VARCHAR" property="sfkzr" />
    <result column="SFKZRMC" jdbcType="VARCHAR" property="sfkzrmc" />
    <result column="SFGD" jdbcType="VARCHAR" property="sfgd" />
    <result column="SFGDMC" jdbcType="VARCHAR" property="sfgdmc" />
    <result column="SFDRGJZW" jdbcType="VARCHAR" property="sfdrgjzw" />
    <result column="SFDRGJZWMC" jdbcType="VARCHAR" property="sfdrgjzwmc" />
    <result column="SFFSJJGX" jdbcType="VARCHAR" property="sffsjjgx" />
    <result column="SFFSJJGXMC" jdbcType="VARCHAR" property="sffsjjgxmc" />
    <result column="TZSJ" jdbcType="VARCHAR" property="tzsj" />
    <result column="GJZWMC" jdbcType="VARCHAR" property="gjzwmc" />
    <result column="GJZWSJ" jdbcType="VARCHAR" property="gjzwsj" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_FR, ZJE, MC, GRJE, JJTX, QSRQ, JZRQ, JYFW, MC2, RJJE, RJBL, RJSJ, 
    SFKZR, SFKZRMC, SFGD, SFGDMC, SFDRGJZW, SFDRGJZWMC, SFFSJJGX, SFFSJJGXMC, TZSJ, GJZWMC, 
    GJZWSJ, BZ
  </sql>
</mapper>