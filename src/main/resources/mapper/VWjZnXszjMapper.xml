<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjZnXszjMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjZnXszj">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_ZN_XSZJ-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_DSR" jdbcType="VARCHAR" property="xmDsr" />
    <result column="BZJSJ" jdbcType="VARCHAR" property="bzjsj" />
    <result column="BZJYY" jdbcType="VARCHAR" property="bzjyy" />
    <result column="CLJG" jdbcType="VARCHAR" property="cljg" />
    <result column="CLJD" jdbcType="VARCHAR" property="cljd" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_DSR, BZJSJ, BZJYY, CLJG, CLJD
  </sql>
</mapper>