<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjZnYjqkMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjZnYjqk">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_ZN_YJQK-->
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="ND" jdbcType="VARCHAR" property="nd" />
    <result column="XM_POZN" jdbcType="VARCHAR" property="xmPozn" />
    <result column="YJGJ" jdbcType="VARCHAR" property="yjgj" />
    <result column="XJZCS" jdbcType="VARCHAR" property="xjzcs" />
    <result column="YJZJHM" jdbcType="VARCHAR" property="yjzjhm" />
    <result column="YJLX" jdbcType="VARCHAR" property="yjlx" />
    <result column="BZ" jdbcType="VARCHAR" property="bz" />
    <result column="YJSJ" jdbcType="VARCHAR" property="yjsj" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    XM, GMSFHM, ND, XM_POZN, YJGJ, XJZCS, YJZJHM, YJLX, BZ, YJSJ
  </sql>
</mapper>