package com.hl.archive;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class DictImport {

    public static void main(String[] args) {


        Set<String> one = new HashSet<>();

        Set<String> two = new HashSet<>();

        Set<String> three = new HashSet<>();


        ExcelReader reader = ExcelUtil.getReader("D:\\work\\code\\hl-wj-police-archive\\sql\\222.xlsx");
        List<List<Object>> read = reader.setSheet(0).read(2);
        for (List<Object> objects : read) {
            one.add((String) objects.get(1));
            two.add((String) objects.get(2));
            two.add((String) objects.get(3));
            System.out.println(objects);
        }




    }
}
