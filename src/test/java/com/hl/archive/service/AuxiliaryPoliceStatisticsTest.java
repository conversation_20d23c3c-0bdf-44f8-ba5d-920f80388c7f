package com.hl.archive.service;

import com.hl.archive.domain.dto.AuxiliaryPoliceStatisticsDTO;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 辅警统计功能测试
 */
@SpringBootTest
public class AuxiliaryPoliceStatisticsTest {

    @Autowired
    private AuxiliaryPoliceInfoService auxiliaryPoliceInfoService;

    @Test
    public void testGetAuxiliaryPoliceStatisticsByDepartment() {
        System.out.println("=== 测试按部门统计辅警信息 ===");
        List<AuxiliaryPoliceStatisticsDTO> statistics = auxiliaryPoliceInfoService.getAuxiliaryPoliceStatisticsByDepartment();
        
        for (AuxiliaryPoliceStatisticsDTO stat : statistics) {
            System.out.println("部门: " + stat.getDepartmentName() + " (" + stat.getDepartment() + ")");
            System.out.println("总人数: " + stat.getTotalCount());
            System.out.println("年龄分布 - 20-30岁: " + stat.getAge20To30() + 
                             ", 30-40岁: " + stat.getAge30To40() + 
                             ", 40-50岁: " + stat.getAge40To50() + 
                             ", 50岁以上: " + stat.getAge50Up());
            System.out.println("学历分布 - 初中及以下: " + stat.getEduJuniorAndBelow() + 
                             ", 中专: " + stat.getEduTechnical() + 
                             ", 高中: " + stat.getEduHighSchool() + 
                             ", 大专: " + stat.getEduCollege() + 
                             ", 本科及以上: " + stat.getEduBachelorAndAbove());
            System.out.println("性别分布 - 男: " + stat.getMaleCount() + ", 女: " + stat.getFemaleCount());
            System.out.println("在岗状态 - 在岗: " + stat.getOnDutyCount() + ", 不在岗: " + stat.getOffDutyCount());
            System.out.println("平均年龄: " + stat.getAvgAge());
            System.out.println("---");
        }
    }

    @Test
    public void testGetAuxiliaryPoliceStatisticsByOrgId() {
        System.out.println("=== 测试按组织ID统计辅警信息 ===");
        
        // 测试全局统计
        StatisticsQueryRequest globalRequest = new StatisticsQueryRequest();
        globalRequest.setOrganizationId("320412000000");
        
        AuxiliaryPoliceStatisticsDTO globalStats = auxiliaryPoliceInfoService.getAuxiliaryPoliceStatisticsByOrgId(globalRequest);
        System.out.println("全局统计:");
        printStatistics(globalStats);
        
        // 测试特定部门统计
        StatisticsQueryRequest deptRequest = new StatisticsQueryRequest();
        deptRequest.setOrganizationId("32041200000001"); // 示例部门ID
        
        AuxiliaryPoliceStatisticsDTO deptStats = auxiliaryPoliceInfoService.getAuxiliaryPoliceStatisticsByOrgId(deptRequest);
        System.out.println("部门统计:");
        printStatistics(deptStats);
    }

    @Test
    public void testAuxiliaryPoliceStatisticsDrillDown() {
        System.out.println("=== 测试辅警统计穿透查询 ===");
        
        // 测试按学历穿透查询
        testDrillDownByEducation();
        
        // 测试按年龄穿透查询
        testDrillDownByAge();
        
        // 测试按性别穿透查询
        testDrillDownByGender();
    }

    private void testDrillDownByEducation() {
        System.out.println("--- 按学历穿透查询 ---");
        
        // 测试初中及以下学历
        StatisticsDrillDownRequest request = new StatisticsDrillDownRequest();
        request.setOrganizationId("320412000000");
        request.setStatisticsType("edu_junior_and_below");
        request.setPage(1);
        request.setLimit(5);
        
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.getAuxiliaryPoliceListByStatisticsType(request);
        System.out.println("初中及以下学历人员数量: " + page.getTotal());
        for (AuxiliaryPoliceInfo info : page.getRecords()) {
            System.out.println("  " + info.getName() + " - " + info.getEducationLevel());
        }
        
        // 测试本科及以上学历
        request.setStatisticsType("edu_bachelor_and_above");
        page = auxiliaryPoliceInfoService.getAuxiliaryPoliceListByStatisticsType(request);
        System.out.println("本科及以上学历人员数量: " + page.getTotal());
        for (AuxiliaryPoliceInfo info : page.getRecords()) {
            System.out.println("  " + info.getName() + " - " + info.getEducationLevel());
        }
    }

    private void testDrillDownByAge() {
        System.out.println("--- 按年龄穿透查询 ---");
        
        StatisticsDrillDownRequest request = new StatisticsDrillDownRequest();
        request.setOrganizationId("320412000000");
        request.setStatisticsType("age_20_30");
        request.setPage(1);
        request.setLimit(5);
        
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.getAuxiliaryPoliceListByStatisticsType(request);
        System.out.println("20-30岁人员数量: " + page.getTotal());
        for (AuxiliaryPoliceInfo info : page.getRecords()) {
            System.out.println("  " + info.getName() + " - " + info.getAge());
        }
    }

    private void testDrillDownByGender() {
        System.out.println("--- 按性别穿透查询 ---");
        
        StatisticsDrillDownRequest request = new StatisticsDrillDownRequest();
        request.setOrganizationId("320412000000");
        request.setStatisticsType("femaleCount");
        request.setPage(1);
        request.setLimit(5);
        
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.getAuxiliaryPoliceListByStatisticsType(request);
        System.out.println("女性人员数量: " + page.getTotal());
        for (AuxiliaryPoliceInfo info : page.getRecords()) {
            System.out.println("  " + info.getName() + " - " + info.getGender());
        }
    }

    private void printStatistics(AuxiliaryPoliceStatisticsDTO stats) {
        System.out.println("部门: " + stats.getDepartmentName() + " (" + stats.getDepartment() + ")");
        System.out.println("总人数: " + stats.getTotalCount());
        System.out.println("年龄分布 - 20-30岁: " + stats.getAge20To30() + 
                         ", 30-40岁: " + stats.getAge30To40() + 
                         ", 40-50岁: " + stats.getAge40To50() + 
                         ", 50岁以上: " + stats.getAge50Up());
        System.out.println("学历分布 - 初中及以下: " + stats.getEduJuniorAndBelow() + 
                         ", 中专: " + stats.getEduTechnical() + 
                         ", 高中: " + stats.getEduHighSchool() + 
                         ", 大专: " + stats.getEduCollege() + 
                         ", 本科及以上: " + stats.getEduBachelorAndAbove());
        System.out.println("性别分布 - 男: " + stats.getMaleCount() + ", 女: " + stats.getFemaleCount());
        System.out.println("在岗状态 - 在岗: " + stats.getOnDutyCount() + ", 不在岗: " + stats.getOffDutyCount());
        System.out.println("平均年龄: " + stats.getAvgAge());
        System.out.println();
    }
}
