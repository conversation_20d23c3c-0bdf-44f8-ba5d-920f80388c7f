package com.hl.archive.service;

import com.hl.archive.domain.dto.MessageAddDTO;
import com.hl.archive.domain.entity.PoliceClubMessage;
import com.hl.archive.mapper.PoliceClubMessageMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 社团留言回复功能测试类
 */
@ExtendWith(MockitoExtension.class)
class PoliceClubMessageServiceReplyTest {

    @Mock
    private PoliceClubMessageMapper policeClubMessageMapper;

    @InjectMocks
    private PoliceClubMessageService policeClubMessageService;

    @Test
    void testReplyMessage_Success() {
        // 准备测试数据
        MessageAddDTO dto = new MessageAddDTO();
        dto.setClubId(1L);
        dto.setParentId(2L);
        dto.setContent("这是一条回复");
        dto.setAuthorIdCard("110101199001011234");
        dto.setReplyToIdCard("110101199001011235");
        dto.setMessageType(1);

        // 模拟父留言
        PoliceClubMessage parentMessage = new PoliceClubMessage();
        parentMessage.setId(2L);
        parentMessage.setRootId(1L);

        // 模拟返回的留言详情
        PoliceClubMessage expectedResult = new PoliceClubMessage();
        expectedResult.setId(3L);
        expectedResult.setClubId(1L);
        expectedResult.setParentId(2L);
        expectedResult.setRootId(1L);
        expectedResult.setContent("这是一条回复");
        expectedResult.setAuthorIdCard("110101199001011234");
        expectedResult.setReplyToIdCard("110101199001011235");
        expectedResult.setMessageType(1);
        expectedResult.setLikeCount(0);
        expectedResult.setReplyCount(0);
        expectedResult.setStatus(1);

        // Mock方法调用
        when(policeClubMessageMapper.selectById(2L)).thenReturn(parentMessage);
        when(policeClubMessageMapper.insert(any(PoliceClubMessage.class))).thenReturn(1);
        when(policeClubMessageMapper.selectMessageDetail(anyLong(), anyString())).thenReturn(expectedResult);

        // 执行测试
        PoliceClubMessage result = policeClubMessageService.replyMessage(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(3L, result.getId());
        assertEquals(1L, result.getClubId());
        assertEquals(2L, result.getParentId());
        assertEquals(1L, result.getRootId());
        assertEquals("这是一条回复", result.getContent());
        assertEquals("110101199001011234", result.getAuthorIdCard());
        assertEquals("110101199001011235", result.getReplyToIdCard());
        assertEquals(1, result.getMessageType());
        assertEquals(0, result.getLikeCount());
        assertEquals(0, result.getReplyCount());
        assertEquals(1, result.getStatus());
    }

    @Test
    void testReplyMessage_InvalidParentId() {
        // 准备测试数据 - 无效的父留言ID
        MessageAddDTO dto = new MessageAddDTO();
        dto.setClubId(1L);
        dto.setParentId(null); // 无效的父留言ID
        dto.setContent("这是一条回复");
        dto.setAuthorIdCard("110101199001011234");

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> policeClubMessageService.replyMessage(dto)
        );

        assertEquals("回复留言时父留言ID不能为空或0", exception.getMessage());
    }

    @Test
    void testReplyMessage_ZeroParentId() {
        // 准备测试数据 - 父留言ID为0
        MessageAddDTO dto = new MessageAddDTO();
        dto.setClubId(1L);
        dto.setParentId(0L); // 无效的父留言ID
        dto.setContent("这是一条回复");
        dto.setAuthorIdCard("110101199001011234");

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> policeClubMessageService.replyMessage(dto)
        );

        assertEquals("回复留言时父留言ID不能为空或0", exception.getMessage());
    }
}
