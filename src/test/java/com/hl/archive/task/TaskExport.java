package com.hl.archive.task;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class TaskExport {
    public static void main(String[] args) throws Exception {
        // 1. 初始化数据源（你可以换成自己的）
        DataSource ds = new SimpleDataSource("***************************************", "hl", "hl1234!@#$");

        // 2. 多个表名
        String outputFile = taskConfig(ds);
        taskForm( ds);
        taskListItem(ds);
        taskNodeProcess(ds);
        taskNodeForm(ds);
        System.out.println("SQL 导出完成，文件路径: " + outputFile);
    }

    private static String taskNodeProcess(DataSource ds) throws SQLException {
        String table = "task_node_process";

        String querySql = "SELECT * from task_node_process WHERE config_id  in (select config_id from task_config WHERE config_uuid = '" + "CKSWWQQWW7H" + "')";
        // 3. SQL 文件保存路径
        String outputFile = "task_node_process.sql";
        writeSql(ds, querySql, table, outputFile);
        return outputFile;
    }

    private static String taskNodeForm(DataSource ds) throws SQLException {
        String table = "task_node_form";

        String querySql = "SELECT * FROM `task_node_form` WHERE form_id in  (select form_id from  task_list_item WHERE config_uuid = '" + "CKSWWQQWW7H" + "')";
        // 3. SQL 文件保存路径
        String outputFile = "task_node_form.sql";
        writeSql(ds, querySql, table, outputFile);
        return outputFile;
    }

    private static String taskForm(DataSource ds) throws SQLException {
        String table = "task_form";

        String querySql = "SELECT * FROM `task_form` WHERE form_id in (select form_id from  task_list_item WHERE config_uuid = '" + "CKSWWQQWW7H" + "')";
        // 3. SQL 文件保存路径
        String outputFile = "task_form.sql";
        writeSql(ds, querySql, table, outputFile);
        return outputFile;
    }

    private static String taskListItem(DataSource ds) throws SQLException {
        String table = "task_list_item";

        String querySql = "select * from task_list_item where config_uuid = '" + "CKSWWQQWW7H" + "'";
        // 3. SQL 文件保存路径
        String outputFile = "task_list_item.sql";
        writeSql(ds, querySql, table, outputFile);
        return outputFile;
    }

    private static String taskConfig(DataSource ds) throws SQLException {
        String table = "task_config";

        String querySql = "select * from task_config where config_uuid = '" + "CKSWWQQWW7H" + "'";
        // 3. SQL 文件保存路径
        String outputFile = "task_config.sql";
        writeSql(ds, querySql, table, outputFile);
        return outputFile;
    }

    private static void writeSql(DataSource ds, String querySql, String table, String outputFile) throws SQLException {
        StringBuilder allSql = new StringBuilder();
        List<Entity> rows = Db.use(ds).query(querySql);
        for (Entity row : rows) {
            List<String> columns = new ArrayList<>(row.getFieldNames());
            List<String> values = columns.stream().map(col -> toSqlValue(row.get(col))).collect(Collectors.toList());

            String sql = StrUtil.format("INSERT INTO {} ({}) VALUES ({});",
                    table,
                    StrUtil.join(", ", columns),
                    StrUtil.join(", ", values)
            );
            allSql.append(sql).append("\n");
        }
        // 4. 写入 SQL 文件
        FileUtil.writeUtf8String(allSql.toString(), outputFile);
    }

    private static String toSqlValue(Object val) {
        if (val == null) {
            return "NULL";
        }
        if (val instanceof Number) {
            return val.toString();
        }
        String str = val.toString().replace("'", "''");
        return "'" + str + "'";
    }
}
